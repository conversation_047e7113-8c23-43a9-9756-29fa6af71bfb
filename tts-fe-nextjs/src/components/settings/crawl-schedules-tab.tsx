'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScheduleForm } from '@/components/settings/schedule-form';
import { ScheduleList } from '@/components/settings/schedule-list';
import { useCrawlSchedules } from '@/lib/hooks/use-crawl-schedules-query';
import { CrawlSchedule } from '@/types/crawl-schedule';
import { Plus, Calendar, Info } from 'lucide-react';

export function CrawlSchedulesTab() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [scheduleToEdit, setScheduleToEdit] = useState<CrawlSchedule | null>(null);

  // Fetch crawl schedules
  const {
    data: schedules,
    isLoading: isLoadingSchedules,
    refetch: refetchSchedules
  } = useCrawlSchedules();

  // Handle create schedule
  const handleCreateSchedule = () => {
    setIsCreateDialogOpen(true);
  };

  // Handle edit schedule
  const handleEditSchedule = (schedule: CrawlSchedule) => {
    setScheduleToEdit(schedule);
    setIsEditDialogOpen(true);
  };

  // Handle form success
  const handleFormSuccess = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setScheduleToEdit(null);
    refetchSchedules();
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setScheduleToEdit(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Crawl Schedules
              </CardTitle>
              <CardDescription>
                Manage automated crawling schedules for product discovery across marketplaces.
              </CardDescription>
            </div>
            <Button onClick={handleCreateSchedule}>
              <Plus className="mr-2 h-4 w-4" />
              Create Schedule
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Info Card */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="space-y-1">
                <h4 className="text-sm font-medium text-blue-900">
                  How Crawl Schedules Work
                </h4>
                <p className="text-sm text-blue-700">
                  Create schedules to automatically search for products on Etsy, eBay, and Amazon. 
                  The Chrome extension will process these schedules when you start automated crawling.
                </p>
                <ul className="text-sm text-blue-700 list-disc list-inside space-y-1 mt-2">
                  <li>Schedules run sequentially to avoid detection</li>
                  <li>Set keywords and maximum products per run</li>
                  <li>Monitor progress and results in real-time</li>
                  <li>Toggle schedules on/off as needed</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Schedule List */}
          <ScheduleList
            schedules={schedules || []}
            isLoading={isLoadingSchedules}
            onEdit={handleEditSchedule}
            onRefresh={refetchSchedules}
          />
        </CardContent>
      </Card>

      {/* Create Schedule Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Crawl Schedule</DialogTitle>
            <DialogDescription>
              Set up a new automated crawling schedule for product discovery.
            </DialogDescription>
          </DialogHeader>
          <ScheduleForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Schedule Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Crawl Schedule</DialogTitle>
            <DialogDescription>
              Update the settings for this crawl schedule.
            </DialogDescription>
          </DialogHeader>
          {scheduleToEdit && (
            <ScheduleForm
              schedule={scheduleToEdit}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
