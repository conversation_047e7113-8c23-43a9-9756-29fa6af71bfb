'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useCreateCrawlSchedule, useUpdateCrawlSchedule } from '@/lib/hooks/use-crawl-schedules-query';
import {
  CrawlSchedule,
  ScheduleFormData,
  FREQUENCY_OPTIONS,
  MARKETPLACE_OPTIONS,
} from '@/types/crawl-schedule';
import { Loader2 } from 'lucide-react';

// Form validation schema
const scheduleFormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters').max(255, 'Name must be less than 255 characters'),
  keywords: z.string().min(3, 'Keywords must be at least 3 characters').max(500, 'Keywords must be less than 500 characters'),
  marketplace: z.enum(['etsy', 'ebay', 'amazon'], {
    required_error: 'Please select a marketplace',
  }),
  frequencyMinutes: z.number().min(60, 'Minimum frequency is 1 hour').max(10080, 'Maximum frequency is 1 week'),
  maxProductsPerRun: z.number().min(1, 'Minimum 1 product').max(1000, 'Maximum 1000 products'),
  isActive: z.boolean(),
});

interface ScheduleFormProps {
  schedule?: CrawlSchedule;
  onSuccess: () => void;
  onCancel: () => void;
}

export function ScheduleForm({ schedule, onSuccess, onCancel }: ScheduleFormProps) {
  const isEditing = !!schedule;
  
  // Mutations
  const createMutation = useCreateCrawlSchedule();
  const updateMutation = useUpdateCrawlSchedule();

  // Form setup
  const form = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      name: schedule?.name || '',
      keywords: schedule?.keywords || '',
      marketplace: schedule?.marketplace || 'etsy',
      frequencyMinutes: schedule?.frequencyMinutes || 1440, // Default to daily
      maxProductsPerRun: schedule?.maxProductsPerRun || 20,
      isActive: schedule?.isActive ?? true,
    },
  });

  // Handle form submission
  const onSubmit = async (data: ScheduleFormData) => {
    try {
      if (isEditing && schedule) {
        await updateMutation.mutateAsync({
          id: schedule.id,
          data: {
            name: data.name,
            keywords: data.keywords,
            marketplace: data.marketplace,
            frequencyMinutes: data.frequencyMinutes,
            maxProductsPerRun: data.maxProductsPerRun,
            isActive: data.isActive,
          },
        });
      } else {
        await createMutation.mutateAsync({
          name: data.name,
          keywords: data.keywords,
          marketplace: data.marketplace,
          frequencyMinutes: data.frequencyMinutes,
          maxProductsPerRun: data.maxProductsPerRun,
          isActive: data.isActive,
        });
      }
      onSuccess();
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Form submission error:', error);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Schedule Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Schedule Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g., Vintage T-Shirts Daily Crawl"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  A descriptive name for this crawl schedule
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Keywords */}
          <FormField
            control={form.control}
            name="keywords"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Search Keywords</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="e.g., vintage t-shirt, retro clothing, cotton tee"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Keywords to search for (comma-separated for multiple terms)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Marketplace */}
          <FormField
            control={form.control}
            name="marketplace"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Marketplace</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select marketplace" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {MARKETPLACE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Target marketplace for crawling
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Frequency */}
          <FormField
            control={form.control}
            name="frequencyMinutes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Crawl Frequency</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(parseInt(value))}
                  defaultValue={field.value.toString()}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {FREQUENCY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  How often to run this crawl schedule
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Max Products */}
          <FormField
            control={form.control}
            name="maxProductsPerRun"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Products per Keyword</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    max="1000"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  Maximum products to crawl in each run (1-1000)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Active Status */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    Active Schedule
                  </FormLabel>
                  <FormDescription>
                    Enable this schedule for automated crawling
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? 'Update Schedule' : 'Create Schedule'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
