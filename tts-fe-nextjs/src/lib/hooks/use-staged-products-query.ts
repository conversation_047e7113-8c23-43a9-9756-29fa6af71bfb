'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import {
  getStagedProducts,
  getStagedProduct,
  createStagedProduct,
  updateStagedProduct,
  deleteStagedProduct,
  getStagedProductUploads,
  createProductUpload
} from '@/lib/api/services';
import {
  StagedProductCreateInput,
  StagedProductUpdateInput,
  StagedProductFilters,
  ProductUploadCreateInput,
  ProductUploadFilters
} from '@/types/staged-product';
import { globalJobMonitor } from '@/lib/services/global-job-monitor';
import { useBrowserNotifications } from '@/lib/hooks/use-browser-notifications';

// Create query keys for staged products
export const stagedProductKeys = {
  all: ['stagedProducts'] as const,
  lists: () => [...stagedProductKeys.all, 'list'] as const,
  list: (filters?: StagedProductFilters) => [...stagedProductKeys.lists(), { filters }] as const,
  details: () => [...stagedProductKeys.all, 'detail'] as const,
  detail: (id: number) => [...stagedProductKeys.details(), id] as const,
  uploads: (id: number) => [...stagedProductKeys.detail(id), 'uploads'] as const,
};

/**
 * Hook to fetch staged products with optional filters
 */
export function useStagedProducts(filters?: StagedProductFilters) {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: stagedProductKeys.list(filters),
    queryFn: () => getStagedProducts(filters, session?.backendToken),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch a single staged product by ID
 */
export function useStagedProduct(id: number) {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: stagedProductKeys.detail(id),
    queryFn: () => getStagedProduct(id, session?.backendToken),
    enabled: isAuthenticated && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch upload history for a staged product
 */
export function useStagedProductUploads(stagedProductId: number, filters?: ProductUploadFilters) {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: stagedProductKeys.uploads(stagedProductId),
    queryFn: () => getStagedProductUploads(stagedProductId, filters, session?.backendToken),
    enabled: isAuthenticated && !!stagedProductId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for creating a new staged product
 */
export function useCreateStagedProduct() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: StagedProductCreateInput) =>
      createStagedProduct(data, session?.backendToken),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });
      toast.success('Product created successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create product: ${error.message}`);
    }
  });
}

/**
 * Hook for updating an existing staged product
 */
export function useUpdateStagedProduct(id: number) {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: StagedProductUpdateInput) =>
      updateStagedProduct(id, data, session?.backendToken),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });
      toast.success('Product updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update product: ${error.message}`);
    }
  });
}

/**
 * Hook for deleting a staged product with optimistic updates
 */
export function useDeleteStagedProduct() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteStagedProduct(id, session?.backendToken),

    // Optimistic update
    onMutate: async (deletedProductId) => {
      // Cancel any outgoing refetches to avoid overwriting our optimistic update
      await queryClient.cancelQueries({ queryKey: stagedProductKeys.lists() });

      // Snapshot the previous value
      const previousProducts = queryClient.getQueryData(stagedProductKeys.lists());

      // Optimistically update the cache by removing the deleted product
      queryClient.setQueryData(
        stagedProductKeys.lists(),
        (old: any) => {
          if (!old) return old;
          return {
            ...old,
            data: old.data.filter((productWithUpload: any) => productWithUpload.product.id !== deletedProductId)
          };
        }
      );

      // Return a context object with the snapshot
      return { previousProducts };
    },

    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (error: Error, _variables, context) => {
      if (context) {
        queryClient.setQueryData(stagedProductKeys.lists(), context.previousProducts);
      }
      toast.error(`Failed to delete product: ${error.message}`);
    },

    // Always refetch after error or success to ensure cache is in sync with server
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });
    },

    onSuccess: () => {
      toast.success('Product deleted successfully');
    }
  });
}

/**
 * Hook for uploading a product to TikTok shop with optimistic updates and global job monitoring
 */
export function useUploadToShop() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const { requestPermission } = useBrowserNotifications();

  return useMutation({
    mutationFn: (data: ProductUploadCreateInput) =>
      createProductUpload(data, session?.backendToken),

    // Optimistic update
    onMutate: async (variables) => {
      // Request notification permission if not already granted
      await requestPermission();

      // Cancel any outgoing refetches to avoid overwriting our optimistic update
      await queryClient.cancelQueries({
        queryKey: stagedProductKeys.lists()
      });

      // Snapshot the previous values
      const previousProducts = queryClient.getQueryData(stagedProductKeys.lists());

      // Optimistically update the cache to show "PENDING" status
      queryClient.setQueryData(
        stagedProductKeys.lists(),
        (old: any) => {
          if (!old) return old;

          return {
            ...old,
            data: old.data.map((productWithUpload: any) => {
              if (productWithUpload.product.id === variables.stagedProductId) {
                // Create optimistic latest upload for the selected shop
                const optimisticLatestUpload = {
                  id: -1, // Temporary ID
                  status: 'PENDING',
                  tiktokShopId: variables.tiktokShopId, // Use selected shop ID
                  createdAt: new Date().toISOString()
                };

                return {
                  ...productWithUpload,
                  latestUpload: optimisticLatestUpload
                };
              }
              return productWithUpload;
            })
          };
        }
      );

      // Return a context object with the snapshot
      return { previousProducts };
    },

    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (error: Error, _variables, context) => {
      if (context) {
        queryClient.setQueryData(stagedProductKeys.lists(), context.previousProducts);
      }
      toast.error(`Failed to add product to upload queue: ${error.message}`);
    },

    // Always refetch after error or success to ensure cache is in sync with server
    onSettled: (_, __, variables) => {
      queryClient.invalidateQueries({
        queryKey: stagedProductKeys.uploads(variables.stagedProductId)
      });
      queryClient.invalidateQueries({
        queryKey: stagedProductKeys.detail(variables.stagedProductId)
      });
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });
    },

    onSuccess: (response) => {
      // Show immediate success toast
      toast.success('Product added to upload queue');

      // Add job to global monitor for cross-page notifications
      if (response.jobId) {
        // Use product and shop info from backend response for better notifications
        const productName = response.stagedProductTitle || 'Unknown Product';
        const shopName = response.shopFriendlyName || 'TikTok Shop';

        globalJobMonitor.addJob({
          id: response.jobId,
          uploadId: response.id,
          type: 'product-upload',
          productName,
          shopName,
          startTime: new Date(),
          userId: session?.user?.id,
        });
      }
    }
  });
}
