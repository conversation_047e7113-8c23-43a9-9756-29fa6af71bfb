# Orders Module Implementation Plan

## Overview

This document outlines the comprehensive implementation plan for the orders module in the TikTok Shop backend system. The module follows the existing products module patterns and focuses solely on synchronizing order data from TikTok Shop API to our internal database.

## Module Purpose

The orders module's **sole purpose** is to synchronize order information from TikTok SDK to our system. It does NOT implement create, update, or delete order functionality - only synchronization endpoints.

## Data Storage Strategy

### Core Principles
1. **TikTok ID Naming**: Use `idTT` as the column name for TikTok Shop order ID (not `id`)
2. **JSONB Storage**: Store complex nested data (`payment`, `recipientAddress`, raw response) as JSONB
3. **Separate Line Items**: Create dedicated `order_line_items` table for important order item data
4. **Raw Data Preservation**: Store complete TikTok response for future extensibility
5. **Independent Line Items**: Line items are independent initially (future: sync with Product/SKU entities)

### Synchronization Scope
- **All Orders**: Sync ALL orders from TikTok Shop
- **Historical Data**: Sync all historical data accessible via TikTok Shop API
- **Product Independence**: If products don't exist, future phase will fetch from TikTok SDK
- **Status Management**: Follow TikTok Shop status only (no internal status field)

## Module Structure

```
src/orders/
├── entities/
│   ├── order.entity.ts
│   └── order-line-item.entity.ts
├── dto/
│   ├── filter-order.dto.ts
│   ├── order-query.dto.ts
│   └── order-sync.dto.ts
├── mappers/
│   └── tiktok-order.mapper.ts
├── orders.controller.ts
├── orders.service.ts
└── orders.module.ts
```

## Entity Design

### Order Entity (`order.entity.ts`)

```typescript
@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, unique: true })
  idTT: string; // TikTok Shop order ID

  @Column({ type: 'enum', enum: OrderStatus })
  status: OrderStatus;

  @Column({ nullable: true })
  orderType: string;

  @Column({ nullable: true })
  createTimeTT: number; // TikTok timestamp

  @Column({ nullable: true })
  updateTimeTT: number; // TikTok timestamp

  @Column({ nullable: true })
  paidTime: number;

  @Column({ nullable: true })
  buyerEmail: string;

  @Column('text', { nullable: true })
  buyerMessage: string;

  @Column({ nullable: true })
  userIdTT: string; // TikTok buyer user ID

  @Column('jsonb', { nullable: true })
  payment: PaymentInfo;

  @Column({ nullable: true })
  paymentMethodName: string;

  @Column('jsonb', { nullable: true })
  recipientAddress: RecipientAddress;

  @Column({ nullable: true })
  fulfillmentType: string;

  @Column({ nullable: true })
  shippingProvider: string;

  @Column({ nullable: true })
  shippingProviderId: string;

  @Column({ nullable: true })
  shippingType: string;

  @Column({ nullable: true })
  trackingNumber: string;

  @Column({ nullable: true })
  deliveryOptionId: string;

  @Column({ nullable: true })
  deliveryOptionName: string;

  @Column({ default: false })
  isCod: boolean;

  @Column({ default: false })
  isExchangeOrder: boolean;

  @Column({ default: false })
  isReplacementOrder: boolean;

  @Column({ default: false })
  isSampleOrder: boolean;

  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any; // Complete TikTok response

  @OneToMany(() => OrderLineItem, (lineItem) => lineItem.order, { cascade: true })
  lineItems: OrderLineItem[];

  @Column()
  tiktokShopId: number;

  @ManyToOne(() => TikTokShop, (ttShop) => ttShop.orders, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tiktokShopId' })
  tiktokShop: TikTokShop;

  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### Order Line Item Entity (`order-line-item.entity.ts`)

```typescript
@Entity('order_line_items')
export class OrderLineItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  idTT: string; // TikTok line item ID

  @Column({ nullable: true })
  productIdTT: string; // TikTok product ID

  @Column({ nullable: true })
  skuIdTT: string; // TikTok SKU ID

  @Column({ nullable: true })
  sellerSku: string;

  @Column({ nullable: true })
  productName: string;

  @Column({ nullable: true })
  skuName: string;

  @Column({ nullable: true })
  skuImage: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  originalPrice: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  salePrice: number;

  @Column({ nullable: true })
  currency: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  platformDiscount: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  sellerDiscount: number;

  @Column({ nullable: true })
  packageId: string;

  @Column({ nullable: true })
  packageStatus: string;

  @Column({ nullable: true })
  displayStatus: string;

  @Column({ default: false })
  isDangerousGood: boolean;

  @Column({ default: false })
  isGift: boolean;

  @Column('jsonb', { nullable: true })
  itemTax: any[];

  @Column('jsonb', { nullable: true })
  combinedListingSkus: any[];

  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any; // Complete line item response

  @ManyToOne(() => Order, (order) => order.lineItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @Column()
  orderId: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## Required Endpoints

### 1. Synchronize Order List
- **Endpoint**: `POST /orders/synchronize`
- **Purpose**: Fetch and sync order list from TikTok Shop
- **Parameters**: TikTok Shop ID, pagination, filters
- **Response**: Sync summary (created, updated, errors)

### 2. Synchronize Order Details
- **Endpoint**: `POST /orders/synchronize-details`
- **Purpose**: Fetch detailed order information for specific orders
- **Parameters**: TikTok Shop ID, order IDs array
- **Response**: Detailed sync summary

## Implementation Components

### DTOs
- `FilterOrderDto`: Query filters for order synchronization
- `OrderQueryDto`: Pagination and search parameters
- `OrderSyncDto`: Sync operation parameters and results

### Mapper
- `TikTokOrderMapper`: Transform TikTok API responses to internal DTOs
- Handle both camelCase and snake_case property names
- Map nested objects to JSONB fields

### Service Methods
- `synchronizeOrders()`: Sync order list from TikTok Shop
- `synchronizeOrderDetails()`: Sync detailed order information
- `findAll()`: Query orders with pagination and filters
- `findOne()`: Get single order by ID

### Error Handling
- Transaction support for data consistency
- Comprehensive error logging
- Graceful handling of API failures
- Duplicate order prevention

## Database Considerations

### Indexes
```sql
CREATE INDEX idx_orders_id_tt ON orders(id_tt);
CREATE INDEX idx_orders_tiktok_shop_id ON orders(tiktok_shop_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_create_time_tt ON orders(create_time_tt);
CREATE INDEX idx_order_line_items_order_id ON order_line_items(order_id);
CREATE INDEX idx_order_line_items_product_id_tt ON order_line_items(product_id_tt);
```

### Constraints
- Unique constraint on `idTT` for orders
- Foreign key constraints for relationships
- Check constraints for enum values

## Integration Points

### TikTok Shop Service
- Utilize existing `TikTokClientFactory`
- Follow authentication patterns from products module
- Implement rate limiting and retry logic

### Queue Integration
- Optional: Background processing for large sync operations
- Follow existing queue patterns from products module

## Testing Strategy

### Unit Tests
- Service method testing
- Mapper transformation testing
- Error handling validation

### Integration Tests
- TikTok API integration testing
- Database transaction testing
- End-to-end sync workflow testing

## Future Enhancements

### Phase 2: Product Integration
- Link order line items to internal Product/SKU entities
- Automatic product sync for missing products
- Cross-reference validation

### Phase 3: Advanced Features
- Real-time order status updates
- Order analytics and reporting
- Automated fulfillment workflows

## Migration Strategy

### Database Migration
```typescript
// Create orders table
// Create order_line_items table
// Add foreign key constraints
// Create indexes
```

### Data Migration
- Initial sync of historical orders
- Incremental sync strategy
- Data validation and cleanup

## Monitoring and Logging

### Metrics
- Sync operation success/failure rates
- API response times
- Data consistency checks

### Logging
- Detailed sync operation logs
- Error tracking and alerting
- Performance monitoring

## Security Considerations

### Data Protection
- Sensitive customer data handling
- PII anonymization where required
- Secure API token management

### Access Control
- User-based order access restrictions
- TikTok Shop association validation
- Admin-only sync operations

## Detailed Implementation Specifications

### Order Status Enum
```typescript
export enum OrderStatus {
  UNPAID = 'UNPAID',
  ON_HOLD = 'ON_HOLD',
  AWAITING_SHIPMENT = 'AWAITING_SHIPMENT',
  PARTIALLY_SHIPPING = 'PARTIALLY_SHIPPING',
  AWAITING_COLLECTION = 'AWAITING_COLLECTION',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}
```

### JSONB Type Definitions
```typescript
interface PaymentInfo {
  totalAmount?: string;
  subTotal?: string;
  currency?: string;
  originalTotalProductPrice?: string;
  tax?: string;
  shippingFee?: string;
  platformDiscount?: string;
  sellerDiscount?: string;
  shippingFeeTax?: string;
  productTax?: string;
  retailDeliveryFee?: string;
  buyerServiceFee?: string;
  handlingFee?: string;
  itemInsuranceFee?: string;
  shippingInsuranceFee?: string;
  smallOrderFee?: string;
  originalShippingFee?: string;
  shippingFeePlatformDiscount?: string;
  shippingFeeSellerDiscount?: string;
  shippingFeeCofundedDiscount?: string;
}

interface RecipientAddress {
  name?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  addressDetail?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressLine3?: string;
  addressLine4?: string;
  postalCode?: string;
  regionCode?: string;
  fullAddress?: string;
  districtInfo?: any[];
  deliveryPreferences?: any;
  firstNameLocalScript?: string;
  lastNameLocalScript?: string;
}
```

### Service Implementation Pattern
```typescript
@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(OrderLineItem)
    private orderLineItemRepository: Repository<OrderLineItem>,
    private readonly clientFactory: TikTokClientFactory,
    private readonly dataSource: DataSource,
    private readonly tikTokOrderMapper: TikTokOrderMapper,
    private readonly tikTokShopService: TikTokShopService,
  ) {}

  async synchronizeOrders(tiktokShopId: number, filters?: FilterOrderDto): Promise<SyncResult> {
    // Implementation following products module pattern
  }

  async synchronizeOrderDetails(tiktokShopId: number, orderIds: string[]): Promise<SyncResult> {
    // Implementation following products module pattern
  }
}
```

### Controller Implementation Pattern
```typescript
@Controller('orders')
@ApiTags('Orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post('synchronize')
  @ApiOperation({ summary: 'Synchronize orders from TikTok Shop' })
  async synchronizeOrders(@Body() syncDto: OrderSyncDto) {
    // Implementation following products module pattern
  }

  @Post('synchronize-details')
  @ApiOperation({ summary: 'Synchronize order details from TikTok Shop' })
  async synchronizeOrderDetails(@Body() detailSyncDto: OrderDetailSyncDto) {
    // Implementation following products module pattern
  }

  @Get()
  @ApiOperation({ summary: 'Get orders with pagination and filters' })
  async findAll(@Query() query: OrderQueryDto) {
    // Implementation following products module pattern
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order by ID' })
  async findOne(@Param('id') id: string) {
    // Implementation following products module pattern
  }
}
```

### Mapper Implementation Pattern
```typescript
@Injectable()
export class TikTokOrderMapper {
  mapToOrderDto(tikTokOrder: any): CreateOrderDto {
    // Transform TikTok API response to internal DTO
    // Handle property name conversions (snake_case to camelCase)
    // Map nested objects to JSONB fields
  }

  mapToLineItemDto(tikTokLineItem: any): CreateOrderLineItemDto {
    // Transform TikTok line item to internal DTO
  }

  private getPropertyValue(obj: any, ...propertyNames: string[]): any {
    // Utility method for handling different property naming conventions
  }
}
```

### Module Configuration
```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      OrderLineItem,
      TikTokShop,
      User,
    ]),
    TiktokShopModule,
    AuthModule,
  ],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    TikTokOrderMapper,
  ],
  exports: [OrdersService],
})
export class OrdersModule {}
```

## API Endpoint Specifications

### Synchronize Orders Endpoint
```
POST /orders/synchronize
Content-Type: application/json
Authorization: Bearer <token>

Request Body:
{
  "tiktokShopId": 1,
  "pageSize": 50,
  "sortOrder": "DESC",
  "sortField": "create_time",
  "filters": {
    "status": ["AWAITING_SHIPMENT", "IN_TRANSIT"],
    "createTimeFrom": **********,
    "createTimeTo": **********
  }
}

Response:
{
  "success": true,
  "data": {
    "totalProcessed": 150,
    "created": 45,
    "updated": 105,
    "errors": 0,
    "syncedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Synchronize Order Details Endpoint
```
POST /orders/synchronize-details
Content-Type: application/json
Authorization: Bearer <token>

Request Body:
{
  "tiktokShopId": 1,
  "orderIds": ["**********", "**********", "**********"]
}

Response:
{
  "success": true,
  "data": {
    "totalProcessed": 3,
    "updated": 3,
    "errors": 0,
    "details": [
      {
        "orderId": "**********",
        "status": "updated",
        "lineItemsCount": 2
      }
    ]
  }
}
```

## Error Handling Strategy

### Transaction Management
- Use `executeInTransaction` utility from products module
- Rollback on any error during sync operation
- Maintain data consistency across order and line item creation

### Error Types and Handling
```typescript
// API Errors
catch (error) {
  if (error.response?.status === 401) {
    throw new UnauthorizedException('TikTok Shop authentication failed');
  }
  if (error.response?.status === 429) {
    // Implement retry logic with exponential backoff
  }
  // Log and handle other API errors
}

// Data Validation Errors
if (!tikTokOrder.id) {
  this.logger.warn(`Order missing ID, skipping: ${JSON.stringify(tikTokOrder)}`);
  continue;
}

// Database Errors
catch (error) {
  if (error.code === '23505') { // Unique constraint violation
    this.logger.log(`Order ${tikTokOrder.id} already exists, updating...`);
    // Handle update logic
  }
}
```

## Performance Considerations

### Batch Processing
- Process orders in batches of 50 (TikTok API limit)
- Implement pagination for large datasets
- Use bulk insert/update operations where possible

### Caching Strategy
- Cache TikTok Shop authentication tokens
- Implement request deduplication
- Cache frequently accessed order data

### Database Optimization
- Use appropriate indexes for query performance
- Implement connection pooling
- Monitor query performance and optimize as needed

## Deployment Checklist

- [ ] Database migrations executed
- [ ] Environment variables configured
- [ ] TikTok API credentials validated
- [ ] Monitoring and alerting setup
- [ ] Documentation updated
- [ ] Testing completed
- [ ] Performance benchmarks established
- [ ] Error handling tested
- [ ] Transaction rollback verified
- [ ] API rate limiting configured
