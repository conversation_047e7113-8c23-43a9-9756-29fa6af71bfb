import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuid } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import * as fileType from 'file-type';

@Injectable()
export class CloudStorageService {
  private readonly logger = new Logger(CloudStorageService.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly publicUrl: string;
  private readonly endpoint: string;

  constructor(private configService: ConfigService) {
    const accountId = this.configService.get<string>('R2_ACCOUNT_ID');
    const accessKeyId = this.configService.get<string>('R2_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.get<string>(
      'R2_SECRET_ACCESS_KEY',
    );
    const bucketName = this.configService.get<string>('R2_BUCKET_NAME');
    const publicUrl = this.configService.get<string>('R2_PUBLIC_URL');

    // Ensure required config values are present
    if (!bucketName) {
      throw new Error(
        'R2_BUCKET_NAME is required but not provided in configuration',
      );
    }

    if (!publicUrl) {
      throw new Error(
        'R2_PUBLIC_URL is required but not provided in configuration',
      );
    }

    if (!accessKeyId || !secretAccessKey) {
      throw new Error(
        'R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY are required but not provided in configuration',
      );
    }

    this.bucketName = bucketName;
    this.publicUrl = publicUrl;
    this.endpoint =
      this.configService.get<string>('R2_ENDPOINT') ||
      `https://${accountId}.r2.cloudflarestorage.com`;

    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: this.endpoint,
      credentials: {
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
      },
    });

    this.logger.log(
      `CloudStorageService initialized with bucket: ${this.bucketName}`,
    );
  }

  /**
   * Upload a file to R2 storage
   * @param fileBuffer The file buffer to upload
   * @param key Optional key (filename) for the file, if not provided a UUID will be generated
   * @param contentType The content type of the file
   * @returns Object containing the URL and key of the uploaded file
   */
  async uploadFile(
    fileBuffer: Buffer,
    key?: string,
    contentType = 'application/octet-stream',
  ): Promise<{ url: string; key: string }> {
    try {
      // Generate a key if not provided
      const fileKey = key || `uploads/${uuid()}`;

      // Upload the file to R2
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
        Body: fileBuffer,
        ContentType: contentType,
      });

      await this.s3Client.send(command);
      this.logger.debug(`Uploaded file to R2: ${fileKey}`);

      // Return the public URL and key
      return {
        url: `${this.publicUrl}/${fileKey}`,
        key: fileKey,
      };
    } catch (error) {
      this.logger.error(
        `Error uploading file to R2: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to upload file to R2: ${error.message}`);
    }
  }

  /**
   * Upload a file from a local path to R2 storage
   * @param filePath Path to the local file
   * @param key Optional key (filename) for the file, if not provided a UUID will be generated
   * @param contentType The content type of the file (optional, will be detected if not provided)
   * @returns Object containing the URL and key of the uploaded file
   */
  async uploadFileFromPath(
    filePath: string,
    key?: string,
    contentType?: string,
  ): Promise<{ url: string; key: string }> {
    try {
      // Read the file
      const fileBuffer = fs.readFileSync(filePath);

      // Detect content type if not provided
      if (!contentType) {
        const fileTypeResult = await fileType.fromBuffer(fileBuffer);
        contentType = fileTypeResult?.mime || 'application/octet-stream';
      }

      // Generate a key if not provided
      const fileName = path.basename(filePath);
      const fileKey = key || `uploads/${uuid()}-${fileName}`;

      // Upload the file
      return this.uploadFile(fileBuffer, fileKey, contentType);
    } catch (error) {
      this.logger.error(
        `Error uploading file from path: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to upload file from path: ${error.message}`);
    }
  }

  /**
   * Upload base64 encoded data to R2 storage
   * @param base64Data Base64 encoded data
   * @param key Optional key (filename) for the file, if not provided a UUID will be generated
   * @param contentType The content type of the file (optional, will be detected if not provided)
   * @returns Object containing the URL and key of the uploaded file
   */
  async uploadBase64Data(
    base64Data: string,
    key?: string,
    contentType?: string,
  ): Promise<{ url: string; key: string }> {
    try {
      // Remove data URL prefix if present
      const base64Content = base64Data.replace(/^data:([^;]+);base64,/, '');

      // Create a buffer from the base64 data
      const buffer = Buffer.from(base64Content, 'base64');

      // Detect content type if not provided
      if (!contentType) {
        const fileTypeResult = await fileType.fromBuffer(buffer);
        contentType = fileTypeResult?.mime || 'application/octet-stream';

        // Generate extension from content type
        const extension = fileTypeResult?.ext || 'bin';
        key = key || `uploads/${uuid()}.${extension}`;
      } else {
        key = key || `uploads/${uuid()}`;
      }

      // Upload the buffer
      return this.uploadFile(buffer, key, contentType);
    } catch (error) {
      this.logger.error(
        `Error uploading base64 data: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to upload base64 data: ${error.message}`);
    }
  }

  /**
   * Get a signed URL for a file (for private buckets)
   * @param key The key (filename) of the file
   * @param expirationSeconds How long the URL should be valid for
   * @returns A signed URL for the file
   */
  async getSignedUrl(key: string, expirationSeconds = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, {
        expiresIn: expirationSeconds,
      });
    } catch (error) {
      this.logger.error(
        `Error generating signed URL: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }

  /**
   * Check if a file exists in R2 storage
   * @param key The key (filename) of the file
   * @returns Boolean indicating if the file exists
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      this.logger.error(
        `Error checking if file exists: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to check if file exists: ${error.message}`);
    }
  }

  /**
   * Delete a file from R2 storage
   * @param key The key (filename) of the file to delete
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.debug(`Deleted file from R2: ${key}`);
    } catch (error) {
      this.logger.error(
        `Error deleting file from R2: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to delete file from R2: ${error.message}`);
    }
  }

  /**
   * List files in a directory in R2 storage
   * @param prefix The directory prefix to list
   * @param maxKeys Maximum number of keys to return
   * @returns Array of file keys
   */
  async listFiles(prefix: string, maxKeys = 1000): Promise<string[]> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      return (response.Contents || [])
        .map((item) => item.Key)
        .filter((key): key is string => key !== undefined);
    } catch (error) {
      this.logger.error(`Error listing files: ${error.message}`, error.stack);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  /**
   * Generate a presigned URL for uploading a file to R2
   * @param key The key (filename) for the file
   * @param contentType The content type of the file
   * @param expirationSeconds How long the URL should be valid for
   * @returns A presigned URL for uploading
   */
  async generatePresignedUploadUrl(
    key: string,
    contentType: string,
    expirationSeconds = 3600,
  ): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        ContentType: contentType,
      });

      return await getSignedUrl(this.s3Client, command, {
        expiresIn: expirationSeconds,
      });
    } catch (error) {
      this.logger.error(
        `Error generating presigned upload URL: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Failed to generate presigned upload URL: ${error.message}`,
      );
    }
  }

  /**
   * Get the public URL base for the R2 bucket
   * @returns The public URL base
   */
  getPublicUrl(): string {
    return this.publicUrl;
  }
}
