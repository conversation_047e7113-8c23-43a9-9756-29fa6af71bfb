import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as crypto from 'crypto';
import fileType from 'file-type';

@Injectable()
export class TempFileStorageService {
  private readonly logger = new Logger(TempFileStorageService.name);
  private tempDir: string;
  private readonly allowedMimeTypes: string[] = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/heic',
    'image/bmp',
    'image/gif',
    'image/tiff',
  ];

  // Maximum file size: 5MB in bytes
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024;

  constructor(private configService: ConfigService) {
    // Get temp directory from environment variables or use default
    const configuredTempDir = this.configService.get<string>('TEMP_FILES_DIR');

    if (configuredTempDir) {
      // Use configured directory
      this.tempDir = configuredTempDir;
      this.logger.log(`Using configured temp directory: ${this.tempDir}`);
    } else {
      // Create a dedicated temp directory for the application
      this.tempDir = path.join(os.tmpdir(), 'tiktok-shop-temp');
      this.logger.log(`Using default temp directory: ${this.tempDir}`);
    }

    this.ensureTempDirExists();
  }

  /**
   * Ensure the temporary directory exists
   */
  private ensureTempDirExists(): void {
    if (!fs.existsSync(this.tempDir)) {
      try {
        fs.mkdirSync(this.tempDir, { recursive: true });
        this.logger.log(`Created temporary directory: ${this.tempDir}`);
      } catch (error) {
        this.logger.error(
          `Failed to create temporary directory: ${error.message}`,
        );
        // Fall back to system temp directory
        this.tempDir = os.tmpdir();
      }
    }
  }

  /**
   * Store base64 image data as a temporary file
   * @param base64Data Base64 encoded image data
   * @returns Path to the temporary file
   */
  async storeBase64Image(base64Data: string): Promise<string> {
    try {
      // Remove data URL prefix if present
      const base64Image = base64Data.replace(/^data:image\/\w+;base64,/, '');

      // Create a buffer from the base64 data
      const imageBuffer = Buffer.from(base64Image, 'base64');

      // Validate file type
      await this.validateImageBuffer(imageBuffer);

      // Generate a unique filename with appropriate extension
      const fileTypeResult = await fileType.fromBuffer(imageBuffer);
      const extension = fileTypeResult?.ext || 'jpg';
      const filename = `image-${crypto.randomUUID()}.${extension}`;
      const tempFilePath = path.join(this.tempDir, filename);

      // Write the buffer to the file
      fs.writeFileSync(tempFilePath, imageBuffer);

      this.logger.debug(
        `Stored base64 image as temporary file: ${tempFilePath}`,
      );
      return tempFilePath;
    } catch (error) {
      this.logger.error(
        `Error storing base64 image: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to store base64 image: ${error.message}`);
    }
  }

  /**
   * Validate that a buffer contains image data of an allowed type and size
   * @param buffer Buffer containing image data
   * @throws BadRequestException if the file type is not allowed or size exceeds limit
   */
  public async validateImageBuffer(buffer: Buffer): Promise<void> {
    try {
      // Check file size (5MB limit)
      if (buffer.length > this.MAX_FILE_SIZE) {
        const sizeInMB = (buffer.length / (1024 * 1024)).toFixed(2);
        throw new BadRequestException(
          `File size (${sizeInMB}MB) exceeds the maximum allowed size of 5MB`,
        );
      }

      const fileTypeResult = await fileType.fromBuffer(buffer);

      // Check if file type could be determined
      if (!fileTypeResult) {
        throw new BadRequestException(
          'Could not determine file type. Only image files are allowed.',
        );
      }

      // Check if the file type is in the allowed list
      if (!this.allowedMimeTypes.includes(fileTypeResult.mime)) {
        throw new BadRequestException(
          `File type ${fileTypeResult.mime} is not allowed. Allowed types: ${this.allowedMimeTypes.join(', ')}`,
        );
      }

      this.logger.debug(
        `Validated image file of type: ${fileTypeResult.mime}, size: ${(buffer.length / 1024).toFixed(2)}KB`,
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to validate image: ${error.message}`,
      );
    }
  }

  /**
   * Store image from URL as a temporary file
   * @param imageUrl URL of the image
   * @returns Path to the temporary file
   */
  async storeImageFromUrl(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Create a temporary file path without extension (will be determined later)
        const tempId = crypto.randomUUID();
        const tempFilePath = path.join(this.tempDir, `image-${tempId}-temp`);

        // Create a write stream
        const file = fs.createWriteStream(tempFilePath);

        // Determine if we need http or https
        const client = imageUrl.startsWith('https')
          ? require('https')
          : require('http');

        // Download the image
        const request = client.get(imageUrl, (response: any) => {
          // Check if the request was successful
          if (response.statusCode !== 200) {
            reject(
              new Error(
                `Failed to download image: HTTP ${response.statusCode}`,
              ),
            );
            return;
          }

          // Pipe the response to the file
          response.pipe(file);

          // Handle file completion
          file.on('finish', async () => {
            file.close();

            try {
              // Read the file to validate it
              const buffer = fs.readFileSync(tempFilePath);

              // Validate the image type
              await this.validateImageBuffer(buffer);

              // Determine the file extension
              const fileTypeResult = await fileType.fromBuffer(buffer);
              const extension = fileTypeResult?.ext || 'jpg';

              // Create the final file path with proper extension
              const finalFilePath = path.join(
                this.tempDir,
                `image-${tempId}.${extension}`,
              );

              // Rename the temp file to the final file
              fs.renameSync(tempFilePath, finalFilePath);

              this.logger.debug(
                `Stored URL image as temporary file: ${finalFilePath}`,
              );
              resolve(finalFilePath);
            } catch (error) {
              // Clean up the temp file
              fs.unlinkSync(tempFilePath);
              reject(error);
            }
          });
        });

        // Handle request errors
        request.on('error', (err: Error) => {
          fs.unlink(tempFilePath, () => {});
          reject(new Error(`Failed to download image: ${err.message}`));
        });

        // Handle file errors
        file.on('error', (err: Error) => {
          fs.unlink(tempFilePath, () => {});
          reject(new Error(`Failed to save image: ${err.message}`));
        });
      } catch (error) {
        reject(new Error(`Failed to store image from URL: ${error.message}`));
      }
    });
  }

  /**
   * Read a temporary file
   * @param filePath Path to the temporary file
   * @returns File content as a Buffer
   */
  readTempFile(filePath: string): Buffer {
    try {
      return fs.readFileSync(filePath);
    } catch (error) {
      this.logger.error(
        `Error reading temporary file: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to read temporary file: ${error.message}`);
    }
  }

  /**
   * Create a read stream for a temporary file
   * @param filePath Path to the temporary file
   * @returns ReadStream for the file
   */
  createReadStream(filePath: string): fs.ReadStream {
    try {
      return fs.createReadStream(filePath);
    } catch (error) {
      this.logger.error(
        `Error creating read stream: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to create read stream: ${error.message}`);
    }
  }

  /**
   * Delete a temporary file
   * @param filePath Path to the temporary file
   */
  deleteTempFile(filePath: string): void {
    try {
      if (filePath && fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.debug(`Deleted temporary file: ${filePath}`);
      }
    } catch (error) {
      this.logger.warn(
        `Failed to delete temporary file ${filePath}: ${error.message}`,
      );
    }
  }

  /**
   * Clean up old temporary files
   * @param maxAgeHours Maximum age of files to keep in hours
   */
  cleanupOldTempFiles(maxAgeHours = 24): void {
    try {
      const files = fs.readdirSync(this.tempDir);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert hours to milliseconds

      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        const stats = fs.statSync(filePath);

        // Check if the file is older than maxAge
        if (now - stats.mtimeMs > maxAge) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }

      if (deletedCount > 0) {
        this.logger.log(`Cleaned up ${deletedCount} old temporary files`);
      }
    } catch (error) {
      this.logger.error(
        `Error cleaning up old temporary files: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Scheduled task to clean up old temporary files
   * Runs every day at midnight
   */
  /*
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  handleScheduledCleanup() {
    this.logger.log('Running scheduled cleanup of temporary files');
    this.cleanupOldTempFiles(24); // Clean up files older than 24 hours
  }
  */
}
