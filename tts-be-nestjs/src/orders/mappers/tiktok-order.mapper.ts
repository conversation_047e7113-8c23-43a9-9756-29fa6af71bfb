import { Injectable } from '@nestjs/common';
import { CreateOrderDto, CreateOrderLineItemDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { OrderStatus, PaymentInfo, RecipientAddress } from '../entities/order.entity';
import { getProperty } from '../../common/utils/property-name.util';

/**
 * Mapper class for transforming TikTok Order API responses to DTOs
 */
@Injectable()
export class TikTokOrderMapper {

  /**
   * Maps TikTok Shop order data to CreateOrderDto
   * @param orderData Raw order data from TikTok API
   * @param tiktokShopId TikTok Shop ID from internal system
   * @param userId User ID from internal system (optional)
   * @returns CreateOrderDto
   */
  mapToCreateOrderDto(
    orderData: any,
    tiktokShopId: number,
    userId?: number,
  ): CreateOrderDto {
    // Extract order data
    const orderDto: CreateOrderDto = {
      idTT: orderData.id,
      tiktokShopId: tiktokShopId,
      userId: userId,
      lineItems: [],
      // Store complete raw response for future extensibility
      rawTikTokResponse: orderData,
    };

    // Map basic order fields
    const statusValue = getProperty(orderData, 'status');
    orderDto.status = statusValue ? this.mapOrderStatus(statusValue) : undefined;
    orderDto.orderType = getProperty(orderData, 'orderType');
    orderDto.createTimeTT = getProperty(orderData, 'createTime');
    orderDto.updateTimeTT = getProperty(orderData, 'updateTime');
    orderDto.paidTime = getProperty(orderData, 'paidTime');

    // Map customer information
    orderDto.buyerEmail = getProperty(orderData, 'buyerEmail');
    orderDto.buyerMessage = getProperty(orderData, 'buyerMessage');
    orderDto.userIdTT = getProperty(orderData, 'userId');

    // Map payment information
    const paymentData = getProperty(orderData, 'payment');
    if (paymentData) {
      orderDto.payment = this.mapPaymentInfo(paymentData);
    }
    orderDto.paymentMethodName = getProperty(orderData, 'paymentMethodName');

    // Map recipient address
    const recipientAddressData = getProperty(orderData, 'recipientAddress');
    if (recipientAddressData) {
      orderDto.recipientAddress = this.mapRecipientAddress(recipientAddressData);
    }

    // Map fulfillment and shipping information
    orderDto.fulfillmentType = getProperty(orderData, 'fulfillmentType');
    orderDto.shippingProvider = getProperty(orderData, 'shippingProvider');
    orderDto.shippingProviderId = getProperty(orderData, 'shippingProviderId');
    orderDto.shippingType = getProperty(orderData, 'shippingType');
    orderDto.trackingNumber = getProperty(orderData, 'trackingNumber');
    orderDto.deliveryOptionId = getProperty(orderData, 'deliveryOptionId');
    orderDto.deliveryOptionName = getProperty(orderData, 'deliveryOptionName');

    // Map boolean flags
    orderDto.isCod = getProperty(orderData, 'isCod', false);
    orderDto.isExchangeOrder = getProperty(orderData, 'isExchangeOrder', false);
    orderDto.isReplacementOrder = getProperty(orderData, 'isReplacementOrder', false);
    orderDto.isSampleOrder = getProperty(orderData, 'isSampleOrder', false);

    // Map line items if available
    const lineItemsData = getProperty(orderData, 'lineItems');
    if (lineItemsData && Array.isArray(lineItemsData)) {
      orderDto.lineItems = lineItemsData.map((lineItem: any) =>
        this.mapToCreateOrderLineItemDto(lineItem),
      );
    }

    return orderDto;
  }

  /**
   * Maps TikTok Shop order line item data to CreateOrderLineItemDto
   * @param lineItemData Raw line item data from TikTok API
   * @returns CreateOrderLineItemDto
   */
  mapToCreateOrderLineItemDto(lineItemData: any): CreateOrderLineItemDto {
    const lineItemDto: CreateOrderLineItemDto = {
      // Store complete raw response for future extensibility
      rawTikTokResponse: lineItemData,
    };

    // Map basic line item fields
    lineItemDto.idTT = getProperty(lineItemData, 'id');
    lineItemDto.productIdTT = getProperty(lineItemData, 'productId');
    lineItemDto.skuIdTT = getProperty(lineItemData, 'skuId');
    lineItemDto.sellerSku = getProperty(lineItemData, 'sellerSku');
    lineItemDto.productName = getProperty(lineItemData, 'productName');
    lineItemDto.skuName = getProperty(lineItemData, 'skuName');
    lineItemDto.skuImage = getProperty(lineItemData, 'skuImage');

    // Map pricing information
    const originalPrice = getProperty(lineItemData, 'originalPrice');
    if (originalPrice) {
      lineItemDto.originalPrice = this.parseDecimal(originalPrice);
    }

    const salePrice = getProperty(lineItemData, 'salePrice');
    if (salePrice) {
      lineItemDto.salePrice = this.parseDecimal(salePrice);
    }

    lineItemDto.currency = getProperty(lineItemData, 'currency');

    const platformDiscount = getProperty(lineItemData, 'platformDiscount');
    if (platformDiscount) {
      lineItemDto.platformDiscount = this.parseDecimal(platformDiscount);
    }

    const sellerDiscount = getProperty(lineItemData, 'sellerDiscount');
    if (sellerDiscount) {
      lineItemDto.sellerDiscount = this.parseDecimal(sellerDiscount);
    }

    // Map package and status information
    lineItemDto.packageId = getProperty(lineItemData, 'packageId');
    lineItemDto.packageStatus = getProperty(lineItemData, 'packageStatus');
    lineItemDto.displayStatus = getProperty(lineItemData, 'displayStatus');

    // Map boolean flags
    lineItemDto.isDangerousGood = getProperty(lineItemData, 'isDangerousGood', false);
    lineItemDto.isGift = getProperty(lineItemData, 'isGift', false);

    // Map complex fields
    const itemTax = getProperty(lineItemData, 'itemTax');
    if (itemTax) {
      lineItemDto.itemTax = Array.isArray(itemTax) ? itemTax : [itemTax];
    }

    const combinedListingSkus = getProperty(lineItemData, 'combinedListingSkus');
    if (combinedListingSkus) {
      lineItemDto.combinedListingSkus = Array.isArray(combinedListingSkus) 
        ? combinedListingSkus 
        : [combinedListingSkus];
    }

    // Map quantity
    const quantity = getProperty(lineItemData, 'quantity');
    if (quantity) {
      lineItemDto.quantity = parseInt(quantity.toString(), 10) || 1;
    }

    return lineItemDto;
  }

  /**
   * Maps TikTok order status to internal OrderStatus enum
   * @param status TikTok order status string
   * @returns OrderStatus enum value
   */
  private mapOrderStatus(status: string): OrderStatus | undefined {
    if (!status) return undefined;

    const statusMap: Record<string, OrderStatus> = {
      'UNPAID': OrderStatus.UNPAID,
      'ON_HOLD': OrderStatus.ON_HOLD,
      'AWAITING_SHIPMENT': OrderStatus.AWAITING_SHIPMENT,
      'PARTIALLY_SHIPPING': OrderStatus.PARTIALLY_SHIPPING,
      'AWAITING_COLLECTION': OrderStatus.AWAITING_COLLECTION,
      'IN_TRANSIT': OrderStatus.IN_TRANSIT,
      'DELIVERED': OrderStatus.DELIVERED,
      'COMPLETED': OrderStatus.COMPLETED,
      'CANCELLED': OrderStatus.CANCELLED,
    };

    return statusMap[status.toUpperCase()] || undefined;
  }

  /**
   * Maps TikTok payment information to PaymentInfo interface
   * @param paymentData Raw payment data from TikTok API
   * @returns PaymentInfo object
   */
  private mapPaymentInfo(paymentData: any): PaymentInfo {
    const payment: PaymentInfo = {};

    // Map all payment fields
    payment.totalAmount = getProperty(paymentData, 'totalAmount');
    payment.subTotal = getProperty(paymentData, 'subTotal');
    payment.currency = getProperty(paymentData, 'currency');
    payment.originalTotalProductPrice = getProperty(paymentData, 'originalTotalProductPrice');
    payment.tax = getProperty(paymentData, 'tax');
    payment.shippingFee = getProperty(paymentData, 'shippingFee');
    payment.platformDiscount = getProperty(paymentData, 'platformDiscount');
    payment.sellerDiscount = getProperty(paymentData, 'sellerDiscount');
    payment.shippingFeeTax = getProperty(paymentData, 'shippingFeeTax');
    payment.productTax = getProperty(paymentData, 'productTax');
    payment.retailDeliveryFee = getProperty(paymentData, 'retailDeliveryFee');
    payment.buyerServiceFee = getProperty(paymentData, 'buyerServiceFee');
    payment.handlingFee = getProperty(paymentData, 'handlingFee');
    payment.itemInsuranceFee = getProperty(paymentData, 'itemInsuranceFee');
    payment.shippingInsuranceFee = getProperty(paymentData, 'shippingInsuranceFee');
    payment.smallOrderFee = getProperty(paymentData, 'smallOrderFee');
    payment.originalShippingFee = getProperty(paymentData, 'originalShippingFee');
    payment.shippingFeePlatformDiscount = getProperty(paymentData, 'shippingFeePlatformDiscount');
    payment.shippingFeeSellerDiscount = getProperty(paymentData, 'shippingFeeSellerDiscount');
    payment.shippingFeeCofundedDiscount = getProperty(paymentData, 'shippingFeeCofundedDiscount');

    return payment;
  }

  /**
   * Maps TikTok recipient address to RecipientAddress interface
   * @param addressData Raw address data from TikTok API
   * @returns RecipientAddress object
   */
  private mapRecipientAddress(addressData: any): RecipientAddress {
    const address: RecipientAddress = {};

    // Map all address fields
    address.name = getProperty(addressData, 'name');
    address.firstName = getProperty(addressData, 'firstName');
    address.lastName = getProperty(addressData, 'lastName');
    address.phoneNumber = getProperty(addressData, 'phoneNumber');
    address.addressDetail = getProperty(addressData, 'addressDetail');
    address.addressLine1 = getProperty(addressData, 'addressLine1');
    address.addressLine2 = getProperty(addressData, 'addressLine2');
    address.addressLine3 = getProperty(addressData, 'addressLine3');
    address.addressLine4 = getProperty(addressData, 'addressLine4');
    address.postalCode = getProperty(addressData, 'postalCode');
    address.regionCode = getProperty(addressData, 'regionCode');
    address.fullAddress = getProperty(addressData, 'fullAddress');
    address.firstNameLocalScript = getProperty(addressData, 'firstNameLocalScript');
    address.lastNameLocalScript = getProperty(addressData, 'lastNameLocalScript');

    // Map complex fields
    const districtInfo = getProperty(addressData, 'districtInfo');
    if (districtInfo) {
      address.districtInfo = Array.isArray(districtInfo) ? districtInfo : [districtInfo];
    }

    const deliveryPreferences = getProperty(addressData, 'deliveryPreferences');
    if (deliveryPreferences) {
      address.deliveryPreferences = deliveryPreferences;
    }

    return address;
  }

  /**
   * Safely parses a decimal value from string or number
   * @param value The value to parse
   * @returns Parsed decimal number or undefined
   */
  private parseDecimal(value: any): number | undefined {
    if (value === null || value === undefined) return undefined;
    
    const parsed = parseFloat(value.toString());
    return isNaN(parsed) ? undefined : parsed;
  }

  /**
   * Maps TikTok order data to UpdateOrderDto (for updates)
   * @param orderData Raw order data from TikTok API
   * @returns UpdateOrderDto
   */
  mapToUpdateOrderDto(orderData: any): UpdateOrderDto {
    // For updates, we use the same mapping logic but return UpdateOrderDto
    const createDto = this.mapToCreateOrderDto(orderData, 0); // tiktokShopId will be preserved

    // Remove tiktokShopId and lineItems from update DTO as they shouldn't be merged
    // lineItems are handled separately in processLineItemsForExistingOrder
    const { tiktokShopId, lineItems, ...updateDto } = createDto;

    return updateDto;
  }
}
