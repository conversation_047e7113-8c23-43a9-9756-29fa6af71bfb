import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import {
  OrderQueryDto,
  OrderSyncDto,
  OrderDetailSyncDto,
  SyncResultDto,
  OrderResponseDto,
} from './dto';
import { PaginatedResult } from '../common/interfaces/paginated-result.interface';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @ApiOperation({ 
    summary: 'Synchronize orders from TikTok Shop',
    description: 'Fetch and synchronize order list from TikTok Shop API with pagination and filtering support'
  })
  @ApiResponse({
    status: 200,
    description: 'Orders synchronized successfully',
    type: SyncResultDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'TikTok Shop not found or access denied',
  })
  @ApiBody({
    type: OrderSyncDto,
    description: 'Order synchronization parameters',
  })
  @Post('synchronize')
  @HttpCode(HttpStatus.OK)
  async synchronizeOrders(
    @Body() syncDto: OrderSyncDto,
    @CurrentUser('id') userId: number,
  ): Promise<SyncResultDto> {
    // Validate DTO
    const validatedDto = plainToInstance(OrderSyncDto, syncDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.synchronizeOrders(validatedDto, userId);
  }

  @ApiOperation({ 
    summary: 'Synchronize order details from TikTok Shop',
    description: 'Fetch detailed order information for specific orders (max 50 orders per request)'
  })
  @ApiResponse({
    status: 200,
    description: 'Order details synchronized successfully',
    type: SyncResultDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters or too many order IDs',
  })
  @ApiResponse({
    status: 404,
    description: 'TikTok Shop not found or access denied',
  })
  @ApiBody({
    type: OrderDetailSyncDto,
    description: 'Order detail synchronization parameters',
  })
  @Post('synchronize-details')
  @HttpCode(HttpStatus.OK)
  async synchronizeOrderDetails(
    @Body() syncDto: OrderDetailSyncDto,
    @CurrentUser('id') userId: number,
  ): Promise<SyncResultDto> {
    // Validate DTO
    const validatedDto = plainToInstance(OrderDetailSyncDto, syncDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.synchronizeOrderDetails(validatedDto, userId);
  }

  @ApiOperation({
    summary: 'Get all orders with pagination and filters',
    description: 'Retrieve orders with advanced filtering, pagination, and sorting options. OrderLineItems are included by default in the response.'
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    type: 'PaginatedResult<OrderResponseDto>',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters',
  })
  @Get()
  async findAll(
    @Query() query: any,
    @CurrentUser('id') userId: number,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    // Validate query parameters
    const queryDto = plainToInstance(OrderQueryDto, query);
    const errors = validateSync(queryDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.findAll(queryDto, userId);
  }

  @ApiOperation({ 
    summary: 'Get order by ID',
    description: 'Retrieve a specific order by its internal ID with all related data'
  })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    type: OrderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID from internal system',
    type: 'number',
    example: 1,
  })
  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<OrderResponseDto> {
    return this.ordersService.findOne(id, userId);
  }
}
