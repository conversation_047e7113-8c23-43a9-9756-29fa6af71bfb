import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Order } from './order.entity';

@Entity('order_line_items')
export class OrderLineItem {
  @ApiProperty({ description: 'Line item ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Line item ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @Column({ nullable: true })
  idTT: string;

  @ApiProperty({
    description: 'Product ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @Column({ nullable: true })
  productIdTT: string;

  @ApiProperty({
    description: 'SKU ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @Column({ nullable: true })
  skuIdTT: string;

  @ApiProperty({
    description: 'Seller SKU identifier',
    example: 'TSHIRT-BLK-M',
  })
  @Column({ nullable: true })
  sellerSku: string;

  @ApiProperty({
    description: 'Product name',
    example: 'Classic Cotton T-Shirt',
  })
  @Column({ nullable: true })
  productName: string;

  @ApiProperty({
    description: 'SKU name with attributes',
    example: 'Black, Medium',
  })
  @Column({ nullable: true })
  skuName: string;

  @ApiProperty({
    description: 'SKU image URL',
    example: 'https://example.com/image.jpg',
  })
  @Column({ nullable: true })
  skuImage: string;

  @ApiProperty({
    description: 'Original price before discounts',
    example: 29.99,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  originalPrice: number;

  @ApiProperty({
    description: 'Sale price after discounts',
    example: 24.99,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  salePrice: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  @Column({ nullable: true })
  currency: string;

  @ApiProperty({
    description: 'Platform discount amount',
    example: 2.00,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  platformDiscount: number;

  @ApiProperty({
    description: 'Seller discount amount',
    example: 3.00,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  sellerDiscount: number;

  @ApiProperty({
    description: 'Package ID for shipping',
    example: 'PKG123456',
  })
  @Column({ nullable: true })
  packageId: string;

  @ApiProperty({
    description: 'Package status',
    example: 'TO_FULFILL',
  })
  @Column({ nullable: true })
  packageStatus: string;

  @ApiProperty({
    description: 'Display status for the line item',
    example: 'AWAITING_SHIPMENT',
  })
  @Column({ nullable: true })
  displayStatus: string;

  @ApiProperty({
    description: 'Whether the SKU is a hazmat item',
    example: false,
  })
  @Column({ default: false })
  isDangerousGood: boolean;

  @ApiProperty({
    description: 'Whether the item is a gift',
    example: false,
  })
  @Column({ default: false })
  isGift: boolean;

  @ApiProperty({
    description: 'Item tax details from TikTok Shop',
    example: [
      {
        taxType: 'VAT',
        taxAmount: '2.40',
        taxRate: '0.10',
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  itemTax: any[];

  @ApiProperty({
    description: 'Combined listing SKUs information',
    example: [
      {
        skuId: '7123456789',
        quantity: 1,
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  combinedListingSkus: any[];

  @ApiProperty({
    description: 'Quantity of the item ordered',
    example: 2,
  })
  @Column({ nullable: true, default: 1 })
  quantity: number;

  @ApiProperty({
    description: 'Retail delivery fee for this item',
    example: 1.50,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  retailDeliveryFee: number;

  @ApiProperty({
    description: 'Buyer service fee for this item',
    example: 0.50,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  buyerServiceFee: number;

  @ApiProperty({
    description: 'Small order fee for this item',
    example: 1.00,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  smallOrderFee: number;

  @ApiProperty({
    description: 'Shipping provider ID for this item',
    example: '12345',
  })
  @Column({ nullable: true })
  shippingProviderId: string;

  @ApiProperty({
    description: 'Shipping provider name for this item',
    example: 'UPS',
  })
  @Column({ nullable: true })
  shippingProviderName: string;

  @ApiProperty({
    description: 'Tracking number for this item',
    example: '1Z999AA1234567890',
  })
  @Column({ nullable: true })
  trackingNumber: string;

  @ApiProperty({
    description: 'RTS (Ready to Ship) time for this item',
    example: **********,
  })
  @Column({ nullable: true })
  rtsTime: number;

  @ApiProperty({
    description: 'Cancellation reason for this item',
    example: 'Out of stock',
  })
  @Column({ nullable: true })
  cancelReason: string;

  @ApiProperty({
    description: 'Who initiated the cancellation',
    example: 'SELLER',
  })
  @Column({ nullable: true })
  cancelUser: string;

  @ApiProperty({
    description: 'SKU type information',
    example: 'NORMAL',
  })
  @Column({ nullable: true })
  skuType: string;

  @ApiProperty({
    description: 'Handling duration in days',
    example: '3',
  })
  @Column({ nullable: true })
  handlingDurationDays: string;

  @ApiProperty({
    description: 'Complete raw TikTok line item response for future extensibility',
  })
  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any;

  @ApiProperty({ description: 'Order this line item belongs to' })
  @ManyToOne(() => Order, (order) => order.lineItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @ApiProperty({
    description: 'Order ID from internal system',
    example: 1,
  })
  @Column()
  orderId: number;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
