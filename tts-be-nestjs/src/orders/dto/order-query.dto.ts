import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, IsEnum, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { FilterOrderDto } from './filter-order.dto';

export enum OrderSortField {
  CREATE_TIME = 'create_time',
  CREATE_TIME_TT = 'createTimeTT',
  UPDATE_TIME = 'update_time',
  PAID_TIME = 'paid_time',
  TOTAL_AMOUNT = 'total_amount',
  STATUS = 'status',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class OrderQueryDto extends FilterOrderDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  pageSize?: number = 20;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: OrderSortField,
    example: OrderSortField.CREATE_TIME,
  })
  @IsOptional()
  @IsEnum(OrderSortField)
  sortField?: OrderSortField = OrderSortField.CREATE_TIME;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    example: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Search term for order ID, buyer email, or tracking number',
    example: 'ORDER123',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'TikTok Shop ID to filter orders',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId?: number;

  @ApiPropertyOptional({
    description: 'User ID to filter orders',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'Include line items in response',
    example: true,
    default: true,
  })
  @IsOptional()
  includeLineItems?: boolean = true;

  @ApiPropertyOptional({
    description: 'Include TikTok Shop information in response',
    example: true,
  })
  @IsOptional()
  includeTikTokShop?: boolean = false;

  @ApiPropertyOptional({
    description: 'Include user information in response',
    example: false,
  })
  @IsOptional()
  includeUser?: boolean = false;
}
