import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsArray, IsString, IsN<PERSON>ber, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus } from '../entities/order.entity';

export class FilterOrderDto {
  @ApiPropertyOptional({
    description: 'Filter by order status',
    enum: OrderStatus,
    isArray: true,
    example: [OrderStatus.AWAITING_SHIPMENT, OrderStatus.IN_TRANSIT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OrderStatus, { each: true })
  status?: OrderStatus[];

  @ApiPropertyOptional({
    description: 'Filter by order type',
    example: 'PRE_ORDER',
  })
  @IsOptional()
  @IsString()
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Filter by fulfillment type',
    example: 'FULFILLMENT_BY_SELLER',
  })
  @IsOptional()
  @IsString()
  fulfillmentType?: string;

  @ApiPropertyOptional({
    description: 'Filter orders created after this timestamp (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'Filter orders created before this timestamp (Unix timestamp)',
    example: 1672531199,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Filter orders updated after this timestamp (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'Filter orders updated before this timestamp (Unix timestamp)',
    example: 1672531199,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Filter orders paid after this timestamp (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  paidTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'Filter orders paid before this timestamp (Unix timestamp)',
    example: 1672531199,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  paidTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Filter by buyer email (partial match)',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  buyerEmail?: string;

  @ApiPropertyOptional({
    description: 'Filter by TikTok buyer user ID',
    example: '**********',
  })
  @IsOptional()
  @IsString()
  userIdTT?: string;

  @ApiPropertyOptional({
    description: 'Filter by payment method name',
    example: 'Credit Card',
  })
  @IsOptional()
  @IsString()
  paymentMethodName?: string;

  @ApiPropertyOptional({
    description: 'Filter by shipping provider',
    example: 'UPS',
  })
  @IsOptional()
  @IsString()
  shippingProvider?: string;

  @ApiPropertyOptional({
    description: 'Filter by tracking number',
    example: '1Z999AA1234567890',
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiPropertyOptional({
    description: 'Filter COD orders only',
    example: true,
  })
  @IsOptional()
  isCod?: boolean;

  @ApiPropertyOptional({
    description: 'Filter exchange orders only',
    example: false,
  })
  @IsOptional()
  isExchangeOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Filter replacement orders only',
    example: false,
  })
  @IsOptional()
  isReplacementOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Filter sample orders only',
    example: false,
  })
  @IsOptional()
  isSampleOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by currency',
    example: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Filter by minimum total amount',
    example: 10.00,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  minTotalAmount?: number;

  @ApiPropertyOptional({
    description: 'Filter by maximum total amount',
    example: 500.00,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  maxTotalAmount?: number;
}
