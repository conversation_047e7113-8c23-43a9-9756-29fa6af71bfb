import { ConfigService } from '@nestjs/config';
import { CloudStorageService } from '../common/cloud-storage/cloud-storage.service';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

async function testConnection() {
  console.log('Starting R2 connection test...');

  const configService = new ConfigService();
  const cloudStorage = new CloudStorageService(configService);

  try {
    // Create a test file
    const testBuffer = Buffer.from(
      'Hello, R2! This is a test file created at ' + new Date().toISOString(),
    );
    const testKey = `test-files/test-${Date.now()}.txt`;

    // Upload the file
    console.log('Uploading test file...');
    const uploadResult = await cloudStorage.uploadFile(
      testBuffer,
      testKey,
      'text/plain',
    );
    console.log(`File uploaded successfully: ${uploadResult.url}`);
    console.log(`R2 Key: ${uploadResult.key}`);

    // Get a signed URL
    console.log('Generating signed URL...');
    const signedUrl = await cloudStorage.getSignedUrl(testKey, 60);
    console.log(`Signed URL (valid for 60 seconds): ${signedUrl}`);

    // Check if file exists
    console.log('Checking if file exists...');
    const exists = await cloudStorage.fileExists(testKey);
    console.log(`File exists: ${exists}`);

    // List files in the test directory
    console.log('Listing files in test directory...');
    const files = await cloudStorage.listFiles('test-files/');
    console.log(`Found ${files.length} files:`);
    files.forEach((file) => console.log(` - ${file}`));

    // Delete the file
    console.log('Deleting test file...');
    await cloudStorage.deleteFile(testKey);
    console.log('File deleted successfully');

    // Verify deletion
    const existsAfterDelete = await cloudStorage.fileExists(testKey);
    console.log(`File exists after deletion: ${existsAfterDelete}`);

    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testConnection();
