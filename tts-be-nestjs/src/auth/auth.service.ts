import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { UserRole } from './enums/user-role.enum';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './entities/user.entity';
import { OAuthAccount } from './entities/oauth-account.entity';
import { MagicLinkToken } from './entities/magic-link-token.entity';
import { PaginationQueryDto } from '../common/dto/pagination-query.dto';
import { RefreshToken } from './entities/refresh-token.entity';
import { MailService } from '../mail/mail.service';
import { ConfigService } from '@nestjs/config';
import { TokenResponseDto } from './dto/token-response.dto';

@Injectable()
export class AuthService {
  private readonly accessTokenExpiresIn: number;
  private readonly refreshTokenExpiresIn: number;
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(OAuthAccount)
    private oauthAccountsRepository: Repository<OAuthAccount>,
    @InjectRepository(MagicLinkToken)
    private magicLinkTokensRepository: Repository<MagicLinkToken>,
    @InjectRepository(RefreshToken)
    private refreshTokensRepository: Repository<RefreshToken>,
    private jwtService: JwtService,
    private mailService: MailService,
    private configService: ConfigService,
  ) {
    // Set to 4 hours for access token to test refresh functionality, 7 days for refresh token
    this.accessTokenExpiresIn = 4 * 60 * 60; // 4 hours in seconds
    this.refreshTokenExpiresIn = 7 * 24 * 60 * 60; // 7 days in seconds
  }

  async oauthLogin(
    provider: string,
    providerId: string,
    email: string,
    name?: string,
    image?: string,
  ): Promise<TokenResponseDto> {
    // Find or create user
    let user = await this.usersRepository.findOne({ where: { email } });

    if (!user) {
      user = this.usersRepository.create({
        email,
        name,
        image,
      });
      await this.usersRepository.save(user);
    }

    // Find or create OAuth account
    let oauthAccount = await this.oauthAccountsRepository.findOne({
      where: { provider, providerId },
    });

    if (!oauthAccount) {
      oauthAccount = this.oauthAccountsRepository.create({
        user,
        provider,
        providerId,
      });
      await this.oauthAccountsRepository.save(oauthAccount);
    }

    // Generate tokens
    return this.generateTokens(user);
  }

  /**
   * Generate access and refresh tokens for a user
   */
  private async generateTokens(user: User): Promise<TokenResponseDto> {
    // Generate JWT access token
    const accessToken = this.jwtService.sign(
      {
        sub: user.id,
        email: user.email,
        role: user.role, // Include role in JWT payload
      },
      {
        expiresIn: this.accessTokenExpiresIn,
      },
    );

    // Generate refresh token
    const refreshToken = uuidv4();

    // Calculate expiry date for refresh token
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + this.refreshTokenExpiresIn);

    // Save refresh token to database
    await this.refreshTokensRepository.save({
      token: refreshToken,
      userId: user.id,
      user,
      isRevoked: false,
      expiresAt,
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenExpiresIn,
      tokenType: 'Bearer',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role, // Include role in response
      },
    };
  }

  async sendMagicLink(email: string, redirectUrl: string) {
    // Find or create user
    let user = await this.usersRepository.findOne({ where: { email } });

    if (!user) {
      user = this.usersRepository.create({ email });
      await this.usersRepository.save(user);
    }

    // Generate token
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // Token expires in 24 hours

    // Save token
    await this.magicLinkTokensRepository.save({
      email,
      token,
      expiresAt,
    });

    // Construct magic link URL
    // Check if redirectUrl already has query parameters to use correct separator
    const separator = redirectUrl.includes('?') ? '&' : '?';
    const magicLinkUrl = `${redirectUrl}${separator}email=${encodeURIComponent(email)}&token=${token}`;

    // Send email
    await this.mailService.sendMagicLink(email, magicLinkUrl);
  }

  async verifyMagicLink(
    email: string,
    token: string,
  ): Promise<TokenResponseDto> {
    // Find token
    const magicLinkToken = await this.magicLinkTokensRepository.findOne({
      where: { email, token },
    });

    if (!magicLinkToken) {
      throw new UnauthorizedException('Invalid token');
    }

    // Check if token is expired
    if (new Date() > magicLinkToken.expiresAt) {
      throw new UnauthorizedException('Token expired');
    }

    // Find or create user
    let user = await this.usersRepository.findOne({ where: { email } });

    if (!user) {
      user = this.usersRepository.create({ email });
      await this.usersRepository.save(user);
    }

    // Delete token
    await this.magicLinkTokensRepository.remove(magicLinkToken);

    // Generate tokens
    return this.generateTokens(user);
  }

  /**
   * Refresh access token using a valid refresh token
   */
  async refreshToken(refreshToken: string): Promise<TokenResponseDto> {
    this.logger.log(`Refreshing token: ${refreshToken}`);
    try {
      // Find the refresh token in the database
      const refreshTokenEntity = await this.refreshTokensRepository.findOne({
        where: { token: refreshToken },
        relations: ['user'],
      });

      // Check if token exists and is valid
      if (!refreshTokenEntity) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Check if token is expired
      const now = new Date();
      if (now > refreshTokenEntity.expiresAt) {
        throw new UnauthorizedException('Refresh token expired');
      }

      // Check if token is revoked
      if (refreshTokenEntity.isRevoked) {
        throw new UnauthorizedException('Refresh token revoked');
      }

      // Get the user
      const user = refreshTokenEntity.user;
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Revoke the current refresh token after a short delay (5 seconds)
      // This helps prevent issues if multiple requests are made with the same token in quick succession
      setTimeout(async () => {
        try {
          await this.refreshTokensRepository.update(refreshTokenEntity.id, {
            isRevoked: true,
          });
        } catch (error) {
          this.logger.error(`Error revoking refresh token: ${error.message}`);
        }
      }, 5000);

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      return tokens;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoke a refresh token (used for logout)
   */
  async revokeRefreshToken(refreshToken: string): Promise<void> {
    const result = await this.refreshTokensRepository.update(
      { token: refreshToken },
      { isRevoked: true },
    );

    if (result.affected === 0) {
      throw new BadRequestException('Invalid refresh token');
    }
  }

  /**
   * Revoke all refresh tokens for a user (used for logout from all devices)
   */
  async revokeAllUserRefreshTokens(userId: number): Promise<void> {
    await this.refreshTokensRepository.update({ userId }, { isRevoked: true });
  }

  /**
   * Find all users (without pagination)
   */
  async findAllUsers(): Promise<User[]> {
    return this.usersRepository.find();
  }

  /**
   * Find all users with pagination and filtering
   */
  async findAllUsersPaginated(
    paginationQuery: PaginationQueryDto,
    filterDto: { search?: string; role?: UserRole },
  ): Promise<{
    data: User[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };
  }> {
    // Set default values for pagination
    const limit = paginationQuery.limit ?? 20;
    const page = paginationQuery.page ?? 1;
    const { search, role } = filterDto;

    // Calculate the skip value based on page and limit
    const skip = (page - 1) * limit;

    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    // Apply filters if provided
    if (search) {
      queryBuilder.where(
        '(LOWER(user.email) LIKE LOWER(:search) OR LOWER(user.name) LIKE LOWER(:search))',
        { search: `%${search}%` },
      );
    }

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Order by creation date (newest first)
    queryBuilder.orderBy('user.createdAt', 'DESC');

    // Execute query
    const data = await queryBuilder.getMany();

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Find user by id
   */
  async findUserById(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new BadRequestException(`User with id ${id} not found`);
    }
    return user;
  }

  /**
   * Update user role
   */
  async updateUserRole(id: number, role: UserRole): Promise<User> {
    const user = await this.findUserById(id);
    user.role = role;
    return this.usersRepository.save(user);
  }
}
