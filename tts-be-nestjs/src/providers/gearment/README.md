# Gearment Provider Module

This module provides integration with the Gearment API for retrieving stock status and other data.

## Setup

1. Add the following environment variables to your `.env` file:

```
GEARMENT_API_KEY=StLRFP7zXsBTzspE
GEARMENT_API_SIGNATURE=Wlspt6WmMPzr2GD0MuYNNuZyjq6HFH2F
GEARMENT_API_BASE_URL=https://api.gearment.com/v2/
```

2. Import the `GearmentModule` in your `app.module.ts`:

```typescript
import { GearmentModule } from './providers/gearment/gearment.module';

@Module({
  imports: [
    // ... other modules
    GearmentModule,
  ],
})
export class AppModule {}
```

## Usage

### Inject the GearmentService

```typescript
import { GearmentService } from './providers/gearment/gearment.service';

@Injectable()
export class YourService {
  constructor(private readonly gearmentService: GearmentService) {}

  async someMethod() {
    // Get stock status
    const stockStatus = await this.gearmentService.getStockStatus({
      type: 'all',
      limit: 250,
      page: 1,
    });

    // Sync stock status to database
    const syncResult = await this.gearmentService.syncStockStatus({
      type: 'all',
      limit: 250,
      page: 1,
    });
  }
}
```

### API Endpoints

The module exposes the following endpoints:

- `GET /providers/gearment/stock-status` - Get stock status from Gearment API
- `POST /providers/gearment/sync-stock-status` - Sync stock status from Gearment API to database

## Entity

The `GearmentStock` entity represents stock status from Gearment provider and is stored in the `gearment_stock` table.

## Data Structure

### Stock Status Item

```typescript
{
  variant_id: string;
  color: string;
  hex_color_code: string;
  size: string;
  name: string;
  status: 'in_stock' | 'temporarily_unavailable' | 'time_expired' | 'out_of_stock';
}
```

### Stock Status Response

```typescript
{
  status: string;
  message: string;
  result: StockStatusItem[];
  paging: {
    offset: number | null;
    limit: number | null;
  };
}
```
