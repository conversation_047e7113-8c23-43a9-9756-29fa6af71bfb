import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { GearmentService } from './gearment.service';
import { GearmentController } from './gearment.controller';
import { GearmentStock } from './entities/gearment-stock.entity';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    TypeOrmModule.forFeature([GearmentStock]),
    ConfigModule,
  ],
  controllers: [GearmentController],
  providers: [GearmentService],
  exports: [GearmentService],
})
export class GearmentModule {}
