import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { firstValueFrom } from 'rxjs';
import { GearmentStock } from './entities/gearment-stock.entity';
import {
  GearmentStockResponse,
  GearmentStockItem,
} from './interfaces/stock-status.interface';
import {
  StockStatusItemDto,
  StockStatusListResponseDto,
  GearmentStockPaginatedQueryDto,
  GearmentStockPaginatedResponseDto,
} from './dto/stock-status.dto';

@Injectable()
export class GearmentService {
  private readonly logger = new Logger(GearmentService.name);
  private readonly apiKey: string;
  private readonly apiSignature: string;
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(GearmentStock)
    private readonly gearmentStockRepository: Repository<GearmentStock>,
  ) {
    this.apiKey = this.configService.get<string>('GEARMENT_API_KEY') ?? '';
    if (!this.apiKey) {
      throw new Error('GEARMENT_API_KEY is not set in environment variables');
    }
    this.apiSignature =
      this.configService.get<string>('GEARMENT_API_SIGNATURE') ?? '';
    if (!this.apiSignature) {
      throw new Error(
        'GEARMENT_API_SIGNATURE is not set in environment variables',
      );
    }
    this.baseUrl = this.configService.get<string>(
      'GEARMENT_API_BASE_URL',
      'https://api.gearment.com/v2/',
    );
  }

  /**
   * Get stock status from Gearment API
   * @returns Stock status response
   */
  async getStockStatusFromGearment(): Promise<GearmentStockResponse> {
    try {
      const type = 'all',
        limit = 250,
        page = 1;

      const url = `${this.baseUrl}?act=stock_status`;

      const headers = {
        'Content-Transfer-Encoding': 'application/json',
      };

      const data = {
        api_key: this.apiKey,
        api_signature: this.apiSignature,
        type,
        limit,
        page,
      };

      this.logger.debug(`Fetching stock status from Gearment API: ${url}`);

      const response = await firstValueFrom(
        this.httpService.post<GearmentStockResponse>(url, data, { headers }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching stock status from Gearment API: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Sync stock status from Gearment API to database
   * @returns Number of items synced
   */
  async syncStockStatus(): Promise<number> {
    try {
      const response = await this.getStockStatusFromGearment();

      if (response.status !== 'success') {
        throw new Error(`Gearment API returned error: ${response.message}`);
      }

      const stockItems = response.result;
      const now = new Date();

      // Process items in batches to avoid memory issues
      const batchSize = 100;
      let syncedCount = 0;

      for (let i = 0; i < stockItems.length; i += batchSize) {
        const batch = stockItems.slice(i, i + batchSize);
        await this.processBatch(batch, now);
        syncedCount += batch.length;
      }

      this.logger.log(
        `Successfully synced ${syncedCount} stock items from Gearment API`,
      );

      return syncedCount;
    } catch (error) {
      this.logger.error(
        `Error syncing stock status from Gearment API: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get stock status with standard pagination format
   * @param queryDto Query parameters for filtering and pagination
   * @returns Paginated stock status response
   */
  async getGearmentStockStatusPaginated(
    queryDto: GearmentStockPaginatedQueryDto,
  ): Promise<GearmentStockPaginatedResponseDto> {
    try {
      const {
        limit = 20,
        page = 1,
        status,
        name,
        color,
        size,
        type,
      } = queryDto;

      // Calculate the skip value based on page and limit
      const skip = (page - 1) * limit;

      // Build where conditions based on filters
      const whereConditions: any = {};

      if (status) {
        whereConditions.status = status;
      }

      if (name) {
        whereConditions.name = ILike(`%${name}%`);
      }

      if (color) {
        whereConditions.color = ILike(`%${color}%`);
      }

      if (size) {
        whereConditions.size = ILike(`%${size}%`);
      }

      if (type) {
        whereConditions.type = ILike(`%${type}%`);
      }

      // Get total count and paginated results
      const [stockItems, total] =
        await this.gearmentStockRepository.findAndCount({
          where: whereConditions,
          skip,
          take: limit,
          order: {
            updatedAt: 'DESC',
          },
        });

      // Map database entities to DTOs
      const resultItems: StockStatusItemDto[] = stockItems.map((item) => ({
        variant_id: item.variantId,
        color: item.color || '',
        hex_color_code: item.hexColorCode || '',
        size: item.size || '',
        name: item.name || '',
        type: item.type || '',
        status: item.status,
        last_synced_at: item.lastSyncedAt,
      }));

      // Calculate total pages
      const totalPages = Math.ceil(total / limit);

      // Build response in PaginatedResult format
      return {
        data: resultItems,
        meta: {
          totalItems: total,
          itemCount: resultItems.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving paginated stock status from database: ${error.message}`,
        error.stack,
      );

      // Throw a more specific error for better error handling
      if (error.name === 'QueryFailedError') {
        throw new NotFoundException('Failed to retrieve stock status data');
      }

      throw error;
    }
  }

  /**
   * Get all Gearment stock items without pagination
   * @param filterDto Filter parameters for stock items
   * @returns List of stock items matching the filter criteria
   */
  async getAllGearmentStockItems(
    filterDto: GearmentStockPaginatedQueryDto,
  ): Promise<StockStatusListResponseDto> {
    try {
      const { status, name, color, size, type } = filterDto;

      // Build where conditions based on filters
      const whereConditions: any = {};

      if (status) {
        whereConditions.status = status;
      }

      if (name) {
        whereConditions.name = ILike(`%${name}%`);
      }

      if (color) {
        whereConditions.color = ILike(`%${color}%`);
      }

      if (size) {
        whereConditions.size = ILike(`%${size}%`);
      }

      if (type) {
        whereConditions.type = ILike(`%${type}%`);
      }

      // Get all matching stock items without pagination
      const stockItems = await this.gearmentStockRepository.find({
        where: whereConditions,
        order: {
          updatedAt: 'DESC',
        },
      });

      // Map database entities to DTOs
      const resultItems: StockStatusItemDto[] = stockItems.map((item) => ({
        variant_id: item.variantId,
        color: item.color || '',
        hex_color_code: item.hexColorCode || '',
        size: item.size || '',
        name: item.name || '',
        type: item.type || '',
        status: item.status,
        last_synced_at: item.lastSyncedAt,
      }));

      // Build response
      const response: StockStatusListResponseDto = {
        status: 'success',
        message: 'Complete stock status list retrieved from database',
        result: resultItems,
      };

      return response;
    } catch (error) {
      this.logger.error(
        `Error retrieving complete stock status list from database: ${error.message}`,
        error.stack,
      );

      // Throw a more specific error for better error handling
      if (error.name === 'QueryFailedError') {
        throw new NotFoundException('Failed to retrieve stock status data');
      }

      throw error;
    }
  }

  /**
   * Process a batch of stock items
   * @param items Stock items to process
   * @param syncTime Sync time
   */
  private async processBatch(
    items: GearmentStockItem[],
    syncTime: Date,
  ): Promise<void> {
    const entities = await Promise.all(
      items.map(async (item) => {
        // Check if item already exists
        const existingItem = await this.gearmentStockRepository.findOne({
          where: { variantId: item.variant_id },
        });

        if (existingItem) {
          // Update existing item
          existingItem.color = item.color;
          existingItem.hexColorCode = item.hex_color_code;
          existingItem.size = item.size;
          existingItem.name = item.name;
          existingItem.status = item.status;
          existingItem.lastSyncedAt = syncTime;
          return existingItem;
        } else {
          // Create new item
          const newItem = new GearmentStock();
          newItem.variantId = item.variant_id;
          newItem.color = item.color;
          newItem.hexColorCode = item.hex_color_code;
          newItem.size = item.size;
          newItem.name = item.name;
          newItem.status = item.status;
          newItem.lastSyncedAt = syncTime;
          return newItem;
        }
      }),
    );

    // Save all entities in a single transaction
    await this.gearmentStockRepository.save(entities);
  }

  /**
   * Generate SKUs from Gearment stock data
   * @param gearmentStockType The type of Gearment stock to use
   * @returns Object containing both SKUs and attributes used to generate them
   */
  async generateSkusFromGearmentStock(gearmentStockType: string): Promise<{
    skus: any[];
    attributes: { name: string; valueNames: string[] }[];
  }> {
    try {
      this.logger.log(
        `Generating SKUs for gearmentStockType: ${gearmentStockType}`,
      );

      // Create filter DTO with the type
      const filterDto = new GearmentStockPaginatedQueryDto();
      filterDto.type = gearmentStockType;
      filterDto.status = 'in_stock'; // Only include in-stock items

      // Get all stock items of the specified type
      const stockResponse = await this.getAllGearmentStockItems(filterDto);

      if (
        !stockResponse ||
        !stockResponse.result ||
        stockResponse.result.length === 0
      ) {
        this.logger.warn(`No stock items found for type: ${gearmentStockType}`);
        throw new NotFoundException(
          `No stock items found for type: ${gearmentStockType}`,
        );
      }

      this.logger.log(
        `Found ${stockResponse.result.length} stock items for type: ${gearmentStockType}`,
      );

      // Extract unique colors and sizes
      const uniqueColors = new Set<string>();
      const uniqueSizes = new Set<string>();

      stockResponse.result.forEach((item) => {
        if (item.color) uniqueColors.add(item.color);
        if (item.size) uniqueSizes.add(item.size);
      });

      this.logger.log(
        `Found ${uniqueColors.size} unique colors and ${uniqueSizes.size} unique sizes`,
      );

      // Generate SKUs for all combinations of color and size
      const skus: any[] = [];

      // Convert Sets to Arrays for iteration
      const colors = Array.from(uniqueColors);
      const sizes = Array.from(uniqueSizes);

      // Create all combinations of color and size
      for (const color of colors) {
        for (const size of sizes) {
          // Check if this specific combination exists in stock
          const stockItem = stockResponse.result.find(
            (item) => item.color === color && item.size === size,
          );

          // Only add the combination if it exists in stock
          if (stockItem) {
            const sku = {
              salesAttributes: [
                {
                  name: 'color',
                  valueName: color,
                },
                {
                  name: 'size',
                  valueName: size,
                },
              ],
            };

            skus.push(sku);
          }
        }
      }

      this.logger.log(`Generated ${skus.length} SKUs`);

      // Create attributes array for the response
      const attributes = [
        {
          name: 'Color',
          valueNames: colors,
        },
        {
          name: 'Size',
          valueNames: sizes,
        },
      ];

      return {
        skus,
        attributes,
      };
    } catch (error) {
      this.logger.error(
        `Error generating SKUs from Gearment stock: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
