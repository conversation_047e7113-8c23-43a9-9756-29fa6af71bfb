import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { GearmentService } from './gearment.service';
import {
  GearmentStockPaginatedQueryDto,
  GearmentStockPaginatedResponseDto,
} from './dto/stock-status.dto';
import {
  GenerateSkusRequestDto,
  GenerateSkusResponseDto,
} from './dto/generate-skus.dto';
import { RoleGuard } from 'src/auth/guards/role.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/auth/enums/user-role.enum';

@ApiTags('providers/gearment')
@Controller('providers/gearment')
@UseGuards(RoleGuard)
export class GearmentController {
  private readonly logger = new Logger(GearmentController.name);

  constructor(private readonly gearmentService: GearmentService) {}

  @Get('stock-status')
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: 'Get stock status from database with standard pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Stock status retrieved successfully',
    type: GearmentStockPaginatedResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid query parameters',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: [
      'in_stock',
      'temporarily_unavailable',
      'time_expired',
      'out_of_stock',
      'discontinued',
      'unknown',
    ],
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter by product name',
  })
  @ApiQuery({
    name: 'color',
    required: false,
    type: String,
    description: 'Filter by color',
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: String,
    description: 'Filter by size',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    type: String,
    description: 'Filter by product type',
  })
  async getStockStatusPaginated(
    @Query() queryDto: GearmentStockPaginatedQueryDto,
  ): Promise<GearmentStockPaginatedResponseDto> {
    this.logger.log(
      `Getting paginated stock status with params: ${JSON.stringify(queryDto)}`,
    );
    return this.gearmentService.getGearmentStockStatusPaginated(queryDto);
  }

  @Post('sync-stock-status')
  @Roles(UserRole.ADMIN)
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Sync stock status from Gearment API to database' })
  @ApiResponse({
    status: 200,
    description: 'Stock status synced successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Stock status synced successfully',
        },
        syncedCount: { type: 'number', example: 250 },
      },
    },
  })
  async syncStockStatus(): Promise<{
    success: boolean;
    message: string;
    syncedCount: number;
  }> {
    const syncedCount = await this.gearmentService.syncStockStatus();
    return {
      success: true,
      message: 'Stock status synced successfully',
      syncedCount,
    };
  }

  @Post('generate-skus')
  @Roles(UserRole.ADMIN, UserRole.CLIENT)
  @ApiOperation({ summary: 'Generate SKUs based on Gearment stock data' })
  @ApiResponse({
    status: 200,
    description: 'The SKUs have been successfully generated.',
    type: GenerateSkusResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Gearment stock not found.' })
  async generateSkus(
    @Body() body: GenerateSkusRequestDto,
  ): Promise<GenerateSkusResponseDto> {
    this.logger.log(
      `Generating SKUs for gearmentStockType: ${body.gearmentStockType}`,
    );
    return this.gearmentService.generateSkusFromGearmentStock(
      body.gearmentStockType,
    );
  }
}
