import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';

/**
 * Entity representing stock status from Gearment provider
 */
@Entity('gearment_stock')
export class GearmentStock {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'variant_id', nullable: false })
  variantId: string;

  @Column({ nullable: true })
  color: string;

  @Column({ name: 'hex_color_code', nullable: true })
  hexColorCode: string;

  @Column({ nullable: true })
  size: string;

  @Column({ nullable: true })
  name: string;

  /**
   * Type extracted from the first word of the name
   */
  @Column({ nullable: true })
  type: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'unknown',
    transformer: {
      to: (value: string): string => {
        // List of valid status values
        const validStatuses = [
          'in_stock',
          'temporarily_unavailable',
          'time_expired',
          'out_of_stock',
          'discontinued',
        ];
        // If the value is in the valid list, use it; otherwise default to 'unknown'
        return validStatuses.includes(value) ? value : 'unknown';
      },
      from: (value: string): string => value,
    },
  })
  status: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'last_synced_at', type: 'timestamp', nullable: true })
  lastSyncedAt: Date;

  /**
   * Extract the type from the name before insert or update
   * Type is the first word of the name (text before the first space)
   */
  @BeforeInsert()
  @BeforeUpdate()
  extractTypeFromName() {
    if (this.name) {
      // Extract the first word (text before the first space)
      const firstSpace = this.name.indexOf(' ');
      this.type =
        firstSpace > 0 ? this.name.substring(0, firstSpace) : this.name;
    }
  }
}
