/**
 * Interface for stock status item from Gearment API
 */
export interface GearmentStockItem {
  variant_id: string;
  color: string;
  hex_color_code: string;
  size: string;
  name: string;
  status: string;
}

/**
 * Interface for pagination information from Gearment API
 */
export interface GearmentPagination {
  offset: number | null;
  limit: number | null;
}

/**
 * Interface for stock status response from Gearment API
 */
export interface GearmentStockResponse {
  status: string;
  message: string;
  result: GearmentStockItem[];
  paging: GearmentPagination;
}
