import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from '../../../common/dto/pagination-query.dto';
import { PaginatedResult } from '../../../common/interfaces/paginated-result.interface';

/**
 * DTO for stock status query parameters with standard pagination
 * Combines PaginationQueryDto with StockStatusFilterDto
 */
export class GearmentStockPaginatedQueryDto extends PaginationQueryDto {
  @ApiProperty({
    description: 'Filter by status',
    enum: [
      'in_stock',
      'temporarily_unavailable',
      'time_expired',
      'out_of_stock',
      'discontinued',
      'unknown',
    ],
    required: false,
  })
  @IsEnum(
    [
      'in_stock',
      'temporarily_unavailable',
      'time_expired',
      'out_of_stock',
      'discontinued',
      'unknown',
    ],
    {
      message:
        'Status must be one of: in_stock, temporarily_unavailable, time_expired, out_of_stock, discontinued, unknown',
    },
  )
  @IsOptional()
  status?: string;

  @ApiProperty({
    description: 'Filter by product name (case-insensitive partial match)',
    example: 'shirt',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Filter by color (case-insensitive partial match)',
    example: 'blue',
    required: false,
  })
  @IsString()
  @IsOptional()
  color?: string;

  @ApiProperty({
    description: 'Filter by size (exact match)',
    example: 'XL',
    required: false,
  })
  @IsString()
  @IsOptional()
  size?: string;

  @ApiProperty({
    description: 'Filter by product type (exact match)',
    example: 'YOUTH',
    required: false,
  })
  @IsString()
  @IsOptional()
  type?: string;
}

/**
 * DTO for stock status item response
 */
export class StockStatusItemDto {
  @ApiProperty({
    description: 'Variant ID',
    example: '13162',
  })
  @IsString()
  variant_id: string;

  @ApiProperty({
    description: 'Color name',
    example: 'Safety Pink',
  })
  @IsString()
  color: string;

  @ApiProperty({
    description: 'Hex color code',
    example: 'E16F8F',
  })
  @IsString()
  hex_color_code: string;

  @ApiProperty({
    description: 'Size',
    example: 'XS',
  })
  @IsString()
  size: string;

  @ApiProperty({
    description: 'Product name',
    example: 'YOUTH T-SHIRT SAFETY PINK XS',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Product type (first word of the name)',
    example: 'YOUTH',
    required: false,
    nullable: true,
  })
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({
    description: 'Stock status',
    enum: [
      'in_stock',
      'temporarily_unavailable',
      'time_expired',
      'out_of_stock',
      'discontinued',
      'unknown',
    ],
    example: 'in_stock',
  })
  @IsEnum([
    'in_stock',
    'temporarily_unavailable',
    'time_expired',
    'out_of_stock',
    'discontinued',
    'unknown',
  ])
  status: string;

  @ApiProperty({
    description: 'Last time the stock was synced with Gearment API',
    example: '2023-05-15T10:30:00Z',
    required: false,
    nullable: true,
  })
  last_synced_at: Date | null;
}

/**
 * DTO for stock status response without pagination
 */
export class StockStatusListResponseDto {
  @ApiProperty({
    description: 'Status of the response',
    example: 'success',
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Message from the API',
    example: '[API] Stock status list!',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Stock status items',
    type: [StockStatusItemDto],
  })
  result: StockStatusItemDto[];
}

/**
 * DTO for paginated stock status response that conforms to PaginatedResult interface
 */
export class GearmentStockPaginatedResponseDto
  implements PaginatedResult<StockStatusItemDto>
{
  @ApiProperty({
    description: 'Stock status items',
    type: [StockStatusItemDto],
  })
  data: StockStatusItemDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        description: 'Total number of items across all pages',
        example: 100,
      },
      itemCount: {
        type: 'number',
        description:
          'Actual number of items returned in this response (<= limit)',
        example: 10,
      },
      itemsPerPage: {
        type: 'number',
        description: 'Number of items per page',
        example: 10,
      },
      totalPages: {
        type: 'number',
        description: 'Total number of pages',
        example: 10,
      },
      currentPage: {
        type: 'number',
        description: 'Current page number',
        example: 1,
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
