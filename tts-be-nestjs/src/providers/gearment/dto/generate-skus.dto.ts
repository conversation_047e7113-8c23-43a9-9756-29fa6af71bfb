import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';

/**
 * DTO for a single attribute in the generate SKUs response
 */
export class AttributeDto {
  @ApiProperty({
    description: 'Name of the attribute (e.g., Color, Size)',
    example: 'Color',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'List of possible values for this attribute',
    example: ['White', 'Black', 'Grey'],
    type: [String],
  })
  @IsArray()
  valueNames: string[];
}

/**
 * DTO for a single sales attribute in a SKU
 */
export class SalesAttributeDto {
  @ApiProperty({
    description: 'Name of the attribute',
    example: 'color',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Value of the attribute',
    example: 'black',
  })
  @IsString()
  valueName: string;
}

/**
 * DTO for a single SKU in the generate SKUs response
 */
export class SkuDto {
  @ApiProperty({
    description: 'Sales attributes for this SKU',
    type: [SalesAttributeDto],
  })
  @IsArray()
  salesAttributes: SalesAttributeDto[];
}

/**
 * DTO for the generate SKUs request body
 */
export class GenerateSkusRequestDto {
  @ApiProperty({
    description: 'Type of Gearment stock to use for generating SKUs',
    example: 'YOUTH',
  })
  @IsString()
  gearmentStockType: string;
}

/**
 * DTO for the enhanced generate SKUs response
 */
export class GenerateSkusResponseDto {
  @ApiProperty({
    description: 'List of generated SKUs',
    type: [SkuDto],
  })
  @IsArray()
  skus: SkuDto[];

  @ApiProperty({
    description: 'List of attributes used to generate the SKUs',
    type: [AttributeDto],
  })
  @IsArray()
  attributes: AttributeDto[];
}
