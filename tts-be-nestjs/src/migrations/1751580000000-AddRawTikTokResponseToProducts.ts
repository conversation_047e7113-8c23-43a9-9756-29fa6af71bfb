import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRawTikTokResponseToProducts1751580000000 implements MigrationInterface {
  name = 'AddRawTikTokResponseToProducts1751580000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column already exists before adding it
    const table = await queryRunner.getTable('products');
    const column = table?.findColumnByName('rawTikTokResponse');
    
    if (!column) {
      await queryRunner.query(`
        ALTER TABLE "products" 
        ADD "rawTikTokResponse" jsonb
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if column exists before dropping it
    const table = await queryRunner.getTable('products');
    const column = table?.findColumnByName('rawTikTokResponse');
    
    if (column) {
      await queryRunner.query(`
        ALTER TABLE "products" 
        DROP COLUMN "rawTikTokResponse"
      `);
    }
  }
}
