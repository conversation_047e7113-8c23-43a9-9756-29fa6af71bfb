import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateOrdersTables1751570000000 implements MigrationInterface {
    name = 'CreateOrdersTables1751570000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create OrderStatus enum
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('UNPAID', 'ON_HOLD', 'AWAITING_SHIPMENT', 'PARTIALLY_SHIPPING', 'AWAITING_COLLECTION', 'IN_TRANSIT', 'DELIVERED', 'COMPLETED', 'CANCELLED')`);
        
        // Create orders table
        await queryRunner.query(`CREATE TABLE "orders" (
            "id" SERIAL NOT NULL, 
            "idTT" character varying, 
            "status" "public"."orders_status_enum", 
            "orderType" character varying, 
            "createTimeTT" integer, 
            "updateTimeTT" integer, 
            "paidTime" integer, 
            "buyerEmail" character varying, 
            "buyerMessage" text, 
            "userIdTT" character varying, 
            "payment" jsonb, 
            "paymentMethodName" character varying, 
            "recipientAddress" jsonb, 
            "fulfillmentType" character varying, 
            "shippingProvider" character varying, 
            "shippingProviderId" character varying, 
            "shippingType" character varying, 
            "trackingNumber" character varying, 
            "deliveryOptionId" character varying, 
            "deliveryOptionName" character varying, 
            "isCod" boolean NOT NULL DEFAULT false, 
            "isExchangeOrder" boolean NOT NULL DEFAULT false, 
            "isReplacementOrder" boolean NOT NULL DEFAULT false, 
            "isSampleOrder" boolean NOT NULL DEFAULT false, 
            "rawTikTokResponse" jsonb, 
            "tiktokShopId" integer NOT NULL, 
            "userId" integer, 
            "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "UQ_orders_id_tt" UNIQUE ("idTT"), 
            CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id")
        )`);

        // Create order_line_items table
        await queryRunner.query(`CREATE TABLE "order_line_items" (
            "id" SERIAL NOT NULL, 
            "idTT" character varying, 
            "productIdTT" character varying, 
            "skuIdTT" character varying, 
            "sellerSku" character varying, 
            "productName" character varying, 
            "skuName" character varying, 
            "skuImage" character varying, 
            "originalPrice" numeric(10,2), 
            "salePrice" numeric(10,2), 
            "currency" character varying, 
            "platformDiscount" numeric(10,2), 
            "sellerDiscount" numeric(10,2), 
            "packageId" character varying, 
            "packageStatus" character varying, 
            "displayStatus" character varying, 
            "isDangerousGood" boolean NOT NULL DEFAULT false, 
            "isGift" boolean NOT NULL DEFAULT false, 
            "itemTax" jsonb, 
            "combinedListingSkus" jsonb, 
            "quantity" integer DEFAULT 1, 
            "retailDeliveryFee" numeric(10,2), 
            "buyerServiceFee" numeric(10,2), 
            "smallOrderFee" numeric(10,2), 
            "shippingProviderId" character varying, 
            "shippingProviderName" character varying, 
            "trackingNumber" character varying, 
            "rtsTime" integer, 
            "cancelReason" character varying, 
            "cancelUser" character varying, 
            "skuType" character varying, 
            "handlingDurationDays" character varying, 
            "rawTikTokResponse" jsonb, 
            "orderId" integer NOT NULL, 
            "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "PK_27283ee631862266d0f1c680646" PRIMARY KEY ("id")
        )`);

        // Create indexes for performance
        await queryRunner.query(`CREATE INDEX "IDX_orders_id_tt" ON "orders" ("idTT")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_tiktok_shop_id" ON "orders" ("tiktokShopId")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_status" ON "orders" ("status")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_create_time_tt" ON "orders" ("createTimeTT")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_user_id" ON "orders" ("userId")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_paid_time" ON "orders" ("paidTime")`);
        await queryRunner.query(`CREATE INDEX "IDX_orders_update_time_tt" ON "orders" ("updateTimeTT")`);
        
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_order_id" ON "order_line_items" ("orderId")`);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_product_id_tt" ON "order_line_items" ("productIdTT")`);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_sku_id_tt" ON "order_line_items" ("skuIdTT")`);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_seller_sku" ON "order_line_items" ("sellerSku")`);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_package_id" ON "order_line_items" ("packageId")`);

        // Add foreign key constraints
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_tiktok_shop_id" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_user_id" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_line_items" ADD CONSTRAINT "FK_order_line_items_order_id" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "order_line_items" DROP CONSTRAINT "FK_order_line_items_order_id"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_user_id"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_tiktok_shop_id"`);

        // Drop indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_package_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_seller_sku"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_sku_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_product_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_order_id"`);
        
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_update_time_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_paid_time"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_create_time_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_tiktok_shop_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_id_tt"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE "order_line_items"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        
        // Drop enum
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
    }
}
