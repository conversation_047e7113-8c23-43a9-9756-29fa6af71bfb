import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1751562910011 implements MigrationInterface {
    name = 'InitialSchema1751562910011'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "warehouses" ("id" SERIAL NOT NULL, "idTT" character varying(25) NOT NULL, "name" character varying(255) NOT NULL, "effectStatus" character varying(20), "isDefault" boolean NOT NULL DEFAULT false, "subType" character varying(50), "type" character varying(50), "fullAddress" text, "addressLine1" character varying(255), "addressLine2" character varying(255), "city" character varying(100), "state" character varying(100), "postalCode" character varying(20), "region" character varying(100), "regionCode" character varying(20), "contactPerson" character varying(100), "phoneNumber" character varying(50), "geolocation" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "tiktokShopId" integer NOT NULL, CONSTRAINT "UQ_4120ef8d1439501e74ac904ff18" UNIQUE ("idTT"), CONSTRAINT "PK_56ae21ee2432b2270b48867e4be" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "skus" ("id" SERIAL NOT NULL, "idTT" character varying, "sellerSku" character varying(100) NOT NULL, "externalListPrices" jsonb, "inventory" jsonb, "identifierCode" jsonb, "extraIdentifierCodes" text, "salesAttributes" jsonb, "externalSkuId" character varying, "combinedSkus" jsonb, "globalListingPolicy" jsonb, "skuUnitCount" character varying, "externalUrls" text, "preSale" jsonb, "price" jsonb NOT NULL, "listPrice" jsonb, "productId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_a5c082bc626fd84232a03bdd48d" UNIQUE ("idTT"), CONSTRAINT "PK_334d59b0b01e5f2193966266e27" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."product_image_usecase_enum" AS ENUM('MAIN_IMAGE', 'ATTRIBUTE_IMAGE', 'DESCRIPTION_IMAGE', 'CERTIFICATION_IMAGE', 'SIZE_CHART_IMAGE')`);
        await queryRunner.query(`CREATE TABLE "product_image" ("id" SERIAL NOT NULL, "uri" character varying NOT NULL, "url" character varying NOT NULL, "width" integer NOT NULL, "height" integer NOT NULL, "useCase" "public"."product_image_usecase_enum" NOT NULL DEFAULT 'MAIN_IMAGE', "productId" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_99d98a80f57857d51b5f63c8240" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "oauth_accounts" ("id" SERIAL NOT NULL, "provider" character varying NOT NULL, "providerId" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "user_id" integer, CONSTRAINT "PK_710a81523f515b78f894e33bb10" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "refresh_tokens" ("id" SERIAL NOT NULL, "token" character varying NOT NULL, "userId" integer NOT NULL, "isRevoked" boolean NOT NULL, "expiresAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_4542dd2f38a61354a040ba9fd57" UNIQUE ("token"), CONSTRAINT "PK_7d8bee0204106019488c4c50ffa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('admin', 'client')`);
        await queryRunner.query(`CREATE TABLE "users" ("id" SERIAL NOT NULL, "email" character varying NOT NULL, "name" character varying, "image" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "role" "public"."users_role_enum" NOT NULL DEFAULT 'client', CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."products_status_enum" AS ENUM('DRAFT', 'PENDING', 'FAILED', 'ACTIVATE', 'SELLER_DEACTIVATED', 'PLATFORM_DEACTIVATED', 'FREEZE', 'DELETED', 'AUDITING')`);
        await queryRunner.query(`CREATE TABLE "products" ("id" SERIAL NOT NULL, "idTT" character varying, "category" character varying(100), "categoryChains" jsonb, "brandInfo" character varying(100), "brand" jsonb, "title" character varying(300) NOT NULL, "description" text, "sizeChart" jsonb, "productImages" text, "mainImages" jsonb, "videoInfo" jsonb, "packageLength" numeric(10,2), "packageWidth" numeric(10,2), "packageHeight" numeric(10,2), "packageDimensions" jsonb, "packageWeight" jsonb, "status" "public"."products_status_enum" NOT NULL DEFAULT 'DRAFT', "productSyncFailReasons" text, "recommendedCategories" jsonb, "isNotForSale" boolean NOT NULL DEFAULT false, "integratedPlatformStatuses" jsonb, "salesRegions" text, "audit" jsonb, "auditFailedReasons" jsonb, "listingQualityTier" character varying(20), "isCodAllowed" boolean DEFAULT false, "productAttributes" jsonb, "createTimeTT" integer, "updateTimeTT" integer, "deliveryOptions" jsonb, "externalProductId" character varying, "productTypes" text, "manufacturerIds" text, "responsiblePersonIds" text, "shippingInsuranceRequirement" character varying, "minimumOrderQuantity" integer, "isPreOwned" boolean DEFAULT false, "certifications" jsonb, "globalProductAssociation" jsonb, "tiktokShopId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer, CONSTRAINT "UQ_6f09fdcc62571327658e5bb091c" UNIQUE ("idTT"), CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."tiktok_shops_seller_type_enum" AS ENUM('CROSS_BORDER', 'LOCAL')`);
        await queryRunner.query(`CREATE TABLE "tiktok_shops" ("id" SERIAL NOT NULL, "idTT" character varying(25) NOT NULL, "name" character varying(255) NOT NULL, "friendly_name" character varying(255), "region" character varying(2) NOT NULL, "seller_type" "public"."tiktok_shops_seller_type_enum" NOT NULL, "cipher" character varying(255) NOT NULL, "code" character varying(50) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "last_refreshed_at" TIMESTAMP, "app_key" character varying(20) NOT NULL, "auth_code" text NOT NULL, "access_token" text NOT NULL, "access_token_expire_in" bigint NOT NULL, "refresh_token" text NOT NULL, "refresh_token_expire_in" bigint NOT NULL, "userId" integer, CONSTRAINT "UQ_276b02ceedca38c18d74c67154b" UNIQUE ("idTT"), CONSTRAINT "PK_1e87c3c4bcf3822ff46c8f00a54" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."tiktok_applications_app_type_enum" AS ENUM('Custom', 'Public')`);
        await queryRunner.query(`CREATE TABLE "tiktok_applications" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "idTT" character varying, "email" character varying NOT NULL, "app_key" character varying NOT NULL, "app_secret" character varying NOT NULL, "app_type" "public"."tiktok_applications_app_type_enum" NOT NULL, "redirect_url" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_faab3366be43a6a3863093b5e67" UNIQUE ("app_key"), CONSTRAINT "PK_92ac31eefb6b62422263e95bb47" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "categories" ("id" SERIAL NOT NULL, "idTT" character varying NOT NULL, "parentIdTT" character varying, "localName" character varying NOT NULL, "isLeaf" boolean NOT NULL, "version" character varying NOT NULL DEFAULT 'v2', "permissionStatuses" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_3837aafeba5c59d80483946f78f" UNIQUE ("idTT"), CONSTRAINT "PK_24dbc6126a28ff948da33e97d3b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "brands" ("id" SERIAL NOT NULL, "idTT" character varying NOT NULL, "name" character varying NOT NULL, "authorizedStatus" character varying, "status" character varying, "isT1Brand" boolean, "imageUrl" character varying, "authorizationLetterUrl" character varying, "authorizationStartDate" character varying, "authorizationEndDate" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_69adfbd48837e8e8751daf85270" UNIQUE ("idTT"), CONSTRAINT "PK_b0c437120b624da1034a81fc561" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "templates" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "description" text NOT NULL, "categoryId" integer NOT NULL, "brandId" integer NOT NULL, "isPublic" boolean NOT NULL DEFAULT false, "images" jsonb NOT NULL, "productAttributes" jsonb NOT NULL, "skus" jsonb NOT NULL, "sizeChart" jsonb, "packageDimensions" jsonb NOT NULL, "packageWeight" jsonb NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer NOT NULL, CONSTRAINT "PK_515948649ce0bbbe391de702ae5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "staged_products" ("id" SERIAL NOT NULL, "title" character varying(255) NOT NULL, "description" text NOT NULL, "categoryId" integer NOT NULL, "brandId" integer NOT NULL, "categoryVersion" character varying(10) NOT NULL DEFAULT 'v2', "packageDimensions" jsonb NOT NULL, "packageWeight" jsonb NOT NULL, "images" jsonb NOT NULL, "productAttributes" jsonb NOT NULL, "skus" jsonb NOT NULL, "sizeChart" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer, CONSTRAINT "PK_1af574ea5ee882921f9f45e2a04" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."product_uploads_status_enum" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')`);
        await queryRunner.query(`CREATE TABLE "product_uploads" ("id" SERIAL NOT NULL, "stagedProductId" integer NOT NULL, "tiktokShopId" integer NOT NULL, "status" "public"."product_uploads_status_enum" NOT NULL DEFAULT 'PENDING', "tiktokProductId" character varying, "errorMessage" text, "jobId" character varying, "progress" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "completedAt" TIMESTAMP, "processedImages" jsonb, "sizeChart" jsonb, "productId" integer, CONSTRAINT "PK_716597b54fa75ab069c2a48dfa1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "crawled_products" ("id" SERIAL NOT NULL, "title" character varying(500) NOT NULL, "productUrl" character varying(1000) NOT NULL, "marketplace" character varying(50) NOT NULL, "marketId" character varying(255) NOT NULL, "sellerName" character varying(255), "reviewCount" integer, "metadata" jsonb, "extractedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "isPublic" boolean NOT NULL DEFAULT false, "images" jsonb DEFAULT '[]', CONSTRAINT "UQ_e61ddfda320eb269457d400295c" UNIQUE ("marketId"), CONSTRAINT "PK_c5e746df8c60a0edab62c7ce247" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "crawl_schedules" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "keywords" text NOT NULL, "marketplace" character varying(50) NOT NULL, "frequencyMinutes" integer NOT NULL, "maxProductsPerRun" integer NOT NULL DEFAULT '20', "isActive" boolean NOT NULL DEFAULT true, "lastRun" TIMESTAMP, "nextRun" TIMESTAMP, "lastRunProductCount" integer NOT NULL DEFAULT '0', "lastRunStatus" character varying(50) NOT NULL DEFAULT 'pending', "lastRunError" character varying(500), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer NOT NULL, CONSTRAINT "PK_c266e639c740173a2196bd91081" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "attributes" ("id" SERIAL NOT NULL, "idTT" character varying NOT NULL, "name" character varying NOT NULL, "isRequired" boolean NOT NULL, "isSalesAttr" boolean NOT NULL, "inputType" character varying, "isMultipleSelection" boolean, "isCustomizable" boolean, "valueDataFormat" character varying, "type" character varying, "categoryIdTT" jsonb NOT NULL DEFAULT '[]', "values" jsonb DEFAULT '[]', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_147bfd9c73509df01622a6c7ef4" UNIQUE ("idTT"), CONSTRAINT "PK_32216e2e61830211d3a5d7fa72c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "magic_link_tokens" ("id" SERIAL NOT NULL, "email" character varying NOT NULL, "token" character varying NOT NULL, "expiresAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e97101929b9f3c7afc0920b0f17" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "gearment_stock" ("id" SERIAL NOT NULL, "variant_id" character varying NOT NULL, "color" character varying, "hex_color_code" character varying, "size" character varying, "name" character varying, "type" character varying, "status" character varying(50) NOT NULL DEFAULT 'unknown', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "last_synced_at" TIMESTAMP, CONSTRAINT "PK_f1cad9c2cd5eedb544840c64934" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "crawled_product_users" ("crawledProductId" integer NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_3115b565efc6c7c235f3efa8a3e" PRIMARY KEY ("crawledProductId", "userId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f9e4bba42dfd0362a20ae853c8" ON "crawled_product_users" ("crawledProductId") `);
        await queryRunner.query(`CREATE INDEX "IDX_ab640b0b275fa4eb025e557958" ON "crawled_product_users" ("userId") `);
        await queryRunner.query(`ALTER TABLE "skus" ADD CONSTRAINT "FK_7beba067c3aa6601fa17a0bda80" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_image" ADD CONSTRAINT "FK_40ca0cd115ef1ff35351bed8da2" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "oauth_accounts" ADD CONSTRAINT "FK_22a05e92f51a983475f9281d3b0" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" ADD CONSTRAINT "FK_610102b60fea1455310ccd299de" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "FK_19af35b672be933f5c6bfa90c73" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "FK_99d90c2a483d79f3b627fb1d5e9" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tiktok_shops" ADD CONSTRAINT "FK_ea11eb5e92548bea17bdc36c0f7" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "templates" ADD CONSTRAINT "FK_7193babbf16087eb6107606dfe3" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "templates" ADD CONSTRAINT "FK_d591d05a2b7699e76589f4fca41" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "templates" ADD CONSTRAINT "FK_e1317a376d1cc5ee38b494a7d7c" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staged_products" ADD CONSTRAINT "FK_b92d8cd07030f38c153d6bd2f94" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staged_products" ADD CONSTRAINT "FK_a9511d1ee9f013cac42e110aa9e" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "staged_products" ADD CONSTRAINT "FK_c865628cf675d160cc79f9a92d2" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_uploads" ADD CONSTRAINT "FK_7c27a6af3c8a7858bdcfba5357e" FOREIGN KEY ("stagedProductId") REFERENCES "staged_products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_uploads" ADD CONSTRAINT "FK_34859addcb1e934c3d7a4f48434" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "crawl_schedules" ADD CONSTRAINT "FK_c2ca5c6de0de4c9d5a88445548d" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "crawled_product_users" ADD CONSTRAINT "FK_f9e4bba42dfd0362a20ae853c80" FOREIGN KEY ("crawledProductId") REFERENCES "crawled_products"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "crawled_product_users" ADD CONSTRAINT "FK_ab640b0b275fa4eb025e5579587" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "crawled_product_users" DROP CONSTRAINT "FK_ab640b0b275fa4eb025e5579587"`);
        await queryRunner.query(`ALTER TABLE "crawled_product_users" DROP CONSTRAINT "FK_f9e4bba42dfd0362a20ae853c80"`);
        await queryRunner.query(`ALTER TABLE "crawl_schedules" DROP CONSTRAINT "FK_c2ca5c6de0de4c9d5a88445548d"`);
        await queryRunner.query(`ALTER TABLE "product_uploads" DROP CONSTRAINT "FK_34859addcb1e934c3d7a4f48434"`);
        await queryRunner.query(`ALTER TABLE "product_uploads" DROP CONSTRAINT "FK_7c27a6af3c8a7858bdcfba5357e"`);
        await queryRunner.query(`ALTER TABLE "staged_products" DROP CONSTRAINT "FK_c865628cf675d160cc79f9a92d2"`);
        await queryRunner.query(`ALTER TABLE "staged_products" DROP CONSTRAINT "FK_a9511d1ee9f013cac42e110aa9e"`);
        await queryRunner.query(`ALTER TABLE "staged_products" DROP CONSTRAINT "FK_b92d8cd07030f38c153d6bd2f94"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_e1317a376d1cc5ee38b494a7d7c"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_d591d05a2b7699e76589f4fca41"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_7193babbf16087eb6107606dfe3"`);
        await queryRunner.query(`ALTER TABLE "tiktok_shops" DROP CONSTRAINT "FK_ea11eb5e92548bea17bdc36c0f7"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_99d90c2a483d79f3b627fb1d5e9"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_19af35b672be933f5c6bfa90c73"`);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" DROP CONSTRAINT "FK_610102b60fea1455310ccd299de"`);
        await queryRunner.query(`ALTER TABLE "oauth_accounts" DROP CONSTRAINT "FK_22a05e92f51a983475f9281d3b0"`);
        await queryRunner.query(`ALTER TABLE "product_image" DROP CONSTRAINT "FK_40ca0cd115ef1ff35351bed8da2"`);
        await queryRunner.query(`ALTER TABLE "skus" DROP CONSTRAINT "FK_7beba067c3aa6601fa17a0bda80"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ab640b0b275fa4eb025e557958"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f9e4bba42dfd0362a20ae853c8"`);
        await queryRunner.query(`DROP TABLE "crawled_product_users"`);
        await queryRunner.query(`DROP TABLE "gearment_stock"`);
        await queryRunner.query(`DROP TABLE "magic_link_tokens"`);
        await queryRunner.query(`DROP TABLE "attributes"`);
        await queryRunner.query(`DROP TABLE "crawl_schedules"`);
        await queryRunner.query(`DROP TABLE "crawled_products"`);
        await queryRunner.query(`DROP TABLE "product_uploads"`);
        await queryRunner.query(`DROP TYPE "public"."product_uploads_status_enum"`);
        await queryRunner.query(`DROP TABLE "staged_products"`);
        await queryRunner.query(`DROP TABLE "templates"`);
        await queryRunner.query(`DROP TABLE "brands"`);
        await queryRunner.query(`DROP TABLE "categories"`);
        await queryRunner.query(`DROP TABLE "tiktok_applications"`);
        await queryRunner.query(`DROP TYPE "public"."tiktok_applications_app_type_enum"`);
        await queryRunner.query(`DROP TABLE "tiktok_shops"`);
        await queryRunner.query(`DROP TYPE "public"."tiktok_shops_seller_type_enum"`);
        await queryRunner.query(`DROP TABLE "products"`);
        await queryRunner.query(`DROP TYPE "public"."products_status_enum"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
        await queryRunner.query(`DROP TABLE "refresh_tokens"`);
        await queryRunner.query(`DROP TABLE "oauth_accounts"`);
        await queryRunner.query(`DROP TABLE "product_image"`);
        await queryRunner.query(`DROP TYPE "public"."product_image_usecase_enum"`);
        await queryRunner.query(`DROP TABLE "skus"`);
        await queryRunner.query(`DROP TABLE "warehouses"`);
    }

}
