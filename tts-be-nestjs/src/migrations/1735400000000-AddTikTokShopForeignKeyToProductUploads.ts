import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTikTokShopForeignKeyToProductUploads1735400000000
  implements MigrationInterface
{
  name = 'AddTikTokShopForeignKeyToProductUploads1735400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraint from product_uploads.tiktokShopId to tiktok_shops.id
    await queryRunner.query(`
      ALTER TABLE "product_uploads" 
      ADD CONSTRAINT "FK_product_uploads_tiktok_shop_id" 
      FOREIGN KEY ("tiktokShopId") 
      REFERENCES "tiktok_shops"("id") 
      ON DELETE CASCADE
    `);

    // Create index for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_product_uploads_tiktok_shop_id" 
      ON "product_uploads" ("tiktokShopId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the index first
    await queryRunner.query(`
      DROP INDEX "IDX_product_uploads_tiktok_shop_id"
    `);

    // Drop the foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "product_uploads" 
      DROP CONSTRAINT "FK_product_uploads_tiktok_shop_id"
    `);
  }
}
