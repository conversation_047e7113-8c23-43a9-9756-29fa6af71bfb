import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProcessedSkuImagesToProductUpload1737734500000 implements MigrationInterface {
  name = 'AddProcessedSkuImagesToProductUpload1737734500000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column already exists before adding it
    const table = await queryRunner.getTable('product_uploads');
    const column = table?.findColumnByName('processedSkuImages');
    
    if (!column) {
      await queryRunner.query(`
        ALTER TABLE "product_uploads" 
        ADD "processedSkuImages" jsonb
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if column exists before dropping it
    const table = await queryRunner.getTable('product_uploads');
    const column = table?.findColumnByName('processedSkuImages');
    
    if (column) {
      await queryRunner.query(`
        ALTER TABLE "product_uploads" 
        DROP COLUMN "processedSkuImages"
      `);
    }
  }
}
