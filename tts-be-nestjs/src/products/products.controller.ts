import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  ParseIntPipe,
  BadRequestException,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { ProductsService } from './products.service';
import { ProductResponseDto } from './dto/product-response.dto';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { FilterProductDto } from './dto/filter-product.dto';
import { PaginatedResult } from 'src/common/interfaces/paginated-result.interface';
import { SynchronizationResultDto } from './dto/synchronization-result.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { CurrentUser } from 'src/auth/decorators/current-user.decorator';

@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  /*
  All products will be created from tiktok shop, so we don't need to create product from here.
  @ApiOperation({ summary: 'Create new product' })
  @ApiResponse({ status: 201, description: 'New product created successfully.', type: Product })
  @Post()
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createProductDto: CreateProductDto): Promise<Product> {
    return this.productsService.create(createProductDto);
  }
  */

  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: 200, description: 'All products.', type: [ProductResponseDto] })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Search by product name',
  })
  @ApiQuery({
    name: 'minPrice',
    required: false,
    type: Number,
    description: 'Minimum price filter',
  })
  @ApiQuery({
    name: 'maxPrice',
    required: false,
    type: Number,
    description: 'Maximum price filter',
  })
  @Get()
  findAll(
    @Query() query: any,
    @CurrentUser('id') userId: number,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    const paginationQuery = plainToInstance(PaginationQueryDto, query);
    const filterDto = plainToInstance(FilterProductDto, query);
    const errors1 = validateSync(paginationQuery);
    const errors2 = validateSync(filterDto);

    if (errors1.length || errors2.length) {
      throw new BadRequestException([...errors1, ...errors2]);
    }
    return this.productsService.findAll(paginationQuery, filterDto, userId);
  }

  @ApiOperation({ summary: 'Get product detail' })
  @ApiResponse({ status: 200, description: 'Product details', type: ProductResponseDto })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @Get(':id')
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductResponseDto> {
    return this.productsService.findOne(id, userId);
  }

  /*
  All products will be updated from tiktok shop, so we don't need to update product from here.
  @ApiOperation({ summary: 'Update Product' })
  @ApiResponse({ status: 200, description: 'Product updated successfully', type: Product })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: UpdateProductDto
  ): Promise<Product> {
    return this.productsService.update(id, updateProductDto);
  }
  */

  /*
  All products will be deleted from tiktok shop, so we don't need to delete product from here.
  @ApiOperation({ summary: 'Delete Product' })
  @ApiResponse({ status: 204, description: 'Product deleted successfully' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.productsService.remove(id);
  }
  */

  @ApiOperation({ summary: 'Synchronize tiktok products' })
  @ApiResponse({
    status: 200,
    description: 'Synchronize tiktok products successfully',
    type: SynchronizationResultDto,
  })
  @ApiParam({
    name: 'tiktokShopId',
    description: 'TikTok Shop ID from internal system',
  })
  @Post('synchronize/:tiktokShopId')
  synchronize_tiktok(
    @Param('tiktokShopId', ParseIntPipe) tiktokShopId: number,
    @CurrentUser('id') userId: number,
  ): Promise<SynchronizationResultDto> {
    return this.productsService.synchronize_tiktok(tiktokShopId, userId);
  }

  @ApiOperation({ summary: 'Synchronize tiktok product detail' })
  @ApiResponse({
    status: 200,
    description: 'Synchronize tiktok product detail successfully',
    type: ProductResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Product not found' })
  @ApiParam({ name: 'id', description: 'Product ID from internal system' })
  @Post('synchronize-detail/:id')
  synchronize_detail_tiktok(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductResponseDto> {
    return this.productsService.synchronize_detail_tiktok(id, userId);
  }

  @ApiOperation({ summary: 'Export products to CSV' })
  @ApiResponse({
    status: 200,
    description: 'Products exported successfully',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @Post('export')
  async exportProducts(
    @Body() filterDto: FilterProductDto,
    @CurrentUser('id') userId: number,
    @Res() res: Response,
  ): Promise<void> {
    const csvData = await this.productsService.exportProducts(filterDto, userId);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="products-export-${new Date().toISOString().split('T')[0]}.csv"`);
    res.send(csvData);
  }
}
