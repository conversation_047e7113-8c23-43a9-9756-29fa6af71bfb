import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { Express } from 'express';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
// TikTokShop entity is now accessed through TikTokShopService
import {
  ImageUseCase,
  UploadImageDto,
  UploadImageResponseDto,
} from '../dto/upload-image.dto';
import {
  R2UploadRequestDto,
  R2PresignedUrlResponseDto,
  R2UploadResponseDto,
} from '../dto/r2-upload.dto';
import * as fs from 'fs';
import { RequestFile } from '../../../tts_sdk/model/models';
import { TempFileStorageService } from 'src/common/services/temp-file-storage.service';
import { TikTokShopService } from 'src/tiktok-shop/tiktok-shop.service';
import { CloudStorageService } from 'src/common/cloud-storage/cloud-storage.service';
import { v4 as uuid } from 'uuid';

@Injectable()
export class ImageUploadService {
  private readonly logger = new Logger(ImageUploadService.name);

  constructor(
    private readonly clientFactory: TikTokClientFactory,
    private readonly tempFileStorageService: TempFileStorageService,
    private readonly tikTokShopService: TikTokShopService,
    private readonly cloudStorageService: CloudStorageService,
  ) {}

  /**
   * Upload an image to TikTok Shop
   * @param tiktokShop TikTok Shop entity
   * @param uploadImageDto Image upload data
   * @returns Uploaded image information
   */
  async uploadImage(
    tiktokShopId: number,
    uploadImageDto: UploadImageDto,
  ): Promise<UploadImageResponseDto> {
    // Find the TikTokShop by ID
    const tiktokShop =
      await this.tikTokShopService.findTikTokShopById(tiktokShopId);

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }
    this.logger.log(`Uploading image for TikTok Shop ID: ${tiktokShop.id}`);

    try {
      // Create a temporary file for the image
      const imageFile = await this.prepareImageFile(uploadImageDto);

      // Create client using the app_key from the TikTokShop
      const client = await this.clientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Upload the image to TikTok Shop
      const result = await client.api.ProductV202309Api.ImagesUploadPost(
        tiktokShop.access_token,
        'multipart/form-data',
        imageFile,
        uploadImageDto.useCase,
        { headers: {} },
      );

      // Clean up the temporary file if it was created
      if (imageFile && typeof imageFile === 'string') {
        this.cleanupTempFile(imageFile, uploadImageDto.r2Key);
      } else if (
        imageFile &&
        typeof imageFile === 'object' &&
        imageFile instanceof fs.ReadStream
      ) {
        const readStream = imageFile;
        if (readStream.path && typeof readStream.path === 'string') {
          this.cleanupTempFile(readStream.path, uploadImageDto.r2Key);
        }
      }

      // Check if the upload was successful
      if (result.body?.code !== 0 || !result.body?.data) {
        throw new BadRequestException(
          `Failed to upload image: ${result.body?.message || 'Unknown error'}`,
        );
      }

      // Return the uploaded image information
      if (
        !result.body.data.uri ||
        !result.body.data.url ||
        result.body.data.width === undefined ||
        result.body.data.height === undefined
      ) {
        throw new BadRequestException('Invalid response from TikTok API');
      }

      const response: UploadImageResponseDto = {
        uri: result.body.data.uri,
        url: result.body.data.url,
        width: result.body.data.width,
        height: result.body.data.height,
        useCase:
          (result.body.data.useCase as ImageUseCase) || ImageUseCase.MAIN_IMAGE,
      };

      // Include the R2 key in the response if available
      if (uploadImageDto.r2Key) {
        response.r2Key = uploadImageDto.r2Key;
      }

      return response;
    } catch (error) {
      this.logger.error(`Error uploading image: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to upload image: ${error.message}`);
    }
  }

  /**
   * Prepare an image file for upload
   * @param uploadImageDto Image upload data
   * @returns Image file for upload
   */
  private async prepareImageFile(
    uploadImageDto: UploadImageDto,
  ): Promise<RequestFile> {
    try {
      let tempFilePath: string;

      // First, try to use R2 storage if we have r2Key
      if (uploadImageDto.r2Key) {
        try {
          // Download the file from R2 to a temporary location
          const signedUrl = await this.cloudStorageService.getSignedUrl(
            uploadImageDto.r2Key,
          );
          tempFilePath =
            await this.tempFileStorageService.storeImageFromUrl(signedUrl);
          this.logger.debug(
            `Downloaded file from R2 to temp file: ${tempFilePath}`,
          );
          return this.tempFileStorageService.createReadStream(tempFilePath);
        } catch (r2Error) {
          this.logger.error(
            `Error accessing R2 object: ${r2Error.message}`,
            r2Error.stack,
          );
          // Fall back to other methods if R2 access fails
        }
      }

      // Check if we have image data, URL, or a temp file path
      if (uploadImageDto.tempFilePath) {
        // If we already have a temp file path, use it directly
        tempFilePath = uploadImageDto.tempFilePath;
        this.logger.debug(`Using existing temp file: ${tempFilePath}`);
      } else if (uploadImageDto.imageData) {
        // Try to upload to R2 first
        try {
          const shopId = uploadImageDto.shopId || 'unknown';
          const imageKey = `tiktok-uploads/${shopId}/${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

          // Upload to R2
          const uploadResult = await this.cloudStorageService.uploadBase64Data(
            uploadImageDto.imageData,
            imageKey,
          );

          // Store the R2 key for future reference
          uploadImageDto.r2Key = uploadResult.key;

          // Download to a temp file for TikTok upload
          tempFilePath = await this.tempFileStorageService.storeImageFromUrl(
            uploadResult.url,
          );
          this.logger.debug(
            `Uploaded to R2 and created temp file: ${tempFilePath}`,
          );
        } catch (r2Error) {
          this.logger.error(
            `Failed to use R2, falling back to temp storage: ${r2Error.message}`,
          );
          // Fall back to temp storage
          tempFilePath = await this.tempFileStorageService.storeBase64Image(
            uploadImageDto.imageData,
          );
          this.logger.debug(
            `Created temp file from base64 data (fallback): ${tempFilePath}`,
          );
        }
      } else if (uploadImageDto.imageUrl) {
        // Download image from URL and store as a temp file
        tempFilePath = await this.tempFileStorageService.storeImageFromUrl(
          uploadImageDto.imageUrl,
        );
        this.logger.debug(`Created temp file from URL: ${tempFilePath}`);
      } else {
        throw new BadRequestException(
          'Either imageData, imageUrl, r2Key, or tempFilePath must be provided',
        );
      }

      // Create a read stream from the temp file
      return this.tempFileStorageService.createReadStream(tempFilePath);
    } catch (error) {
      this.logger.error(
        `Error preparing image file: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to prepare image file: ${error.message}`,
      );
    }
  }

  /**
   * Clean up a temporary file and optionally an R2 object
   * @param filePath Path to the temporary file
   * @param r2Key Optional R2 key to delete
   */
  private cleanupTempFile(filePath: string, r2Key?: string): void {
    try {
      // Use the TempFileStorageService to delete the file
      this.tempFileStorageService.deleteTempFile(filePath);

      // If we have an R2 key, delete the object from R2 as well
      if (r2Key) {
        this.cloudStorageService
          .deleteFile(r2Key)
          .then(() => this.logger.debug(`Deleted R2 object: ${r2Key}`))
          .catch((error) =>
            this.logger.warn(
              `Failed to delete R2 object ${r2Key}: ${error.message}`,
            ),
          );
      }
    } catch (error) {
      this.logger.warn(
        `Failed to clean up temporary file ${filePath}: ${error.message}`,
      );
    }
  }

  // The findTikTokShop method has been replaced by TikTokShopService.findTikTokShopById

  /**
   * Upload a file directly to R2 storage
   * @param file The file to upload
   * @param folder The folder to store the file in
   * @returns Information about the uploaded file
   */
  async uploadFileToR2(
    file: Express.Multer.File,
    folder: string = 'uploads',
  ): Promise<R2UploadResponseDto> {
    try {
      // Validate file type and size
      await this.tempFileStorageService.validateImageBuffer(file.buffer);

      // Generate a key for the file
      const key = `${folder}/${Date.now()}-${uuid()}-${file.originalname}`;

      // Upload to R2
      const result = await this.cloudStorageService.uploadFile(
        file.buffer,
        key,
        file.mimetype,
      );

      return {
        key: result.key,
        url: result.url,
        filename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
      };
    } catch (error) {
      this.logger.error(
        `Error uploading file to R2: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to upload file to R2: ${error.message}`,
      );
    }
  }

  /**
   * Generate a presigned URL for client-side uploads to R2
   * @param dto The request DTO containing file information
   * @returns Presigned URL information
   */
  async generatePresignedUrl(
    dto: R2UploadRequestDto,
  ): Promise<R2PresignedUrlResponseDto> {
    try {
      this.logger.log(
        `Generating presigned URL for ${dto.fileName} in folder ${dto.folder || 'uploads'}`,
      );

      // Generate a key for the file
      const key = `${dto.folder || 'uploads'}/${Date.now()}-${uuid()}-${dto.fileName}`;

      // Generate a presigned URL for uploading
      const presignedUrl =
        await this.cloudStorageService.generatePresignedUploadUrl(
          key,
          dto.contentType,
          3600, // 1 hour expiration
        );

      return {
        key: key,
        presignedUrl: presignedUrl,
        publicUrl: `${this.cloudStorageService.getPublicUrl()}/${key}`,
        expiresIn: 3600,
      };
    } catch (error) {
      this.logger.error(
        `Error generating presigned URL: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to generate presigned URL: ${error.message}`,
      );
    }
  }
}
