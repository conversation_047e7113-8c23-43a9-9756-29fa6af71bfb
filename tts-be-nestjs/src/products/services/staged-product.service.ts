import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StagedProduct } from '../entities/staged-product.entity';
import { Template } from '../entities/template.entity';
import {
  CreateStagedProductDto,
  CreateStagedProductFromTemplateDto,
  CreateStagedProductFromTemplateAndUploadDto,
  StagedProductResponseDto,
  UpdateStagedProductDto,
} from '../dto/staged-product.dto';
import { ProductUploadResponseDto } from '../dto/product-upload.dto';
import {
  StagedProductQueryDto,
  StagedProductWithLatestUploadDto,
} from '../dto/staged-product-query.dto';
import { SortDirection } from '../../common/dto/pagination-query.dto';
import { TempFileStorageService } from 'src/common/services/temp-file-storage.service';
import { CloudStorageService } from 'src/common/cloud-storage/cloud-storage.service';
import { ProductUploadService } from './product-upload.service';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@Injectable()
export class StagedProductService {
  private readonly logger = new Logger(StagedProductService.name);

  constructor(
    @InjectRepository(StagedProduct)
    private readonly stagedProductRepository: Repository<StagedProduct>,
    @InjectRepository(Template)
    private readonly templateRepository: Repository<Template>,
    // Note: TikTokShop and ImageUploadService dependencies removed
    // If needed in the future, they should be added back
    private readonly tempFileStorageService: TempFileStorageService,
    private readonly cloudStorageService: CloudStorageService,
    private readonly productUploadService: ProductUploadService,
  ) {}

  /**
   * Create a new staged product
   * @param createStagedProductDto Create staged product DTO
   * @param userId User ID
   * @returns Created staged product
   */
  async createStagedProduct(
    createStagedProductDto: CreateStagedProductDto,
    userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(`Creating new staged product for user: ${userId}`);

    // Create staged product entity
    const stagedProduct = new StagedProduct();
    stagedProduct.title = createStagedProductDto.title;
    stagedProduct.description = createStagedProductDto.description;
    stagedProduct.categoryId = createStagedProductDto.categoryId;
    stagedProduct.brandId = createStagedProductDto.brandId;
    stagedProduct.categoryVersion =
      createStagedProductDto.categoryVersion || 'v2'; // Default to v2 for US market
    stagedProduct.packageDimensions = createStagedProductDto.packageDimensions;
    stagedProduct.packageWeight = createStagedProductDto.packageWeight;
    stagedProduct.userId = userId; // Set the userId

    // Process images - with direct R2 upload, we just store the imageUrl and r2Key
    stagedProduct.images = createStagedProductDto.images.map((image) => {
      return {
        imageUrl: image.imageUrl,
        r2Key: image.r2Key,
      };
    });

    // Store complete attribute information including names
    stagedProduct.productAttributes = createStagedProductDto.productAttributes || [];
    stagedProduct.skus = createStagedProductDto.skus;

    // Handle size chart if provided
    if (createStagedProductDto.sizeChart) {
      const sizeChart = createStagedProductDto.sizeChart;

      // With direct R2 upload, we just store the imageUrl and r2Key
      stagedProduct.sizeChart = {
        imageUrl: sizeChart.imageUrl,
        r2Key: sizeChart.r2Key,
      };
    }

    // Save staged product
    const savedStagedProduct =
      await this.stagedProductRepository.save(stagedProduct);
    this.logger.log(`Staged product created with ID: ${savedStagedProduct.id}`);

    // Map to response DTO
    return this.mapToResponseDto(savedStagedProduct);
  }

  /**
   * Create a new staged product from a template
   * @param createFromTemplateDto Create staged product from template DTO
   * @param userId User ID
   * @returns Created staged product
   */
  async createStagedProductFromTemplate(
    createFromTemplateDto: CreateStagedProductFromTemplateDto,
    userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Creating staged product from template ${createFromTemplateDto.templateId} for user: ${userId}`,
    );

    try {
      // First try to find a template owned by the user
      let template = await this.templateRepository.findOne({
        where: { id: createFromTemplateDto.templateId, userId },
        relations: ['category', 'brand'],
      });

      // If not found, check if there's a public template with this id
      if (!template) {
        template = await this.templateRepository.findOne({
          where: { id: createFromTemplateDto.templateId, isPublic: true },
          relations: ['category', 'brand'],
        });
      }

      if (!template) {
        throw new NotFoundException(
          `Template with ID ${createFromTemplateDto.templateId} not found`,
        );
      }

      const stagedProduct = this.stagedProductRepository.create({
        title: createFromTemplateDto.title,
        description: template.description,
        categoryId: template.categoryId,
        brandId: template.brandId,
        categoryVersion: 'v2', // Default to v2 for US market
        packageDimensions: template.packageDimensions,
        packageWeight: template.packageWeight,
        images: [...(createFromTemplateDto.images || []), ...(template.images || [])], // Combine images from both sources: first from DTO, then from template
        // Store complete attribute information including names
        productAttributes: template.productAttributes || [],
        skus: template.skus,
        sizeChart: template.sizeChart,
        userId,
      });

      const savedStagedProduct = await this.stagedProductRepository.save(stagedProduct);

      this.logger.log(
        `Staged product created from template with ID: ${savedStagedProduct.id}`,
      );

      // Map to response DTO
      return this.mapToResponseDto(savedStagedProduct);
    } catch (error) {
      this.logger.error(
        `Error creating staged product from template: ${error.message}`,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.message === 'Template not found') {
        throw new NotFoundException(
          `Template with ID ${createFromTemplateDto.templateId} not found`,
        );
      }
      throw error;
    }
  }

  /**
   * Create a new staged product from a template and immediately upload to TikTok Shop
   * @param createFromTemplateAndUploadDto Create staged product from template and upload DTO
   * @param userId User ID
   * @returns Created product upload
   */
  async createStagedProductFromTemplateAndUpload(
    createFromTemplateAndUploadDto: CreateStagedProductFromTemplateAndUploadDto,
    userId: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Creating staged product from template ${createFromTemplateAndUploadDto.templateId} and uploading to shop ${createFromTemplateAndUploadDto.tiktokShopId} for user: ${userId}`,
    );

    try {
      // Step 1: Create staged product from template
      const stagedProductData: CreateStagedProductFromTemplateDto = {
        templateId: createFromTemplateAndUploadDto.templateId,
        title: createFromTemplateAndUploadDto.title,
        images: createFromTemplateAndUploadDto.images,
      };

      const stagedProduct = await this.createStagedProductFromTemplate(
        stagedProductData,
        userId,
      );

      this.logger.log(
        `Staged product created with ID: ${stagedProduct.id}, now creating upload`,
      );

      // Step 2: Create product upload
      const uploadData = {
        stagedProductId: stagedProduct.id,
        tiktokShopId: createFromTemplateAndUploadDto.tiktokShopId,
      };

      const uploadOptions = {
        skipValidation: createFromTemplateAndUploadDto.skipValidation || false,
        forceUpload: createFromTemplateAndUploadDto.forceUpload || false,
      };

      const productUpload = await this.productUploadService.createProductUpload(
        uploadData,
        uploadOptions,
        userId,
      );

      this.logger.log(
        `Product upload created with ID: ${productUpload.id} for staged product: ${stagedProduct.id}`,
      );

      return productUpload;
    } catch (error) {
      this.logger.error(
        `Error creating staged product from template and uploading: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get a staged product by ID
   * @param id Staged product ID
   * @param userId User ID
   * @returns Staged product
   */
  async getStagedProductById(
    id: number,
    userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Getting staged product with ID: ${id} for user: ${userId}`,
    );

    const stagedProduct = await this.stagedProductRepository.findOne({
      where: { id, userId },
      relations: ['category', 'brand'],
    });
    if (!stagedProduct) {
      throw new NotFoundException(`Staged product with ID ${id} not found`);
    }

    return this.mapToResponseDto(stagedProduct);
  }

  /**
   * Get all staged products with pagination, filtering, and sorting
   * @param queryDto Query parameters for pagination, filtering, and sorting
   * @param userId User ID
   * @returns Paginated list of staged products with latest upload status
   */
  async getAllStagedProducts(
    queryDto: StagedProductQueryDto,
    userId: number,
  ): Promise<PaginatedResult<StagedProductWithLatestUploadDto>> {
    this.logger.log(
      `Getting staged products for user: ${userId} with query: ${JSON.stringify(queryDto)}`,
    );

    // Create query builder
    const queryBuilder = this.stagedProductRepository
      .createQueryBuilder('stagedProduct')
      .leftJoinAndSelect('stagedProduct.category', 'category')
      .leftJoinAndSelect('stagedProduct.brand', 'brand');

    // Apply user ID filter (security)
    queryBuilder.where('stagedProduct.userId = :userId', { userId });

    // Apply filters if provided
    if (queryDto.title) {
      queryBuilder.andWhere('stagedProduct.title ILIKE :title', {
        title: `%${queryDto.title}%`,
      });
    }

    if (queryDto.categoryId) {
      queryBuilder.andWhere('stagedProduct.categoryId = :categoryId', {
        categoryId: queryDto.categoryId,
      });
    }

    if (queryDto.brandId) {
      queryBuilder.andWhere('stagedProduct.brandId = :brandId', {
        brandId: queryDto.brandId,
      });
    }

    if (queryDto.updatedAfter) {
      queryBuilder.andWhere('stagedProduct.updatedAt >= :updatedAfter', {
        updatedAfter: queryDto.updatedAfter,
      });
    }

    if (queryDto.updatedBefore) {
      queryBuilder.andWhere('stagedProduct.updatedAt <= :updatedBefore', {
        updatedBefore: queryDto.updatedBefore,
      });
    }

    // Get total count
    const totalItems = await queryBuilder.getCount();

    // Apply sorting
    const sortField = queryDto.sortField || 'updatedAt';
    const sortDirection = queryDto.sortDirection || SortDirection.DESC;
    queryBuilder.orderBy(
      `stagedProduct.${sortField}`,
      sortDirection === 'asc' ? 'ASC' : 'DESC',
    );

    // Get pagination parameters
    const page = queryDto.page || 1;
    const limit = queryDto.limit || 20;
    const skip = (page - 1) * limit;

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const stagedProducts = await queryBuilder.getMany();

    // Map to response DTOs
    const stagedProductDtos = stagedProducts.map((stagedProduct) =>
      this.mapToResponseDto(stagedProduct),
    );

    // Get latest upload status for each staged product
    const data = await Promise.all(
      stagedProductDtos.map(async (stagedProductDto) => {
        try {
          // Get latest upload for this staged product
          const uploads =
            await this.productUploadService.getProductUploadsByProductId(
              stagedProductDto.id,
              userId,
            );

          // If there are uploads, include the latest one
          if (uploads && uploads.length > 0) {
            const latestUpload = uploads[0]; // Uploads are already sorted by createdAt DESC

            return {
              product: stagedProductDto,
              latestUpload: {
                id: latestUpload.id,
                status: latestUpload.status,
                tiktokShopId: latestUpload.tiktokShopId,
                createdAt: latestUpload.createdAt,
                completedAt: latestUpload.completedAt,
              },
            };
          }

          // If no uploads, just return the product
          return {
            product: stagedProductDto,
            latestUpload: null,
          };
        } catch (error) {
          this.logger.error(
            `Error getting latest upload for staged product ${stagedProductDto.id}: ${error.message}`,
          );
          return {
            product: stagedProductDto,
            latestUpload: null,
          };
        }
      }),
    );

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalItems / limit);

    // Return paginated response with the new format
    return {
      data,
      meta: {
        totalItems,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Update a staged product
   * @param id Staged product ID
   * @param updateStagedProductDto Update staged product DTO
   * @param userId User ID
   * @returns Updated staged product
   */
  async updateStagedProduct(
    id: number,
    updateStagedProductDto: UpdateStagedProductDto,
    userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Updating staged product with ID: ${id} for user: ${userId}`,
    );

    // Get staged product
    const stagedProduct = await this.stagedProductRepository.findOne({
      where: { id, userId },
      relations: ['category', 'brand'],
    });
    if (!stagedProduct) {
      throw new NotFoundException(`Staged product with ID ${id} not found`);
    }

    // Update fields if provided
    if (updateStagedProductDto.title !== undefined) {
      stagedProduct.title = updateStagedProductDto.title;
    }

    if (updateStagedProductDto.description !== undefined) {
      stagedProduct.description = updateStagedProductDto.description;
    }

    if (updateStagedProductDto.categoryId !== undefined) {
      stagedProduct.categoryId = updateStagedProductDto.categoryId;
    }

    if (updateStagedProductDto.brandId !== undefined) {
      stagedProduct.brandId = updateStagedProductDto.brandId;
    }

    if (updateStagedProductDto.categoryVersion !== undefined) {
      stagedProduct.categoryVersion = updateStagedProductDto.categoryVersion;
    }

    if (updateStagedProductDto.packageDimensions !== undefined) {
      stagedProduct.packageDimensions =
        updateStagedProductDto.packageDimensions;
    }

    if (updateStagedProductDto.packageWeight !== undefined) {
      stagedProduct.packageWeight = updateStagedProductDto.packageWeight;
    }

    // Update images if provided
    if (updateStagedProductDto.images !== undefined) {
      // With direct R2 upload, we just store the imageUrl and r2Key
      stagedProduct.images = updateStagedProductDto.images.map((image) => {
        return {
          imageUrl: image.imageUrl,
          r2Key: image.r2Key,
        };
      });
    }

    // Update product attributes if provided
    if (updateStagedProductDto.productAttributes !== undefined) {
      // Store complete attribute information including names
      stagedProduct.productAttributes = updateStagedProductDto.productAttributes || [];
    }

    // Update SKUs if provided
    if (updateStagedProductDto.skus !== undefined) {
      // Process SKUs and set default currency if not provided
      stagedProduct.skus = updateStagedProductDto.skus.map((sku) => ({
        ...sku,
        price: {
          ...sku.price,
          currency: sku.price.currency || 'USD',
        },
        listPrice: sku.listPrice
          ? {
              ...sku.listPrice,
              currency: sku.listPrice.currency || 'USD',
            }
          : sku.listPrice,
      }));
    }

    // Update size chart if provided
    if (updateStagedProductDto.sizeChart !== undefined) {
      const sizeChart = updateStagedProductDto.sizeChart;

      // With direct R2 upload, we just store the imageUrl and r2Key
      stagedProduct.sizeChart = {
        imageUrl: sizeChart.imageUrl,
        r2Key: sizeChart.r2Key,
      };
    }

    // Save updated staged product
    const updatedStagedProduct =
      await this.stagedProductRepository.save(stagedProduct);
    this.logger.log(`Staged product with ID: ${id} updated`);

    // Map to response DTO
    return this.mapToResponseDto(updatedStagedProduct);
  }

  /**
   * Delete a staged product
   * @param id Staged product ID
   * @param userId User ID
   */
  async deleteStagedProduct(id: number, userId: number): Promise<void> {
    this.logger.log(
      `Deleting staged product with ID: ${id} for user: ${userId}`,
    );

    const stagedProduct = await this.stagedProductRepository.findOne({
      where: { id, userId },
      relations: ['category', 'brand'],
    });
    if (!stagedProduct) {
      throw new NotFoundException(`Staged product with ID ${id} not found`);
    }

    // Clean up any temporary files
    this.cleanupTempFiles(stagedProduct);

    await this.stagedProductRepository.remove(stagedProduct);
    this.logger.log(`Staged product with ID: ${id} deleted`);
  }

  /**
   * Clean up files associated with a staged product
   * @param stagedProduct Staged product entity
   */
  private cleanupTempFiles(stagedProduct: StagedProduct): void {
    // Clean up image files
    if (stagedProduct.images) {
      for (const image of stagedProduct.images) {
        // Clean up R2 objects
        if ('r2Key' in image && image.r2Key) {
          try {
            this.cloudStorageService
              .deleteFile(image.r2Key)
              .then(() =>
                this.logger.debug(`Deleted R2 object: ${image.r2Key}`),
              )
              .catch((error) =>
                this.logger.warn(
                  `Failed to delete R2 object ${image.r2Key}: ${error.message}`,
                ),
              );
          } catch (error) {
            this.logger.warn(
              `Failed to delete R2 object ${image.r2Key}: ${error.message}`,
            );
          }
        }
      }
    }

    // Clean up size chart files
    if (stagedProduct.sizeChart) {
      const sizeChart = stagedProduct.sizeChart;

      // Clean up R2 object
      if ('r2Key' in sizeChart && sizeChart.r2Key) {
        try {
          this.cloudStorageService
            .deleteFile(sizeChart.r2Key)
            .then(() =>
              this.logger.debug(
                `Deleted size chart R2 object: ${sizeChart.r2Key}`,
              ),
            )
            .catch((error) =>
              this.logger.warn(
                `Failed to delete size chart R2 object ${sizeChart.r2Key}: ${error.message}`,
              ),
            );
        } catch (error) {
          this.logger.warn(
            `Failed to delete size chart R2 object ${sizeChart.r2Key}: ${error.message}`,
          );
        }
      }
    }
  }

  /**
   * Get upload history for a specific staged product
   * @param id Staged product ID
   * @param userId User ID
   * @returns List of product uploads for the staged product
   */
  async getStagedProductUploads(
    id: number,
    userId: number,
  ): Promise<ProductUploadResponseDto[]> {
    this.logger.log(
      `Getting upload history for staged product with ID: ${id} for user: ${userId}`,
    );

    // First check if the staged product exists and belongs to the user
    const stagedProduct = await this.stagedProductRepository.findOne({
      where: { id, userId },
      relations: ['category', 'brand'],
    });
    if (!stagedProduct) {
      throw new NotFoundException(`Staged product with ID ${id} not found`);
    }

    // Use ProductUploadService to get uploads for this staged product
    return this.productUploadService.getProductUploadsByProductId(id, userId);
  }

  /**
   * Map staged product entity to response DTO
   * @param stagedProduct Staged product entity
   * @returns Staged product response DTO
   */
  private mapToResponseDto(
    stagedProduct: StagedProduct,
  ): StagedProductResponseDto {
    return {
      id: stagedProduct.id,
      title: stagedProduct.title,
      description: stagedProduct.description,
      categoryId: stagedProduct.categoryId,
      brandId: stagedProduct.brandId,
      categoryVersion: stagedProduct.categoryVersion,
      packageDimensions: stagedProduct.packageDimensions,
      packageWeight: stagedProduct.packageWeight,
      images: stagedProduct.images,
      productAttributes: stagedProduct.productAttributes,
      skus: stagedProduct.skus,
      sizeChart: stagedProduct.sizeChart,
      createdAt: stagedProduct.createdAt,
      updatedAt: stagedProduct.updatedAt,
      category: stagedProduct.category ? {
        id: stagedProduct.category.id,
        idTT: stagedProduct.category.idTT,
        localName: stagedProduct.category.localName,
        isLeaf: stagedProduct.category.isLeaf,
        version: stagedProduct.category.version,
        permissionStatuses: stagedProduct.category.permissionStatuses,
      } : undefined,
      brand: stagedProduct.brand ? {
        id: stagedProduct.brand.id,
        idTT: stagedProduct.brand.idTT,
        name: stagedProduct.brand.name,
        authorizedStatus: stagedProduct.brand.authorizedStatus,
        brandStatus: stagedProduct.brand.status,
        isT1Brand: stagedProduct.brand.isT1Brand,
        imageUrl: stagedProduct.brand.imageUrl,
      } : undefined,
      // Note: We don't include userId in the response DTO to keep it hidden from frontend
    };
  }
}
