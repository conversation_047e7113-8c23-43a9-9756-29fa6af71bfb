import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, Not } from 'typeorm';
import { CrawlSchedule } from '../entities/crawl-schedule.entity';
import { CreateCrawlScheduleDto, UpdateCrawlScheduleDto } from '../dto/create-crawl-schedule.dto';

@Injectable()
export class CrawlScheduleService {
  constructor(
    @InjectRepository(CrawlSchedule)
    private crawlScheduleRepository: Repository<CrawlSchedule>,
  ) {}

  async create(
    createCrawlScheduleDto: CreateCrawlScheduleDto,
    userId: number,
  ): Promise<CrawlSchedule> {
    const crawlSchedule = this.crawlScheduleRepository.create({
      ...createCrawlScheduleDto,
      userId,
      maxProductsPerRun: createCrawlScheduleDto.maxProductsPerRun ?? 20,
      isActive: createCrawlScheduleDto.isActive ?? true,
    });

    // Calculate next run time
    if (crawlSchedule.isActive) {
      crawlSchedule.nextRun = new Date(Date.now());
    }

    return this.crawlScheduleRepository.save(crawlSchedule);
  }

  async findAll(userId: number): Promise<CrawlSchedule[]> {
    return this.crawlScheduleRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number, userId: number): Promise<CrawlSchedule> {
    const schedule = await this.crawlScheduleRepository.findOne({
      where: { id, userId },
    });

    if (!schedule) {
      throw new NotFoundException(`Crawl schedule with ID ${id} not found`);
    }

    return schedule;
  }

  async update(
    id: number,
    updateCrawlScheduleDto: UpdateCrawlScheduleDto,
    userId: number,
  ): Promise<CrawlSchedule> {
    const schedule = await this.findOne(id, userId);

    Object.assign(schedule, updateCrawlScheduleDto);

    // Recalculate next run time if frequency changed or schedule was activated
    if (
      updateCrawlScheduleDto.frequencyMinutes ||
      (updateCrawlScheduleDto.isActive && !schedule.isActive)
    ) {
      if (schedule.isActive) {
        schedule.nextRun = new Date(
          Date.now() + schedule.frequencyMinutes * 60 * 1000,
        );
      } else {
        schedule.nextRun = null;
      }
    }

    return this.crawlScheduleRepository.save(schedule);
  }

  async remove(id: number, userId: number): Promise<void> {
    const schedule = await this.findOne(id, userId);
    await this.crawlScheduleRepository.remove(schedule);
  }

  async findDueSchedules(): Promise<CrawlSchedule[]> {
    const now = new Date();
    return this.crawlScheduleRepository.find({
      where: {
        isActive: true,
        nextRun: LessThanOrEqual(now),
      },
      relations: ['user'],
    });
  }

  // New methods for automation support
  async getReadySchedules(userId: number): Promise<CrawlSchedule[]> {
    const now = new Date();
    return this.crawlScheduleRepository.find({
      where: {
        userId,
        isActive: true,
        lastRunStatus: Not('running'),
        nextRun: LessThanOrEqual(now),
      },
      order: { nextRun: 'ASC' },
    });
  }

  async getNextAvailableSchedule(userId: number): Promise<CrawlSchedule | null> {
    const now = new Date();

    // Find the next available schedule that is not currently running
    const schedule = await this.crawlScheduleRepository.findOne({
      where: {
        userId,
        isActive: true,
        lastRunStatus: Not('running'),
        nextRun: LessThanOrEqual(now),
      },
      order: { nextRun: 'ASC' },
    });

    if (!schedule) {
      return null;
    }

    // Immediately claim the schedule by setting status to 'running'
    schedule.lastRunStatus = 'running';
    schedule.lastRun = new Date();
    await this.crawlScheduleRepository.save(schedule);

    return schedule;
  }

  async toggleActive(id: number, userId: number): Promise<CrawlSchedule> {
    const schedule = await this.findOne(id, userId);
    schedule.isActive = !schedule.isActive;

    if (schedule.isActive) {
      // Set next run time when activating
      schedule.nextRun = new Date(Date.now());
    } else {
      // Clear next run time when deactivating
      schedule.nextRun = null;
      if (schedule.lastRunStatus === 'running') {
        schedule.lastRunStatus = 'pending';
      }
    }

    return this.crawlScheduleRepository.save(schedule);
  }

  async updateScheduleStatus(
    id: number,
    status: string,
    productCount: number = 0,
    error?: string,
    userId?: number,
  ): Promise<void> {
    const whereCondition: any = { id };
    if (userId) {
      whereCondition.userId = userId;
    }

    const schedule = await this.crawlScheduleRepository.findOne({
      where: whereCondition,
    });

    if (!schedule) {
      throw new NotFoundException(`Crawl schedule with ID ${id} not found`);
    }

    schedule.lastRunStatus = status;
    schedule.lastRunProductCount = productCount;
    schedule.lastRunError = error || '';

    if (status === 'running') {
      schedule.lastRun = new Date();
    } 
    // Calculate next run time if schedule is still active
    if (schedule.isActive && status === 'completed') {
      schedule.nextRun = new Date(
        Date.now() + schedule.frequencyMinutes * 60 * 1000,
      );
    }

    await this.crawlScheduleRepository.save(schedule);
  }

  async updateScheduleProgress(
    id: number,
    progress: { current: number; total: number },
    userId?: number,
  ): Promise<void> {
    const whereCondition: any = { id };
    if (userId) {
      whereCondition.userId = userId;
    }

    const schedule = await this.crawlScheduleRepository.findOne({
      where: whereCondition,
    });

    if (!schedule) {
      throw new NotFoundException(`Crawl schedule with ID ${id} not found`);
    }

    // For now, we'll store progress in the lastRunProductCount field
    // In a more advanced implementation, you might want to add dedicated progress fields
    schedule.lastRunProductCount = progress.current;

    await this.crawlScheduleRepository.save(schedule);
  }
}
