import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Template } from '../entities/template.entity';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';

import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { TemplateQueryDto } from '../dto/template-query.dto';

@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);

  constructor(
    @InjectRepository(Template)
    private templateRepository: Repository<Template>,
  ) {}

  async create(
    createTemplateDto: CreateTemplateDto,
    userId: number,
  ): Promise<Template> {
    // Store complete attribute information including names
    const template = this.templateRepository.create({
      ...createTemplateDto,
      userId,
    });
    return this.templateRepository.save(template);
  }

  async findAll(
    userId: number,
    templateQuery: TemplateQueryDto,
  ): Promise<PaginatedResult<Template>> {
    const {
      limit = 20,
      page = 1,
      sortField = 'updatedAt',
      sortDirection = 'desc',
      name,
    } = templateQuery;

    // Calculate the skip value based on page and limit
    const skip = (page - 1) * limit;

    // Create query builder for more flexibility with sorting
    const queryBuilder = this.templateRepository
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.category', 'category')
      .leftJoinAndSelect('template.brand', 'brand')
      .where('(template.userId = :userId OR template.isPublic = :isPublic)', {
        userId,
        isPublic: true,
      })
      .skip(skip)
      .take(limit);

    // Apply name filter if provided (case-insensitive partial matching)
    if (name) {
      queryBuilder.andWhere('template.name ILIKE :name', {
        name: `%${name}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(
      `template.${sortField}`,
      sortDirection === 'asc' ? 'ASC' : 'DESC',
    );

    // Execute query
    const [templates, total] = await queryBuilder.getManyAndCount();

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return {
      data: templates,
      meta: {
        totalItems: total,
        itemCount: templates.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  async findOne(id: number, userId: number): Promise<Template> {
    // First try to find a template owned by the user
    let template = await this.templateRepository.findOne({
      where: { id, userId },
      relations: ['category', 'brand'],
    });

    // If not found, check if there's a public template with this id
    if (!template) {
      template = await this.templateRepository.findOne({
        where: { id, isPublic: true },
        relations: ['category', 'brand'],
      });
    }

    if (!template) {
      throw new Error('Template not found');
    }

    return template;
  }

  async update(
    id: number,
    updateTemplateDto: UpdateTemplateDto,
    userId: number,
  ): Promise<Template> {
    // Only the owner can update a template, even if it's public
    const template = await this.templateRepository.findOne({
      where: { id, userId },
    });

    if (!template) {
      throw new Error(
        'Template not found or you do not have permission to update it',
      );
    }

    Object.assign(template, updateTemplateDto);
    return this.templateRepository.save(template);
  }

  async remove(id: number, userId: number): Promise<void> {
    // Only the owner can delete a template, even if it's public
    const template = await this.templateRepository.findOne({
      where: { id, userId },
    });

    if (!template) {
      throw new Error(
        'Template not found or you do not have permission to delete it',
      );
    }

    await this.templateRepository.remove(template);
  }
}
