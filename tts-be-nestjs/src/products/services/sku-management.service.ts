import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Sku } from '../entities/sku.entity';
import { Product } from '../entities/product.entity';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import {
  UpdateSkuInventoryDto,
  UpdateSkuInventoryResponseDto,
} from '../dto/update-sku-inventory.dto';
import { SkuDto, SkuResponseDto } from '../dto/sku.dto';
import { getProperty } from 'src/common/utils/property-name.util';

@Injectable()
export class SkuManagementService {
  private readonly logger = new Logger(SkuManagementService.name);

  constructor(
    @InjectRepository(Sku)
    private readonly skuRepository: Repository<Sku>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    private readonly tikTokClientFactory: TikTokClientFactory,
  ) {}

  /**
   * Update SKU inventory
   * @param id SKU ID
   * @param updateSkuInventoryDto Update SKU inventory DTO
   * @returns Updated SKU inventory
   */
  async updateSkuInventory(
    id: number,
    updateSkuInventoryDto: UpdateSkuInventoryDto,
  ): Promise<UpdateSkuInventoryResponseDto> {
    this.logger.log(`Updating inventory for SKU ID: ${id}`);

    // Get SKU
    const sku = await this.skuRepository.findOne({ where: { id } });
    if (!sku) {
      throw new NotFoundException(`SKU with ID ${id} not found`);
    }

    // Get product
    const product = await this.productRepository.findOne({
      where: { id: sku.productId },
    });
    if (!product) {
      throw new NotFoundException(`Product with ID ${sku.productId} not found`);
    }

    // Get TikTok Shop
    const tiktokShop = await this.tikTokShopRepository.findOne({
      where: { id: product.tiktokShopId },
    });
    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${product.tiktokShopId} not found`,
      );
    }

    try {
      // Create TikTok client
      const client = await this.tikTokClientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Prepare inventory update data
      const inventoryUpdates = updateSkuInventoryDto.inventory.map((item) => ({
        warehouse_id: item.warehouseId || '',
        quantity: item.quantity,
      }));

      // Update inventory in TikTok Shop
      if (sku.idTT) {
        const result =
          await client.api.ProductV202309Api.ProductsProductIdInventoryUpdatePost(
            product.idTT,
            tiktokShop.access_token,
            'application/json',
            tiktokShop.cipher,
            {
              skus: [
                {
                  id: sku.idTT,
                  inventory: inventoryUpdates,
                },
              ],
            },
            { headers: {} },
          );

        // Check if the request was successful
        if (result.body?.code !== 0) {
          throw new BadRequestException(
            `Failed to update inventory: ${result.body?.message || 'Unknown error'}`,
          );
        }
      } else {
        this.logger.warn(
          `SKU with ID ${id} does not have a TikTok Shop ID (idTT). Updating only local inventory.`,
        );
      }

      // Update inventory in database
      sku.inventory = updateSkuInventoryDto.inventory.map((item) => ({
        quantity: item.quantity,
        warehouseId: item.warehouseId || '',
      }));

      // Save updated SKU
      await this.skuRepository.save(sku);

      return {
        success: true,
        message: 'Inventory updated successfully',
        inventory: updateSkuInventoryDto.inventory,
      };
    } catch (error) {
      this.logger.error(
        `Error updating inventory for SKU ${id}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to update inventory: ${error.message}`,
      );
    }
  }
}
