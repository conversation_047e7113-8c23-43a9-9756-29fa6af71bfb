import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { CategoryDto, CategoryResponseDto } from '../dto/category.dto';
import { BrandDto, BrandResponseDto } from '../dto/brand.dto';
import { AttributeDto, AttributeResponseDto } from '../dto/attribute.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { Category } from '../entities/category.entity';
import { Brand } from '../entities/brand.entity';
import { Attribute } from '../entities/attribute.entity';
import { IAttributeValue } from '../interfaces/attribute-value.interface';
import { getProperty } from '../../common/utils/property-name.util';
import { executeInTransaction } from '../../common/utils/transaction.util';
import { DataSource } from 'typeorm';

@Injectable()
export class ProductMetadataService {
  private readonly logger = new Logger(ProductMetadataService.name);

  constructor(
    private readonly clientFactory: TikTokClientFactory,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    @InjectRepository(Attribute)
    private readonly attributeRepository: Repository<Attribute>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get TikTok Shop by ID
   * @param tiktokShopId TikTok Shop ID
   * @returns TikTok Shop entity
   */
  private async getTikTokShop(tiktokShopId: number): Promise<TikTokShop> {
    const tiktokShop = await this.tikTokShopRepository.findOne({
      where: { id: tiktokShopId },
    });
    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }
    return tiktokShop;
  }

  // The synchronizeCategories method has been moved to CategoryQueueProcessor
  // and is now handled as a background job

  /**
   * Synchronize brands from TikTok Shop
   * @param tiktokShopId TikTok Shop ID
   * @param brandName Brand Name (optional)
   * @param categoryId Category ID (optional)
   * @returns List of brands
   */
  async synchronizeBrands(
    tiktokShopId: number,
    brandName?: string,
    categoryId?: string,
  ): Promise<BrandResponseDto> {
    this.logger.log(
      `Synchronizing brands for TikTok Shop ID: ${tiktokShopId}, categoryId: ${categoryId || 'all'} with brandName: ${brandName || 'all'}`,
    );

    try {
      // Get TikTok Shop
      const tiktokShop = await this.getTikTokShop(tiktokShopId);

      // Create client
      const client = await this.clientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Call TikTok API to get brands
      const result = await client.api.ProductV202309Api.BrandsGet(
        100, // pageSize - maximum number of results per page
        tiktokShop.access_token,
        'application/json',
        categoryId, // categoryId (optional)
        false, // isAuthorized - return all brands
        brandName, // brandName (optional)
        undefined, // pageToken (optional)
        'v2', // categoryVersion - using v2 for US shops
        tiktokShop.cipher,
        { headers: {} },
      );

      // Check if the request was successful
      if (result.body?.code !== 0 || !result.body?.data?.brands) {
        throw new BadRequestException(
          `Failed to get brands: ${result.body?.message || 'Unknown error'}`,
        );
      }

      // Transform the response
      const apiBrands = result.body.data.brands
        .map((brand: any) => {
          // Get properties using getProperty utility to handle both camelCase and snake_case
          const id = getProperty(brand, 'id');
          const name = getProperty(brand, 'name');
          const authorizedStatus = getProperty(brand, 'authorizedStatus');
          const status = getProperty(brand, 'brandStatus');
          const isT1Brand = getProperty(brand, 'isT1Brand');

          // Check if required properties are missing
          if (!id || !name) {
            this.logger.error(
              `Skipping brand with missing required properties: ${JSON.stringify(brand, null, 2)}`,
            );
            return null; // Return null to filter out this brand
          }

          return {
            idTT: id,
            name: name,
            authorizedStatus: authorizedStatus,
            status: status,
            isT1Brand: isT1Brand,
          };
        })
        .filter((brand) => brand !== null); // Filter out null brands

      // Save brands to database
      const savedBrands = await this.saveBrandsToDatabase(apiBrands);

      // Convert to DTOs
      const brands: BrandDto[] = savedBrands.map((brand: Brand) => ({
        id: brand.id,
        idTT: brand.idTT,
        name: brand.name,
        authorizedStatus: brand.authorizedStatus,
        brandStatus: brand.status,
        isT1Brand: brand.isT1Brand,
        imageUrl: brand.imageUrl,
        authorizationLetterUrl: brand.authorizationLetterUrl,
        authorizationStartDate: brand.authorizationStartDate,
        authorizationEndDate: brand.authorizationEndDate,
      }));

      return { brands };
    } catch (error) {
      this.logger.error(`Error getting brands: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get brands: ${error.message}`);
    }
  }

  /**
   * Synchronize attributes from TikTok Shop and save them to the database
   * @param tiktokShopId TikTok Shop ID
   * @param categoryIdTT Category ID Tiktok
   * @returns List of attributes
   */
  async synchronizeAttributes(
    tiktokShopId: number,
    categoryIdTT: string,
  ): Promise<AttributeResponseDto> {
    this.logger.log(
      `Synchronizing attributes for TikTok Shop ID: ${tiktokShopId}, categoryIdTT: ${categoryIdTT}`,
    );

    // Validate categoryIdTT is provided
    if (!categoryIdTT) {
      this.logger.error(
        'Cannot synchronize attributes: categoryIdTT is required but was not provided',
      );
      throw new BadRequestException(
        'Category ID is required to synchronize attributes',
      );
    }

    try {
      // Get TikTok Shop
      const tiktokShop = await this.getTikTokShop(tiktokShopId);

      // Create client
      const client = await this.clientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Call TikTok API to get attributes
      const result =
        await client.api.ProductV202309Api.CategoriesCategoryIdAttributesGet(
          categoryIdTT,
          tiktokShop.access_token,
          'application/json',
          undefined, // locale (optional)
          'v2', // categoryVersion - using v2 for US shops
          tiktokShop.cipher,
          { headers: {} },
        );

      // Check if the request was successful
      if (result.body?.code !== 0 || !result.body?.data?.attributes) {
        throw new BadRequestException(
          `Failed to get attributes: ${result.body?.message || 'Unknown error'}`,
        );
      }

      // Transform the response
      const apiAttributes = result.body.data.attributes
        .map((attr: any) => {
          // Get properties using getProperty utility to handle both camelCase and snake_case
          const id = getProperty(attr, 'id');
          const name = getProperty(attr, 'name');
          const isRequired = getProperty(attr, 'isRequired', false);
          const type = getProperty(attr, 'type');
          const inputType = getProperty(attr, 'inputType');
          const isMultipleSelection = getProperty(attr, 'isMultipleSelection');
          const isCustomizable = getProperty(attr, 'isCustomizable');
          const valueDataFormat = getProperty(attr, 'valueDataFormat');
          const values = getProperty(attr, 'values', []);

          // Check if required properties are missing
          if (!id || !name) {
            this.logger.error(
              `Skipping attribute with missing required properties: ${JSON.stringify(attr, null, 2)}`,
            );
            return null; // Return null to filter out this attribute
          }

          // Process values
          const processedValues: IAttributeValue[] = values
            ? values
                .map((val: any) => {
                  const valId = getProperty(val, 'id');
                  const valName = getProperty(val, 'name');

                  if (!valId || !valName) {
                    this.logger.error(
                      `Skipping attribute value with missing required properties: ${JSON.stringify(val, null, 2)}`,
                    );
                    return null;
                  }

                  return {
                    idTT: valId,
                    name: valName,
                  };
                })
                .filter((val): val is IAttributeValue => val !== null)
            : [];

          return {
            idTT: id,
            name: name,
            isRequired: isRequired,
            isSalesAttr: type === 'SALES_PROPERTY',
            inputType: inputType,
            isMultipleSelection: isMultipleSelection,
            isCustomizable: isCustomizable,
            valueDataFormat: valueDataFormat,
            type: type,
            categoryIdTT: [categoryIdTT],
            values: processedValues,
          };
        })
        .filter((attr) => attr !== null); // Filter out null attributes

      // Save attributes to database
      const savedAttributes =
        await this.saveAttributesToDatabase(apiAttributes);

      // Convert to DTOs
      const attributes: AttributeDto[] = savedAttributes.map(
        (attr: Attribute) => ({
          id: attr.id,
          idTT: attr.idTT,
          name: attr.name,
          isRequired: attr.isRequired,
          isSalesAttr: attr.isSalesAttr,
          inputType: attr.inputType,
          isMultipleSelection: attr.isMultipleSelection,
          isCustomizable: attr.isCustomizable,
          valueDataFormat: attr.valueDataFormat,
          type: attr.type,
          categoryIdTT: attr.categoryIdTT,
          values: attr.values || [],
        }),
      );

      return { attributes };
    } catch (error) {
      this.logger.error(
        `Error getting attributes: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to get attributes: ${error.message}`,
      );
    }
  }

  // The saveCategoriesToDatabase method has been moved to CategoryQueueProcessor
  /**
   * Save brands to database
   * @param brands Brands to save
   * @returns Saved brands
   */
  private async saveBrandsToDatabase(brands: any[]): Promise<Brand[]> {
    this.logger.log(`Saving ${brands.length} brands to database`);

    const savedBrands: Brand[] = [];

    for (const brandData of brands) {
      try {
        // Check if brand already exists
        let brand = await this.brandRepository.findOne({
          where: { idTT: brandData.idTT },
        });

        if (brand) {
          // Update existing brand
          brand.name = brandData.name;
          brand.authorizedStatus = brandData.authorizedStatus;
          brand.status = brandData.status;
          brand.isT1Brand = brandData.isT1Brand;
          // Only update other fields if they are provided
          if (brandData.imageUrl) brand.imageUrl = brandData.imageUrl;
          if (brandData.authorizationLetterUrl)
            brand.authorizationLetterUrl = brandData.authorizationLetterUrl;
          if (brandData.authorizationStartDate)
            brand.authorizationStartDate = brandData.authorizationStartDate;
          if (brandData.authorizationEndDate)
            brand.authorizationEndDate = brandData.authorizationEndDate;
        } else {
          // Create new brand
          brand = new Brand();
          brand.idTT = brandData.idTT;
          brand.name = brandData.name;
          brand.authorizedStatus = brandData.authorizedStatus;
          brand.status = brandData.status;
          brand.isT1Brand = brandData.isT1Brand;
          brand.imageUrl = brandData.imageUrl || null;
          brand.authorizationLetterUrl =
            brandData.authorizationLetterUrl || null;
          brand.authorizationStartDate =
            brandData.authorizationStartDate || null;
          brand.authorizationEndDate = brandData.authorizationEndDate || null;
        }

        // Save brand
        const savedBrand = await this.brandRepository.save(brand);
        savedBrands.push(savedBrand);
      } catch (error) {
        this.logger.error(
          `Error saving brand ${brandData.idTT}: ${error.message}`,
          error.stack,
        );
        // Continue with next brand
      }
    }

    this.logger.log(`Successfully saved ${savedBrands.length} brands`);
    return savedBrands;
  }

  /**
   * Save attributes to database
   * @param attributes Attributes to save
   * @returns Saved attributes
   */
  private async saveAttributesToDatabase(
    attributes: any[],
  ): Promise<Attribute[]> {
    this.logger.log(`Saving ${attributes.length} attributes to database`);

    // Create a map to deduplicate attributes by idTT
    const attributeMap = new Map<string, any>();
    for (const attr of attributes) {
      attributeMap.set(attr.idTT, attr);
    }

    // Get deduplicated attributes
    const uniqueAttributes = Array.from(attributeMap.values());
    this.logger.log(
      `Processing ${uniqueAttributes.length} unique attributes after deduplication`,
    );

    // Process attributes one by one in separate transactions
    // This ensures that if one attribute fails, others can still be processed
    const savedAttributes: Attribute[] = [];

    for (const attributeData of uniqueAttributes) {
      try {
        // Process each attribute in its own transaction
        const result = await executeInTransaction(
          this.dataSource,
          async (entityManager) => {
            // Find or create attribute
            let attribute = await entityManager.findOne(Attribute, {
              where: { idTT: attributeData.idTT },
            });

            if (attribute) {
              // Update existing attribute
              this.logger.debug(
                `Updating existing attribute: ${attributeData.idTT}`,
              );
              attribute.name = attributeData.name;
              attribute.isRequired = attributeData.isRequired;
              attribute.isSalesAttr = attributeData.isSalesAttr;
              attribute.inputType = attributeData.inputType;
              attribute.isMultipleSelection = attributeData.isMultipleSelection;
              attribute.isCustomizable = attributeData.isCustomizable;
              attribute.valueDataFormat = attributeData.valueDataFormat;
              attribute.type = attributeData.type;
              // If the attribute already exists, we need to check if the categoryIdTT already contains the new category
              // First ensure categoryIdTT is an array
              const existingCategories = Array.isArray(attribute.categoryIdTT)
                ? attribute.categoryIdTT
                : [];
              const newCategories = Array.isArray(attributeData.categoryIdTT)
                ? attributeData.categoryIdTT
                : [];

              // Merge categories without duplicates
              const uniqueCategories = [
                ...new Set([...existingCategories, ...newCategories]),
              ];
              attribute.categoryIdTT = uniqueCategories;
              attribute.values = attributeData.values || [];
            } else {
              // Create new attribute
              this.logger.debug(
                `Creating new attribute: ${attributeData.idTT}`,
              );
              attribute = entityManager.create(Attribute, {
                idTT: attributeData.idTT,
                name: attributeData.name,
                isRequired: attributeData.isRequired,
                isSalesAttr: attributeData.isSalesAttr,
                inputType: attributeData.inputType,
                isMultipleSelection: attributeData.isMultipleSelection,
                isCustomizable: attributeData.isCustomizable,
                valueDataFormat: attributeData.valueDataFormat,
                type: attributeData.type,
                categoryIdTT: attributeData.categoryIdTT,
                values: attributeData.values || [],
              });
            }

            // Save attribute
            attribute = await entityManager.save(Attribute, attribute);

            return attribute;
          },
          this.logger,
        );

        if (result) {
          savedAttributes.push(result);
        }
      } catch (error) {
        this.logger.error(
          `Error processing attribute ${attributeData.idTT}: ${error.message}`,
        );
        // Continue with next attribute
      }
    }

    this.logger.log(`Successfully saved ${savedAttributes.length} attributes`);
    return savedAttributes;
  }

  /**
   * Get attributes from database with optional filters
   * @param categoryIdTT Category ID Tiktok (optional)
   * @param isRequired Filter by isRequired (optional)
   * @param isSalesAttr Filter by isSalesAttr (optional)
   * @returns List of attributes
   */
  async getAttributes(
    categoryIdTT?: string,
    isRequired?: boolean,
    isSalesAttr?: boolean,
  ): Promise<AttributeResponseDto> {
    this.logger.log(
      `Getting attributes from database with filters: categoryIdTT=${categoryIdTT || 'any'}, isRequired=${isRequired ?? 'any'}, isSalesAttr=${isSalesAttr ?? 'any'}`,
    );

    try {
      // Start building the query
      let queryBuilder =
        this.attributeRepository.createQueryBuilder('attribute');

      // Add filters
      if (categoryIdTT) {
        // Use PostgreSQL's JSONB containment operator for array check
        queryBuilder = queryBuilder.andWhere(
          `attribute.categoryIdTT @> :categoryIds`,
          {
            categoryIds: JSON.stringify([categoryIdTT]),
          },
        );
      }

      if (isRequired !== undefined) {
        queryBuilder = queryBuilder.andWhere(
          'attribute.isRequired = :isRequired',
          { isRequired },
        );
      }

      if (isSalesAttr !== undefined) {
        queryBuilder = queryBuilder.andWhere(
          'attribute.isSalesAttr = :isSalesAttr',
          { isSalesAttr },
        );
      }

      // Execute the query
      const attributes = await queryBuilder.getMany();

      this.logger.log(
        `Found ${attributes.length} attributes matching the provided filters`,
      );

      // Convert to DTOs
      const attributeDtos: AttributeDto[] = attributes.map(
        (attr: Attribute) => ({
          id: attr.id,
          idTT: attr.idTT,
          name: attr.name,
          isRequired: attr.isRequired,
          isSalesAttr: attr.isSalesAttr,
          inputType: attr.inputType,
          isMultipleSelection: attr.isMultipleSelection,
          isCustomizable: attr.isCustomizable,
          valueDataFormat: attr.valueDataFormat,
          type: attr.type,
          categoryIdTT: attr.categoryIdTT,
          values: attr.values || [],
          updatedAt: attr.updatedAt,
        }),
      );

      return { attributes: attributeDtos };
    } catch (error) {
      this.logger.error(
        `Error getting attributes from database: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to get attributes: ${error.message}`,
      );
    }
  }

  /**
   * Get brands from database with optional filters (limited to 100 results)
   * @param brandName Filter by brand name (optional)
   * @param isT1Brand Filter by isT1Brand flag (optional)
   * @param authorizedStatus Filter by authorizedStatus (optional)
   * @param brandStatus Filter by brandStatus (optional)
   * @returns List of brands (maximum 100 results)
   */
  async getBrands(
    brandName?: string,
    isT1Brand?: boolean,
    authorizedStatus?: string,
    brandStatus?: string,
  ): Promise<BrandResponseDto> {
    this.logger.log(
      `Getting brands from database with filters: brandName=${brandName || 'any'}, isT1Brand=${isT1Brand ?? 'any'}, authorizedStatus=${authorizedStatus || 'any'}, brandStatus=${brandStatus || 'any'}`,
    );

    try {
      // Build where clause based on provided filters
      const whereClause: any = {};

      if (brandName) {
        whereClause.name = ILike(`%${brandName}%`); // Use ILIKE for case-insensitive partial name matching
      }

      if (isT1Brand !== undefined) {
        whereClause.isT1Brand = isT1Brand;
      }

      if (authorizedStatus) {
        whereClause.authorizedStatus = authorizedStatus;
      }

      if (brandStatus) {
        whereClause.status = brandStatus;
      }

      // Query brands from database with a limit of 100 results
      const brands = await this.brandRepository.find({
        where: whereClause,
        take: 100, // Limit to top 100 results
        order: { name: 'ASC' }, // Order by name for consistent results
      });

      // Convert to DTOs
      const brandDtos: BrandDto[] = brands.map((brand: Brand) => ({
        id: brand.id,
        idTT: brand.idTT,
        name: brand.name,
        authorizedStatus: brand.authorizedStatus,
        brandStatus: brand.status,
        isT1Brand: brand.isT1Brand,
        imageUrl: brand.imageUrl,
        authorizationLetterUrl: brand.authorizationLetterUrl,
        authorizationStartDate: brand.authorizationStartDate,
        authorizationEndDate: brand.authorizationEndDate,
        updatedAt: brand.updatedAt,
      }));

      return { brands: brandDtos };
    } catch (error) {
      this.logger.error(
        `Error getting brands from database: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(`Failed to get brands: ${error.message}`);
    }
  }

  /**
   * Get categories from database with optional filters (limited to 100 results)
   * @param localName Filter by category name (optional)
   * @param isLeaf Filter by isLeaf flag (optional)
   * @param permissionStatuses Filter by permission statuses (optional, comma-separated string)
   * @returns List of categories (maximum 100 results)
   */
  async getCategories(
    localName?: string,
    isLeaf?: boolean,
    permissionStatuses?: string,
  ): Promise<CategoryResponseDto> {
    this.logger.log(
      `Getting categories from database with filters: localName=${localName || 'any'}, isLeaf=${isLeaf ?? 'any'}, permissionStatuses=${permissionStatuses || 'any'}`,
    );

    try {
      // Build where clause based on provided filters
      const whereClause: any = {};

      if (localName) {
        whereClause.localName = ILike(`%${localName}%`); // Use ILIKE for case-insensitive partial name matching
      }

      if (isLeaf !== undefined) {
        whereClause.isLeaf = isLeaf;
      }

      // Query categories from database with a limit of 100 results
      let query = this.categoryRepository
        .createQueryBuilder('category')
        .where(whereClause)
        .take(100) // Limit to top 100 results
        .orderBy('category.localName', 'ASC'); // Order by name for consistent results

      // Handle permissionStatuses filter if provided
      if (permissionStatuses) {
        const statusArray = permissionStatuses
          .split(',')
          .map((status) => status.trim());

        // For each status, we need to check if it's included in the permissionStatuses array
        statusArray.forEach((status, index) => {
          query = query.andWhere(
            `category.permissionStatuses ILIKE :status${index}`,
            { [`status${index}`]: `%${status}%` },
          );
        });
      }

      const categories = await query.getMany();

      this.logger.log(
        `Found ${categories.length} categories matching the provided filters (limited to top 100)`,
      );

      // Convert to DTOs
      const categoryDtos: CategoryDto[] = categories.map(
        (category: Category) => ({
          id: category.id,
          idTT: category.idTT,
          parentIdTT: category.parentIdTT,
          localName: category.localName,
          isLeaf: category.isLeaf,
          version: category.version,
          permissionStatuses: category.permissionStatuses,
          updatedAt: category.updatedAt,
        }),
      );

      return { categories: categoryDtos };
    } catch (error) {
      this.logger.error(
        `Error getting categories from database: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to get categories: ${error.message}`,
      );
    }
  }

  /**
   * Get a single category by ID
   * @param id Category ID
   * @returns Single category
   */
  async getCategoryById(id: number): Promise<CategoryDto> {
    this.logger.log(`Getting category by ID: ${id}`);

    try {
      const category = await this.categoryRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      // Convert to DTO
      const categoryDto: CategoryDto = {
        id: category.id,
        idTT: category.idTT,
        parentIdTT: category.parentIdTT,
        localName: category.localName,
        isLeaf: category.isLeaf,
        version: category.version,
        permissionStatuses: category.permissionStatuses,
        updatedAt: category.updatedAt,
      };

      return categoryDto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error getting category by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to get category: ${error.message}`,
      );
    }
  }

  /**
   * Get a single brand by ID
   * @param id Brand ID
   * @returns Single brand
   */
  async getBrandById(id: number): Promise<BrandDto> {
    this.logger.log(`Getting brand by ID: ${id}`);

    try {
      const brand = await this.brandRepository.findOne({
        where: { id },
      });

      if (!brand) {
        throw new NotFoundException(`Brand with ID ${id} not found`);
      }

      // Convert to DTO
      const brandDto: BrandDto = {
        id: brand.id,
        idTT: brand.idTT,
        name: brand.name,
        authorizedStatus: brand.authorizedStatus,
        brandStatus: brand.status,
        isT1Brand: brand.isT1Brand,
        imageUrl: brand.imageUrl,
        authorizationLetterUrl: brand.authorizationLetterUrl,
        authorizationStartDate: brand.authorizationStartDate,
        authorizationEndDate: brand.authorizationEndDate,
        updatedAt: brand.updatedAt,
      };

      return brandDto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error getting brand by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(`Failed to get brand: ${error.message}`);
    }
  }
}
