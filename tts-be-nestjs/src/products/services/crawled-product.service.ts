import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { CrawledProduct } from '../entities/crawled-product.entity';
import { User } from '../../auth/entities/user.entity';
import { CreateCrawledProductDto } from '../dto/create-crawled-product.dto';
import { CrawledProductQueryDto } from '../dto/crawled-product-query.dto';
import { UpdateCrawledProductDto, ManageCrawledProductUsersDto, BulkDeleteCrawledProductsDto, ShareSelectedProductsDto } from '../dto/update-crawled-product.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@Injectable()
export class CrawledProductService {
  constructor(
    @InjectRepository(CrawledProduct)
    private crawledProductRepository: Repository<CrawledProduct>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(
    createCrawledProductDto: CreateCrawledProductDto,
    userId: number,
  ): Promise<CrawledProduct> {
    const { images, isPublic, marketId, reviewCount, ...productData } = createCrawledProductDto;

    // Process images to ensure proper structure
    const processedImages = images?.map((imageDto, index) => ({
      imageUrl: imageDto.imageUrl,
      isPrimary: imageDto.isPrimary ?? index === 0,
      sortOrder: imageDto.sortOrder ?? index,
    })) || [];

    // Check for existing product with same marketId
    const existingProduct = await this.crawledProductRepository.findOne({
      where: { marketId },
      relations: ['users'],
    });

    if (existingProduct) {
      // Update existing product with new data
      existingProduct.title = productData.title;
      existingProduct.productUrl = productData.productUrl;
      existingProduct.sellerName = productData.sellerName || existingProduct.sellerName;
      existingProduct.reviewCount = reviewCount !== undefined ? reviewCount : existingProduct.reviewCount;
      existingProduct.metadata = productData.metadata || existingProduct.metadata;

      // Update images with new data
      existingProduct.images = processedImages.length > 0 ? processedImages : existingProduct.images;

      // Add current user to the product if not already associated
      const currentUser = await this.userRepository.findOne({ where: { id: userId } });
      if (currentUser) {
        const isUserAlreadyAssociated = existingProduct.users.some(user => user.id === userId);
        if (!isUserAlreadyAssociated) {
          existingProduct.users.push(currentUser);
        }
      }

      return await this.crawledProductRepository.save(existingProduct);
    }

    // Get users to associate with the product
    const users: User[] = [];

    // Always include the creating user
    const creatingUser = await this.userRepository.findOne({ where: { id: userId } });
    if (creatingUser) {
      users.push(creatingUser);
    }

    // Create the crawled product with images as JSONB
    const crawledProduct = this.crawledProductRepository.create({
      ...productData,
      marketId,
      reviewCount,
      isPublic: isPublic ?? false,
      images: processedImages,
      users,
    });

    return await this.crawledProductRepository.save(crawledProduct);
  }

  async findAll(
    query: CrawledProductQueryDto,
    userId: number,
  ): Promise<PaginatedResult<CrawledProduct>> {
    const {
      page = 1,
      limit = 20,
      marketplace,
      title,
      sellerName,
      marketId,
      isPublic,
      extractedAfter,
      extractedBefore,
      minReviewCount,
      maxReviewCount,
      sortField = 'updatedAt',
      sortDirection = 'desc',
    } = query;

    const queryBuilder: SelectQueryBuilder<CrawledProduct> = this.crawledProductRepository
      .createQueryBuilder('crawledProduct')
      .leftJoinAndSelect('crawledProduct.users', 'users')
      .where('(users.id = :userId OR crawledProduct.isPublic = true)', { userId });

    // Apply filters
    if (marketplace) {
      queryBuilder.andWhere('crawledProduct.marketplace = :marketplace', { marketplace });
    }

    if (title) {
      queryBuilder.andWhere('crawledProduct.title ILIKE :search', {
        search: `%${title}%`,
      });
    }

    if (sellerName) {
      queryBuilder.andWhere('crawledProduct.sellerName ILIKE :sellerName', {
        sellerName: `%${sellerName}%`,
      });
    }

    if (marketId) {
      queryBuilder.andWhere('crawledProduct.marketId = :marketId', { marketId });
    }

    if (isPublic !== undefined) {
      queryBuilder.andWhere('crawledProduct.isPublic = :isPublic', { isPublic });
    }

    if (extractedAfter) {
      queryBuilder.andWhere('crawledProduct.extractedAt >= :extractedAfter', {
        extractedAfter,
      });
    }

    if (extractedBefore) {
      queryBuilder.andWhere('crawledProduct.extractedAt <= :extractedBefore', {
        extractedBefore,
      });
    }

    if (minReviewCount !== undefined) {
      queryBuilder.andWhere('crawledProduct.reviewCount >= :minReviewCount', {
        minReviewCount,
      });
    }

    if (maxReviewCount !== undefined) {
      queryBuilder.andWhere('crawledProduct.reviewCount <= :maxReviewCount', {
        maxReviewCount,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`crawledProduct.${sortField}`, sortDirection.toUpperCase() as 'ASC' | 'DESC');

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  async findOne(id: number, userId: number): Promise<CrawledProduct> {
    const crawledProduct = await this.crawledProductRepository
      .createQueryBuilder('crawledProduct')
      .leftJoinAndSelect('crawledProduct.users', 'users')
      .where('crawledProduct.id = :id', { id })
      .andWhere('(users.id = :userId OR crawledProduct.isPublic = true)', { userId })
      .getOne();

    if (!crawledProduct) {
      throw new NotFoundException(`Crawled product with ID ${id} not found or access denied`);
    }

    return crawledProduct;
  }

  async remove(id: number, userId: number): Promise<void> {
    const crawledProduct = await this.findOne(id, userId);

    // Check if user has permission to delete (must be one of the associated users)
    const hasPermission = crawledProduct.users.some(user => user.id === userId);
    if (!hasPermission) {
      throw new NotFoundException(`Crawled product with ID ${id} not found or access denied`);
    }

    await this.crawledProductRepository.remove(crawledProduct);
  }

  async update(
    id: number,
    updateDto: UpdateCrawledProductDto,
    userId: number,
  ): Promise<CrawledProduct> {
    const crawledProduct = await this.findOne(id, userId);

    // Check if user has permission to update (must be one of the associated users)
    const hasPermission = crawledProduct.users.some(user => user.id === userId);
    if (!hasPermission) {
      throw new NotFoundException(`Crawled product with ID ${id} not found or access denied`);
    }

    Object.assign(crawledProduct, updateDto);
    return await this.crawledProductRepository.save(crawledProduct);
  }

  async manageUsers(
    id: number,
    manageUsersDto: ManageCrawledProductUsersDto,
    userId: number,
  ): Promise<CrawledProduct> {
    const crawledProduct = await this.findOne(id, userId);

    // Check if user has permission to manage users (must be one of the associated users)
    const hasPermission = crawledProduct.users.some(user => user.id === userId);
    if (!hasPermission) {
      throw new NotFoundException(`Crawled product with ID ${id} not found or access denied`);
    }

    const { addUserIds, removeUserIds } = manageUsersDto;

    // Add users
    if (addUserIds && addUserIds.length > 0) {
      const usersToAdd = await this.userRepository.find({
        where: { id: In(addUserIds) },
      });

      // Filter out users that are already associated
      const existingUserIds = crawledProduct.users.map(user => user.id);
      const newUsers = usersToAdd.filter(user => !existingUserIds.includes(user.id));

      crawledProduct.users.push(...newUsers);
    }

    // Remove users
    if (removeUserIds && removeUserIds.length > 0) {
      crawledProduct.users = crawledProduct.users.filter(
        user => !removeUserIds.includes(user.id)
      );
    }

    return await this.crawledProductRepository.save(crawledProduct);
  }

  async findByMarketId(marketId: string): Promise<CrawledProduct | null> {
    return this.crawledProductRepository.findOne({
      where: { marketId },
      relations: ['users'],
    });
  }

  async findByUrl(productUrl: string, userId: number): Promise<CrawledProduct | null> {
    return this.crawledProductRepository
      .createQueryBuilder('crawledProduct')
      .leftJoinAndSelect('crawledProduct.users', 'users')
      .where('crawledProduct.productUrl = :productUrl', { productUrl })
      .andWhere('(users.id = :userId OR crawledProduct.isPublic = true)', { userId })
      .getOne();
  }

  async togglePublicStatus(id: number, userId: number): Promise<CrawledProduct> {
    const crawledProduct = await this.findOne(id, userId);

    // Check if user has permission to toggle public status (must be one of the associated users)
    const hasPermission = crawledProduct.users.some(user => user.id === userId);
    if (!hasPermission) {
      throw new NotFoundException(`Crawled product with ID ${id} not found or access denied`);
    }

    // Toggle the isPublic status
    crawledProduct.isPublic = !crawledProduct.isPublic;

    return await this.crawledProductRepository.save(crawledProduct);
  }

  async bulkDelete(bulkDeleteDto: BulkDeleteCrawledProductsDto, userId: number): Promise<{ deletedCount: number }> {
    const { ids } = bulkDeleteDto;

    // Find all products that belong to the user
    const products = await this.crawledProductRepository
      .createQueryBuilder('crawledProduct')
      .leftJoinAndSelect('crawledProduct.users', 'users')
      .where('crawledProduct.id IN (:...ids)', { ids })
      .andWhere('users.id = :userId', { userId })
      .getMany();

    if (products.length === 0) {
      throw new NotFoundException('No crawled products found or access denied');
    }

    // Delete the products
    await this.crawledProductRepository.remove(products);

    return { deletedCount: products.length };
  }

  async shareSelected(shareDto: ShareSelectedProductsDto, userId: number): Promise<{ updatedCount: number; products: CrawledProduct[] }> {
    const { ids, isPublic } = shareDto;

    // Find all products that belong to the user
    const products = await this.crawledProductRepository
      .createQueryBuilder('crawledProduct')
      .leftJoinAndSelect('crawledProduct.users', 'users')
      .where('crawledProduct.id IN (:...ids)', { ids })
      .andWhere('users.id = :userId', { userId })
      .getMany();

    if (products.length === 0) {
      throw new NotFoundException('No crawled products found or access denied');
    }

    // Update the isPublic status for all products
    products.forEach(product => {
      product.isPublic = isPublic;
    });

    const updatedProducts = await this.crawledProductRepository.save(products);

    return {
      updatedCount: updatedProducts.length,
      products: updatedProducts
    };
  }
}
