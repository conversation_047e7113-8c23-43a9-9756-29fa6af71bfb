import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ProductUpload,
  ProductUploadStatus,
} from '../entities/product-upload.entity';
import { StagedProduct } from '../entities/staged-product.entity';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import {
  CreateProductUploadDto,
  ProductUploadResponseDto,
  ProductUploadStatusResponseDto,
} from '../dto/product-upload.dto';
import { ProductUploadOptionsDto } from '../dto/product-validation.dto';
import { ProductQueueService } from 'src/queues/services/product-queue.service';
// StagedProductService is no longer needed here as image processing is done in the queue

@Injectable()
export class ProductUploadService {
  private readonly logger = new Logger(ProductUploadService.name);

  constructor(
    @InjectRepository(ProductUpload)
    private readonly productUploadRepository: Repository<ProductUpload>,
    @InjectRepository(StagedProduct)
    private readonly stagedProductRepository: Repository<StagedProduct>,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    private readonly productQueueService: ProductQueueService,
  ) {}

  /**
   * Create a new product upload
   * @param createProductUploadDto Create product upload DTO
   * @param options Upload options
   * @param userId User ID
   * @returns Created product upload
   */
  async createProductUpload(
    createProductUploadDto: CreateProductUploadDto,
    options?: ProductUploadOptionsDto,
    userId?: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Creating new product upload for staged product ID: ${createProductUploadDto.stagedProductId} and TikTok shop ID: ${createProductUploadDto.tiktokShopId}${userId ? ` for user: ${userId}` : ''}`,
    );

    // Check if staged product exists and belongs to the user
    const whereClause: any = { id: createProductUploadDto.stagedProductId };
    if (userId) {
      whereClause.userId = userId;
    }

    const stagedProduct = await this.stagedProductRepository.findOne({
      where: whereClause,
    });

    if (!stagedProduct) {
      throw new NotFoundException(
        `Staged product with ID ${createProductUploadDto.stagedProductId} not found`,
      );
    }

    // Check if TikTok shop exists and belongs to the user
    const tiktokShopWhereClause: any = { id: createProductUploadDto.tiktokShopId };
    if (userId) {
      tiktokShopWhereClause.userId = userId;
    }

    const tiktokShop = await this.tikTokShopRepository.findOne({
      where: tiktokShopWhereClause,
    });
    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok shop with ID ${createProductUploadDto.tiktokShopId} not found${userId ? ' or does not belong to the user' : ''}`,
      );
    }

    // Check if there's already an active upload for this staged product and TikTok shop
    const existingUpload = await this.productUploadRepository.findOne({
      where: {
        stagedProductId: createProductUploadDto.stagedProductId,
        tiktokShopId: createProductUploadDto.tiktokShopId,
        status: ProductUploadStatus.IN_PROGRESS,
      },
    });

    if (existingUpload) {
      throw new BadRequestException(
        `There's already an active upload for this staged product and TikTok shop`,
      );
    }

    // Create product upload entity
    const productUpload = new ProductUpload();
    productUpload.stagedProductId = createProductUploadDto.stagedProductId;
    productUpload.tiktokShopId = createProductUploadDto.tiktokShopId;
    productUpload.status = ProductUploadStatus.PENDING;
    productUpload.progress = 0;

    // Save product upload
    const savedProductUpload =
      await this.productUploadRepository.save(productUpload);
    this.logger.log(`Product upload created with ID: ${savedProductUpload.id}`);

    // Note: We no longer process images here. Image processing is now handled in the background job.

    // Add product upload job to the queue with options and userId
    const jobResult = await this.productQueueService.addProductCreationJob({
      productUploadId: savedProductUpload.id,
      stagedProductId: createProductUploadDto.stagedProductId,
      tiktokShopId: createProductUploadDto.tiktokShopId,
      userId: userId, // Pass userId to the job
      options: {
        skipValidation: options?.skipValidation || false,
        forceUpload: options?.forceUpload || false,
      },
    });

    // Update product upload with job ID
    savedProductUpload.jobId = jobResult.jobId.toString();
    savedProductUpload.status = ProductUploadStatus.IN_PROGRESS;
    const updatedProductUpload =
      await this.productUploadRepository.save(savedProductUpload);

    // Load relations for complete response data
    const productUploadWithRelations = await this.productUploadRepository.findOne({
      where: { id: updatedProductUpload.id },
      relations: ['tiktokShop', 'stagedProduct'],
    });

    if (!productUploadWithRelations) {
      throw new NotFoundException(`Product upload with ID ${updatedProductUpload.id} not found after creation`);
    }

    return this.mapToResponseDto(productUploadWithRelations);
  }

  /**
   * Get a product upload by ID
   * @param id Product upload ID
   * @param userId User ID
   * @returns Product upload
   */
  async getProductUploadById(
    id: number,
    userId?: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Getting product upload with ID: ${id}${userId ? ` for user: ${userId}` : ''}`,
    );

    const productUpload = await this.productUploadRepository.findOne({
      where: { id },
      relations: ['tiktokShop'],
    });
    if (!productUpload) {
      throw new NotFoundException(`Product upload with ID ${id} not found`);
    }

    // If userId is provided, verify that the staged product belongs to the user
    if (userId) {
      const stagedProduct = await this.stagedProductRepository.findOne({
        where: { id: productUpload.stagedProductId, userId },
      });

      if (!stagedProduct) {
        throw new NotFoundException(`Product upload with ID ${id} not found`);
      }
    }

    return this.mapToResponseDto(productUpload);
  }

  /**
   * Get product uploads by staged product ID
   * @param stagedProductId Staged product ID
   * @param userId User ID
   * @returns List of product uploads
   */
  async getProductUploadsByProductId(
    stagedProductId: number,
    userId?: number,
  ): Promise<ProductUploadResponseDto[]> {
    this.logger.log(
      `Getting product uploads for staged product ID: ${stagedProductId}${userId ? ` for user: ${userId}` : ''}`,
    );

    // If userId is provided, verify that the staged product belongs to the user
    if (userId) {
      const stagedProduct = await this.stagedProductRepository.findOne({
        where: { id: stagedProductId, userId },
      });

      if (!stagedProduct) {
        throw new NotFoundException(
          `Staged product with ID ${stagedProductId} not found`,
        );
      }
    }

    const productUploads = await this.productUploadRepository.find({
      where: { stagedProductId },
      relations: ['tiktokShop'],
      order: { createdAt: 'DESC' },
    });

    return productUploads.map((productUpload) =>
      this.mapToResponseDto(productUpload),
    );
  }

  /**
   * Get product upload status
   * @param id Product upload ID
   * @param userId User ID
   * @returns Product upload status
   */
  async getProductUploadStatus(
    id: number,
    userId?: number,
  ): Promise<ProductUploadStatusResponseDto> {
    this.logger.log(
      `Getting status for product upload ID: ${id}${userId ? ` for user: ${userId}` : ''}`,
    );

    const productUpload = await this.productUploadRepository.findOne({
      where: { id },
    });
    if (!productUpload) {
      throw new NotFoundException(`Product upload with ID ${id} not found`);
    }

    // If userId is provided, verify that the staged product belongs to the user
    if (userId) {
      const stagedProduct = await this.stagedProductRepository.findOne({
        where: { id: productUpload.stagedProductId, userId },
      });

      if (!stagedProduct) {
        throw new NotFoundException(`Product upload with ID ${id} not found`);
      }
    }

    // If the upload is in progress and has a job ID, get the job status
    if (
      productUpload.status === ProductUploadStatus.IN_PROGRESS &&
      productUpload.jobId
    ) {
      try {
        const jobStatus = await this.productQueueService.getJobStatus(
          productUpload.jobId,
        );

        // If the job doesn't exist anymore, mark the upload as failed
        if (!jobStatus.exists) {
          productUpload.status = ProductUploadStatus.FAILED;
          productUpload.errorMessage = 'Job not found';
          await this.productUploadRepository.save(productUpload);
        }
        // If the job is completed, update the upload status
        else if (jobStatus.state === 'completed') {
          if (jobStatus.returnvalue?.success) {
            productUpload.status = ProductUploadStatus.COMPLETED;
            productUpload.tiktokProductId = jobStatus.returnvalue.productId;
            productUpload.completedAt = new Date();
          } else {
            productUpload.status = ProductUploadStatus.FAILED;
            productUpload.errorMessage =
              jobStatus.returnvalue?.message || 'Unknown error';
          }
          await this.productUploadRepository.save(productUpload);
        }
        // If the job is failed, update the upload status
        else if (jobStatus.state === 'failed') {
          productUpload.status = ProductUploadStatus.FAILED;
          productUpload.errorMessage = jobStatus.failedReason || 'Job failed';
          await this.productUploadRepository.save(productUpload);
        }
        // Update progress
        else if (typeof jobStatus.progress === 'number') {
          productUpload.progress = jobStatus.progress;
          await this.productUploadRepository.save(productUpload);
        }
      } catch (error) {
        this.logger.error(
          `Error getting job status: ${error.message}`,
          error.stack,
        );
      }
    }

    return {
      id: productUpload.id,
      status: productUpload.status,
      progress: productUpload.progress,
      tiktokProductId: productUpload.tiktokProductId,
      errorMessage: productUpload.errorMessage,
      jobId: productUpload.jobId,
      createdAt: productUpload.createdAt,
      updatedAt: productUpload.updatedAt,
      completedAt: productUpload.completedAt,
    };
  }

  /**
   * Map product upload entity to response DTO
   * @param productUpload Product upload entity
   * @returns Product upload response DTO
   */
  private mapToResponseDto(
    productUpload: ProductUpload,
  ): ProductUploadResponseDto {
    return {
      id: productUpload.id,
      stagedProductId: productUpload.stagedProductId,
      tiktokShopId: productUpload.tiktokShopId,
      status: productUpload.status,
      tiktokProductId: productUpload.tiktokProductId,
      errorMessage: productUpload.errorMessage,
      jobId: productUpload.jobId,
      progress: productUpload.progress,
      createdAt: productUpload.createdAt,
      updatedAt: productUpload.updatedAt,
      completedAt: productUpload.completedAt,
      shopFriendlyName: productUpload.tiktokShop?.friendly_name,
      shopCode: productUpload.tiktokShop?.code,
      stagedProductTitle: productUpload.stagedProduct?.title,
    };
  }
}
