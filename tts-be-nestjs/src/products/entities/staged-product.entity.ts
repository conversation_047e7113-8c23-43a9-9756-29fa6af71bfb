import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../auth/entities/user.entity';
import { Category } from './category.entity';
import { Brand } from './brand.entity';

/**
 * Entity representing a staged product that can be uploaded to multiple TikTok shops
 */
@Entity('staged_products')
export class StagedProduct {
  @ApiProperty({ description: 'Internal staged product ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  @Column({ length: 255 })
  title: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  @Column({ type: 'text' })
  description: string;

  @ApiProperty({ description: 'Category ID', example: '1' })
  @Column()
  categoryId: number;

  @ApiProperty({ description: 'Brand ID', example: '1' })
  @Column()
  brandId: number;

  @ApiProperty({
    description:
      'Category version (v1 for 3-level category tree, v2 for 7-level US category tree)',
    example: 'v2',
  })
  @Column({ length: 10, default: 'v2' })
  categoryVersion: string;

  @ApiProperty({
    description: 'Package dimensions',
    example: {
      length: '20',
      width: '15',
      height: '5',
      unit: 'CENTIMETER',
    },
  })
  @Column('jsonb')
  packageDimensions: {
    length: string;
    width: string;
    height: string;
    unit: string;
  };

  @ApiProperty({
    description: 'Package weight',
    example: {
      value: '500',
      unit: 'GRAM',
    },
  })
  @Column('jsonb')
  packageWeight: {
    value: string;
    unit: string;
  };

  @ApiProperty({ description: 'Product images' })
  @Column('jsonb')
  images: Array<{
    imageUrl?: string;
    r2Key?: string;
  }>;

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
  })
  @Column('jsonb')
  productAttributes: Array<{
    id: string;
    name?: string;
    values: Array<{
      id?: string;
      name?: string;
    }>;
  }>;

  @ApiProperty({ description: 'Product SKUs' })
  @Column('jsonb')
  skus: Array<{
    salesAttributes: Array<{
      name: string;
      valueName: string;
      skuImg?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      };
      supplementarySkuImages?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      }[];
    }>;
    inventory: Array<{
      quantity: number;
      warehouseId?: string;
    }>;
    price: {
      amount: number;
      currency: string;
    };
    listPrice?: {
      amount: number;
      currency: string;
    };
    identifierCode?: {
      code: string;
      type: string;
    };
    sellerSku?: string;
  }>;

  @ApiProperty({ description: 'Size chart information', required: false })
  @Column('jsonb', { nullable: true })
  sizeChart?: {
    imageUrl?: string;
    r2Key?: string;
  };

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The ID of the user who owns this staged product',
    example: 1,
  })
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Category)
  @JoinColumn({ name: 'categoryId' })
  category: Category;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;
}
