import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Product } from './product.entity';
import { ImageUseCase } from '../dto/upload-image.dto';

@Entity()
export class ProductImage {
  @ApiProperty({
    description: 'The unique identifier for the product image',
    example: 1,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'The URI of the image in TikTok Shop',
    example: 'tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5',
  })
  @Column()
  uri: string;

  @ApiProperty({
    description: 'The URL to access the image',
    example:
      'https://p16-tiktokshop-sg.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5~tplv-tiktokshop-v1:0:0:q-70.webp',
  })
  @Column()
  url: string;

  @ApiProperty({
    description: 'The width of the image',
    example: 800,
  })
  @Column()
  width: number;

  @ApiProperty({
    description: 'The height of the image',
    example: 600,
  })
  @Column()
  height: number;

  @ApiProperty({
    description: 'The use case of the image',
    enum: ImageUseCase,
    example: ImageUseCase.MAIN_IMAGE,
  })
  @Column({
    type: 'enum',
    enum: ImageUseCase,
    default: ImageUseCase.MAIN_IMAGE,
  })
  useCase: ImageUseCase;

  @ApiProperty({
    description: 'The ID of the product this image belongs to',
    example: 1,
  })
  @Column({ nullable: true })
  productId: number;

  @ManyToOne(() => Product, (product) => product.images, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @ApiProperty({
    description: 'The date the image was created',
    example: '2023-01-01T00:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'The date the image was last updated',
    example: '2023-01-01T00:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
