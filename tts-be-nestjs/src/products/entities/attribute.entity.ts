import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IAttributeValue } from '../interfaces/attribute-value.interface';

@Entity('attributes')
export class Attribute {
  @ApiProperty({ description: 'Attribute ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Attribute ID from TikTok Shop',
    example: '7082427311584347905',
  })
  @Column({ unique: true })
  idTT: string;

  @ApiProperty({ description: 'Attribute name', example: 'Color' })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Whether this attribute is required',
    example: true,
  })
  @Column()
  isRequired: boolean;

  @ApiProperty({
    description: 'Whether this is a sales attribute',
    example: true,
  })
  @Column()
  isSalesAttr: boolean;

  @ApiProperty({
    description: 'Input type for this attribute',
    example: 'MULTIPLE_SELECT',
  })
  @Column({ nullable: true })
  inputType: string;

  @ApiProperty({
    description: 'Whether this attribute allows multiple selections',
    example: true,
  })
  @Column({ nullable: true })
  isMultipleSelection: boolean;

  @ApiProperty({
    description: 'Whether this attribute is customizable',
    example: false,
  })
  @Column({ nullable: true })
  isCustomizable: boolean;

  @ApiProperty({
    description: 'Value data format for this attribute',
    example: 'POSITIVE_INT_OR_DECIMAL',
  })
  @Column({ nullable: true })
  valueDataFormat: string;

  @ApiProperty({
    description: 'Attribute type (SALES_PROPERTY or PRODUCT_PROPERTY)',
    example: 'SALES_PROPERTY',
  })
  @Column({ nullable: true })
  type: string;

  @ApiProperty({
    description: 'Category IDs this attribute belongs to',
    example: ['853000', '853001'],
  })
  @Column({ type: 'jsonb', default: '[]' })
  categoryIdTT: string[];

  @ApiProperty({ description: 'Attribute values' })
  @Column({ type: 'jsonb', nullable: true, default: '[]' })
  values: IAttributeValue[];

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
