import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../auth/entities/user.entity';

/**
 * Entity representing automated crawl schedules for product discovery
 */
@Entity('crawl_schedules')
export class CrawlSchedule {
  @ApiProperty({ description: 'Internal schedule ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Name/description for this crawl schedule',
    example: 'Vintage T-Shirts Daily Crawl',
  })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({
    description: 'Keywords to search for (comma-separated)',
    example: 'vintage t-shirt, retro clothing, cotton tee',
  })
  @Column({ type: 'text' })
  keywords: string;

  @ApiProperty({
    description: 'Target marketplace for crawling',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
  })
  @Column({ length: 50 })
  marketplace: string;

  @ApiProperty({
    description: 'Crawl frequency in minutes',
    example: 1440,
  })
  @Column()
  frequencyMinutes: number;

  @ApiProperty({
    description: 'Maximum number of products to crawl per run',
    example: 50,
  })
  @Column({ default: 20 })
  maxProductsPerRun: number;

  @ApiProperty({
    description: 'Whether this schedule is currently active',
    example: true,
  })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    description: 'When this schedule was last executed',
    example: '2024-01-15T10:30:00Z',
  })
  @Column({ type: 'timestamp', nullable: true })
  lastRun: Date | null;

  @ApiProperty({
    description: 'When this schedule should run next',
    example: '2024-01-16T10:30:00Z',
  })
  @Column({ type: 'timestamp', nullable: true })
  nextRun: Date | null;

  @ApiProperty({
    description: 'Number of products found in last run',
    example: 25,
  })
  @Column({ default: 0 })
  lastRunProductCount: number;

  @ApiProperty({
    description: 'Status of the last run',
    example: 'completed',
    enum: ['pending', 'running', 'completed', 'failed'],
  })
  @Column({ length: 50, default: 'pending' })
  lastRunStatus: string;

  @ApiProperty({
    description: 'Error message if last run failed',
    example: 'Rate limit exceeded',
  })
  @Column({ length: 500, nullable: true })
  lastRunError: string;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The ID of the user who owns this schedule',
    example: 1,
  })
  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
