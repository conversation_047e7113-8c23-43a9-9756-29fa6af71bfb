import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Product } from './product.entity';

@Entity('skus')
export class Sku {
  @ApiProperty({ description: 'Internal SKU ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'TikTok Shop SKU ID', example: '1234567890' })
  @Column({ nullable: true, unique: true })
  idTT: string;

  @ApiProperty({
    description:
      'An internal code/name for managing SKUs, not visible to buyers',
    example: 'GILDAN-5000-BLK-S',
  })
  @Column({ length: 100 })
  sellerSku: string;

  @ApiProperty({
    description: 'SKU list prices from external platforms',
    example: [
      {
        amount: '29.99',
        currency: 'USD',
        source: 'SHOPIFY_COMPARE_AT_PRICE',
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  externalListPrices: {
    amount: number;
    currency: string;
    source: string;
  }[];

  @ApiProperty({
    description: 'SKU inventory information',
    example: [
      {
        quantity: 100,
        warehouseId: '123456',
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  inventory: {
    quantity: number;
    warehouseId: string;
  }[];

  @ApiProperty({
    description: 'Identifier code',
    example: {
      code: '10000000000010',
      type: 'GTIN',
    },
  })
  @Column('jsonb', { nullable: true })
  identifierCode: {
    code: string;
    type: string;
  };

  @ApiProperty({
    description: 'Extra identifier codes',
    example: ['00012345678905', '9780596520687'],
  })
  @Column('simple-array', { nullable: true })
  extraIdentifierCodes: string[];

  @ApiProperty({
    description: 'Sales attributes',
    example: [
      {
        id: '100000',
        name: 'Color',
        valueId: '100000',
        valueName: 'Red',
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  salesAttributes: {
    id: string;
    name: string;
    valueId: string;
    valueName: string;
    skuImg?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    supplementarySkuImages?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
  }[];

  @ApiProperty({
    description: 'External SKU ID',
    example: '1729592969712207234',
  })
  @Column({ nullable: true })
  externalSkuId: string;

  @ApiProperty({
    description: 'Combined SKUs',
    example: [
      {
        productId: '1729582718312380123',
        skuId: '1729582718312380123',
        skuCount: 1,
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  combinedSkus: {
    productId: string;
    skuId: string;
    skuCount: number;
  }[];

  @ApiProperty({
    description: 'Global listing policy',
    example: {
      priceSync: true,
      inventoryType: 'SHARED',
      replicateSource: {
        productId: '1729592969712203232',
        shopId: '7295929697122032321',
        skuId: '1729592969712203232',
      },
    },
  })
  @Column('jsonb', { nullable: true })
  globalListingPolicy: {
    priceSync?: boolean;
    inventoryType?: string;
    replicateSource?: {
      productId: string;
      shopId: string;
      skuId: string;
    };
  };

  //TO-DO: unnecessary 
  @ApiProperty({
    description: 'SKU unit count',
    example: '1.00',
  })
  @Column({ nullable: true })
  skuUnitCount: string;

  @ApiProperty({
    description: 'External URLs',
    example: ['https://example.com/path1', 'https://example.com/path2'],
  })
  @Column('simple-array', { nullable: true })
  externalUrls: string[];

  @ApiProperty({
    description: 'Pre-sale information',
    example: {
      type: 'PRE_ORDER',
      fulfillmentType: {
        handlingDurationDays: 3,
      },
    },
  })
  @Column('jsonb', { nullable: true })
  preSale: {
    type: string;
    fulfillmentType?: {
      handlingDurationDays: number;
    };
  };

  @ApiProperty({
    description: 'SKU price information',
    example: {
      salePrice: 24.99,
      taxExclusivePrice: 26.99,
      currency: 'USD',
      unitPrice: 1,
    },
  })
  @Column('jsonb')
  price: {
    salePrice: number | null;
    taxExclusivePrice: number | null;
    currency: string;
    unitPrice?: number | null;
  };

  @ApiProperty({
    description: 'SKU list price information',
    example: {
      amount: 29.99,
      currency: 'USD',
    },
  })
  @Column('jsonb', { nullable: true })
  listPrice: {
    amount: number | null;
    currency: string;
  } | null;

  @ApiProperty({
    description: 'Product ID from internal system',
    example: 1,
  })
  @Column()
  productId: number;

  @ManyToOne(() => Product, (product) => product.skus, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
