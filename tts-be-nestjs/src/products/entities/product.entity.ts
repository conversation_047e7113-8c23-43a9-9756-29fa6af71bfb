import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Sku } from './sku.entity';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';
import { ProductImage } from './product-image.entity';
import { User } from '../../auth/entities/user.entity';

export enum ProductStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  FAILED = 'FAILED',
  ACTIVATE = 'ACTIVATE',
  SELLER_DEACTIVATED = 'SELLER_DEACTIVATED',
  PLATFORM_DEACTIVATED = 'PLATFORM_DEACTIVATED',
  FREEZE = 'FREEZE',
  DELETED = 'DELETED',
  AUDITING = 'AUDITING',
}

@Entity('products')
export class Product {
  @ApiProperty({ description: 'Product ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Product ID of TikTok Shop ',
    example: '7891234567',
  })
  @Column({ nullable: true, unique: true })
  idTT: string;

  @ApiProperty({
    description: 'Category',
    example: 'Women Tops/Hoodies & Sweatshirts/Women Pullover Sweatshirts',
  })
  @Column({ length: 100, nullable: true })
  category: string;

  @ApiProperty({
    description: 'Category chains from TikTok Shop',
    example: [
      {
        id: '853000',
        parentId: '851848',
        localName: 'Botol & Stoples Penyimpanan',
        isLeaf: true,
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  categoryChains: {
    id: string;
    parentId: string;
    localName: string;
    isLeaf: boolean;
  }[];

  @ApiProperty({ description: 'Brand', example: 'Gildan' })
  @Column({ length: 100, nullable: true })
  brandInfo: string;

  @ApiProperty({
    description: 'Brand information from TikTok Shop',
    example: { id: '7082427311584347905', name: 'brand xxx aaa' },
  })
  @Column('jsonb', { nullable: true })
  brand: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Product title',
    example: 'T-Shirt Gildan 5000 1 side',
  })
  @Column({ length: 300 })
  title: string;

  @ApiProperty({
    description: 'Product description',
    example:
      'Gildan 5000 - Classic Heavy Cotton T-Shirt - Durability Meets Comfort!',
  })
  @Column('text', { nullable: true })
  description: string;

  @ApiProperty({
    description: 'Size chart information from TikTok Shop',
    example: {
      image: {
        urls: ['https://example.com/size-chart.png'],
      },
      template: { id: '7267563252536723205' },
    },
  })
  @Column('jsonb', { nullable: true })
  sizeChart: {
    image?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    template?: {
      id: string;
    };
  };

  //TO-DO: unnecessary.
  @ApiProperty({
    description: 'Product Image List',
    example: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ],
  })
  @Column('simple-array', { nullable: true })
  productImages: string[];

  @ApiProperty({
    description: 'Main images information from TikTok Shop',
    example: [
      {
        height: 600,
        width: 600,
        thumbUrls: ['https://example.com/thumb.jpg'],
        uri: 'tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4',
        urls: ['https://example.com/image.jpg'],
      },
    ],
  })
  @Column('jsonb', { nullable: true })
  mainImages: {
    height?: number;
    width?: number;
    thumbUrls?: string[];
    uri?: string;
    urls?: string[];
  }[];

  @ApiProperty({
    description: 'Video information from TikTok Shop',
    example: {
      id: 'v09ea0g40000cj91373c77u3mid3g1s0',
      coverUrl: 'https://example.com/cover.jpg',
      format: 'MP4',
      url: 'https://example.com/video.mp4',
      width: 1280,
      height: 480,
      size: 1000,
    },
  })
  @Column('jsonb', { nullable: true })
  videoInfo: {
    id?: string;
    coverUrl?: string;
    format?: string;
    url?: string;
    width?: number;
    height?: number;
    size?: number;
  };

  @ApiProperty({ example: 12, description: 'Package Length in inch' })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  packageLength: number;

  @ApiProperty({ example: 10, description: 'Package Width in inch' })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  packageWidth: number;

  @ApiProperty({ example: 1.5, description: 'Package Height in inch' })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  packageHeight: number;

  @ApiProperty({
    description: 'Package dimensions information from TikTok Shop',
    example: {
      length: '10',
      width: '10',
      height: '10',
      unit: 'CENTIMETER',
    },
  })
  @Column('jsonb', { nullable: true })
  packageDimensions: {
    length?: string;
    width?: string;
    height?: string;
    unit?: string;
  };

  @ApiProperty({
    description: 'Package weight information from TikTok Shop',
    example: {
      value: '1.32',
      unit: 'KILOGRAM',
    },
  })
  @Column('jsonb', { nullable: true })
  packageWeight: {
    value?: string;
    unit?: string;
  };

  @ApiProperty({
    description: 'Product status in TikTok Shop',
    enum: ProductStatus,
    example: ProductStatus.DRAFT,
  })
  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.DRAFT,
  })
  status: ProductStatus;

  @ApiProperty({ description: 'Product sync fail reasons' })
  @Column('simple-array', { nullable: true })
  productSyncFailReasons: string[];

  @ApiProperty({ description: 'Recommended categories' })
  @Column('jsonb', { nullable: true })
  recommendedCategories: {
    id: string;
    localName: string;
  }[];

  @ApiProperty({ description: 'Is not for sale flag' })
  @Column({ default: false })
  isNotForSale: boolean;

  @ApiProperty({ description: 'Integrated platform statuses' })
  @Column('jsonb', { nullable: true })
  integratedPlatformStatuses: {
    platform: string;
    status: string;
  }[];

  @ApiProperty({ description: 'Sales regions' })
  @Column('simple-array', { nullable: true })
  salesRegions: string[];

  @ApiProperty({ description: 'Audit status' })
  @Column('jsonb', { nullable: true })
  audit: {
    status: string;
    preApprovedReasons?: string[];
  };

  @ApiProperty({ description: 'Audit failed reasons' })
  @Column('jsonb', { nullable: true })
  auditFailedReasons: {
    position?: string;
    reasons?: string[];
    suggestions?: string[];
    listingPlatform?: string;
  }[];

  @ApiProperty({
    description: 'Listing quality tier',
    nullable: true,
  })
  @Column({ length: 20, nullable: true })
  listingQualityTier: string;

  @ApiProperty({ description: 'Is COD allowed' })
  @Column({ default: false, nullable: true })
  isCodAllowed: boolean;

  @ApiProperty({ description: 'Product attributes' })
  @Column('jsonb', { nullable: true })
  productAttributes: {
    id: string;
    name: string;
    values: {
      id: string;
      name: string;
    }[];
  }[];

  @ApiProperty({ description: 'Create time from TikTok Shop' })
  @Column({ nullable: true })
  createTimeTT: number;

  @ApiProperty({ description: 'Update time from TikTok Shop' })
  @Column({ nullable: true })
  updateTimeTT: number;

  @ApiProperty({ description: 'Delivery options' })
  @Column('jsonb', { nullable: true })
  deliveryOptions: {
    id: string;
    name: string;
    isAvailable: boolean;
  }[];

  @ApiProperty({ description: 'External product ID' })
  @Column({ nullable: true })
  externalProductId: string;

  @ApiProperty({ description: 'Product types' })
  @Column('simple-array', { nullable: true })
  productTypes: string[];

  @ApiProperty({ description: 'Manufacturer IDs' })
  @Column('simple-array', { nullable: true })
  manufacturerIds: string[];

  @ApiProperty({ description: 'Responsible person IDs' })
  @Column('simple-array', { nullable: true })
  responsiblePersonIds: string[];

  @ApiProperty({ description: 'Shipping insurance requirement' })
  @Column({ nullable: true })
  shippingInsuranceRequirement: string;

  @ApiProperty({ description: 'Minimum order quantity' })
  @Column({ nullable: true })
  minimumOrderQuantity: number;

  @ApiProperty({ description: 'Is pre-owned' })
  @Column({ default: false, nullable: true })
  isPreOwned: boolean;

  @ApiProperty({ description: 'Certifications' })
  @Column('jsonb', { nullable: true })
  certifications: {
    id: string;
    title: string;
    files?: {
      id: string;
      urls: string[];
      name: string;
      format: string;
    }[];
    images?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
    expirationDate?: number;
  }[];

  @ApiProperty({ description: 'Global product association' })
  @Column('jsonb', { nullable: true })
  globalProductAssociation: {
    globalProductId: string;
    skuMappings: {
      globalSkuId: string;
      localSkuId: string;
    }[];
  };

  @ApiProperty({
    description: 'Complete raw TikTok response for debugging and future extensibility',
  })
  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any;

  @ApiProperty({ description: 'SKUs' })
  @OneToMany(() => Sku, (sku) => sku.product, { cascade: true, eager: false })
  skus: Sku[];

  //TO-DO: ProductImage is unnecessary 
  @ApiProperty({ description: 'Product Images' })
  @OneToMany(() => ProductImage, (image) => image.product, {
    cascade: true,
    eager: false,
  })
  images: ProductImage[];

  @ApiProperty({
    description: 'Tiktok Shop ID from internal system',
    example: 1,
  })
  @Column()
  tiktokShopId: number;

  @ManyToOne(() => TikTokShop, (ttShop) => ttShop.products, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'tiktokShopId' })
  tiktokShop: TikTokShop;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The ID of the user who owns this product',
    example: 1,
  })
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
