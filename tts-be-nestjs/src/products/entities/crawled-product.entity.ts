import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../auth/entities/user.entity';

/**
 * Entity representing a product crawled from e-commerce marketplaces
 */
@Entity('crawled_products')
export class CrawledProduct {
  @ApiProperty({ description: 'Internal crawled product ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Product title extracted from marketplace',
    example: 'Vintage Cotton T-Shirt - Premium Quality',
  })
  @Column({ length: 500 })
  title: string;

  @ApiProperty({
    description: 'Direct URL to the product page',
    example: 'https://www.etsy.com/listing/123456789/vintage-cotton-t-shirt',
  })
  @Column({ length: 1000 })
  productUrl: string;

  @ApiProperty({
    description: 'Marketplace source platform',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
  })
  @Column({ length: 50 })
  marketplace: string;

  @ApiProperty({
    description: 'Unique marketplace identifier to prevent duplicates across platforms',
    example: 'etsy-1452582422',
  })
  @Column({ unique: true, length: 255 })
  marketId: string;

  @ApiProperty({
    description: 'Name of the seller/shop',
    example: 'VintageClothingCo',
  })
  @Column({ length: 255, nullable: true })
  sellerName: string;

  @ApiProperty({
    description: 'Number of reviews for this product',
    example: 127,
  })
  @Column({ type: 'integer', nullable: true })
  reviewCount: number;

  @ApiProperty({
    description: 'Additional metadata extracted from the product page',
    example: {
      price: '$29.99',
      currency: 'USD',
      availability: 'In Stock',
      rating: '4.8',
    },
  })
  @Column('jsonb', { nullable: true })
  metadata: {
    price?: string;
    currency?: string;
    availability?: string;
    rating?: string;
    description?: string;
    [key: string]: any;
  };

  @ApiProperty({ description: 'When the product was extracted' })
  @CreateDateColumn()
  extractedAt: Date;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'Users who have access to this crawled product',
    type: () => [User],
  })
  @ManyToMany(() => User, { cascade: true })
  @JoinTable({
    name: 'crawled_product_users',
    joinColumn: { name: 'crawledProductId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'userId', referencedColumnName: 'id' },
  })
  users: User[];

  @ApiProperty({
    description: 'Whether this product can be shared with other users',
    example: false,
  })
  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @ApiProperty({
    description: 'Array of product images stored as JSONB',
    example: [
      {
        imageUrl: 'https://i.etsystatic.com/12345/r/il/abc123/1234567890/il_794xN.1234567890_xyz.jpg',
        isPrimary: true,
        sortOrder: 0,
      },
      {
        imageUrl: 'https://i.etsystatic.com/12345/r/il/def456/0987654321/il_794xN.0987654321_abc.jpg',
        isPrimary: false,
        sortOrder: 1,
      },
    ],
  })
  @Column('jsonb', { nullable: true, default: [] })
  images: {
    imageUrl: string;
    isPrimary: boolean;
    sortOrder: number;
  }[];
}
