import {
  Entity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('categories')
export class Category {
  @ApiProperty({ description: 'Category ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Category ID from TikTok Shop',
    example: '853000',
  })
  @Column({ unique: true })
  idTT: string;

  @ApiProperty({
    description: 'Parent Category ID from TikTok Shop',
    example: '851848',
  })
  @Column({ nullable: true })
  parentIdTT: string;

  @ApiProperty({
    description: 'Category name in local language',
    example: 'Botol & Stoples Penyimpanan',
  })
  @Column()
  localName: string;

  @ApiProperty({
    description: 'Whether this is a leaf category',
    example: true,
  })
  @Column()
  isLeaf: boolean;

  @ApiProperty({ description: 'Category version', example: 'v2' })
  @Column({ default: 'v2' })
  version: string;

  @ApiProperty({
    description: 'Permission statuses for this category',
    example: ['AVAILABLE', 'INVITE_ONLY'],
  })
  @Column('simple-array', { nullable: true })
  permissionStatuses: string[];

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
