import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../auth/entities/user.entity';
import { Category } from './category.entity';
import { Brand } from './brand.entity';

/**
 * Entity representing a product template that can be used to quickly create staged products
 */
@Entity('templates')
export class Template {
  @ApiProperty({ description: 'Internal template ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Template name',
    example: 'Gildan 1-sided T-shirt',
  })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({
    description: 'Product description template',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  @Column({ type: 'text' })
  description: string;

  @ApiProperty({ description: 'Category ID', example: 1 })
  @Column()
  categoryId: number;

  @ApiProperty({ description: 'Brand ID', example: 1 })
  @Column()
  brandId: number;

  @ApiProperty({
    description: 'Whether this template is available for all users to use',
    example: false,
    default: false,
  })
  @Column({ default: false })
  isPublic: boolean;

  @ApiProperty({ description: 'Product images' })
  @Column('jsonb')
  images: Array<{
    imageUrl?: string;
    r2Key?: string;
  }>;

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
  })
  @Column('jsonb')
  productAttributes: Array<{
    id: string;
    name?: string;
    values: Array<{
      id?: string;
      name?: string;
    }>;
  }>;

  @ApiProperty({ description: 'Product SKUs template' })
  @Column('jsonb')
  skus: Array<{
    salesAttributes: Array<{
      name: string;
      valueName: string;
      skuImg?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      };
      supplementarySkuImages?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      }[];
    }>;
    inventory: Array<{
      quantity: number;
    }>;
    price: {
      amount: number;
      currency: string;
    };
    listPrice?: {
      amount: number;
      currency: string;
    };
    identifierCode?: {
      code: string;
      type: string;
    };
    sellerSku?: string;
  }>;

  @ApiProperty({
    description: 'Size chart information template',
    required: false,
  })
  @Column('jsonb', { nullable: true })
  sizeChart?: {
    imageUrl?: string;
    r2Key?: string;
  };

  @ApiProperty({
    description: 'Package dimensions template',
    example: {
      length: '20',
      width: '15',
      height: '5',
      unit: 'CENTIMETER',
    },
  })
  @Column('jsonb')
  packageDimensions: {
    length: string;
    width: string;
    height: string;
    unit: string;
  };

  @ApiProperty({
    description: 'Package weight template',
    example: {
      value: '500',
      unit: 'GRAM',
    },
  })
  @Column('jsonb')
  packageWeight: {
    value: string;
    unit: string;
  };

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The ID of the user who owns this template',
    example: 1,
  })
  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Category)
  @JoinColumn({ name: 'categoryId' })
  category: Category;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;
}
