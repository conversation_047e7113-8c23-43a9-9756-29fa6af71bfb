import {
  Enti<PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('brands')
export class Brand {
  @ApiProperty({ description: 'Brand ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Brand ID from TikTok Shop',
    example: '7082427311584347905',
  })
  @Column({ unique: true })
  idTT: string;

  @ApiProperty({ description: 'Brand name', example: 'Nike' })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Brand authorized status',
    example: 'AUTHORIZED',
  })
  @Column({ nullable: true })
  authorizedStatus: string;

  @ApiProperty({ description: 'Brand status', example: 'ACTIVE' })
  @Column({ nullable: true })
  status: string;

  @ApiProperty({
    description: 'Whether this is a T1 brand (internationally renowned)',
    example: true,
  })
  @Column({ nullable: true })
  isT1Brand: boolean;

  @ApiProperty({
    description: 'Brand image URL',
    example: 'https://example.com/brand.jpg',
  })
  @Column({ nullable: true })
  imageUrl: string;

  @ApiProperty({
    description: 'Brand authorization letter URL',
    example: 'https://example.com/authorization.pdf',
  })
  @Column({ nullable: true })
  authorizationLetterUrl: string;

  @ApiProperty({
    description: 'Brand authorization start date',
    example: '2023-01-01',
  })
  @Column({ nullable: true })
  authorizationStartDate: string;

  @ApiProperty({
    description: 'Brand authorization end date',
    example: '2023-12-31',
  })
  @Column({ nullable: true })
  authorizationEndDate: string;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
