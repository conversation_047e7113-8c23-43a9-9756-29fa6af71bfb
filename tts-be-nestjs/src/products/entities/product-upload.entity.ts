import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { StagedProduct } from './staged-product.entity';
import { Product } from './product.entity';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';

/**
 * Status of a product upload
 */
export enum ProductUploadStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

/**
 * Entity representing an upload of a staged product to a specific TikTok shop
 */
@Entity('product_uploads')
export class ProductUpload {
  @ApiProperty({ description: 'Internal product upload ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'Staged product ID', example: 1 })
  @Column()
  stagedProductId: number;

  @ApiProperty({ description: 'TikTok shop ID', example: 1 })
  @Column()
  tiktokShopId: number;

  @ApiProperty({
    description: 'Upload status',
    enum: ProductUploadStatus,
    example: ProductUploadStatus.PENDING,
  })
  @Column({
    type: 'enum',
    enum: ProductUploadStatus,
    default: ProductUploadStatus.PENDING,
  })
  status: ProductUploadStatus;

  @ApiProperty({
    description: 'TikTok product ID (if successful)',
    example: '7082427311584347905',
    required: false,
  })
  @Column({ nullable: true })
  tiktokProductId?: string;

  @ApiProperty({ description: 'Error message (if failed)', required: false })
  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @ApiProperty({
    description: 'Background job ID',
    example: '123',
    required: false,
  })
  @Column({ nullable: true })
  jobId?: string;

  @ApiProperty({ description: 'Upload progress (0-100)', example: 50 })
  @Column({ default: 0 })
  progress: number;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({ description: 'Completed Date', required: false })
  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @ApiProperty({
    description: 'Product images processed for this specific upload',
  })
  @Column('jsonb', { nullable: true })
  processedImages?: Array<{
    uri?: string;
    url?: string;
    tiktokUrl?: string;
    width?: number;
    height?: number;
    useCase?: string;
  }>;

  @ApiProperty({
    description: 'Size chart information for this specific upload',
    required: false,
  })
  @Column('jsonb', { nullable: true })
  sizeChart?: {
    uri?: string;
    tiktokUrl?: string;
    width?: number;
    height?: number;
    useCase?: string;
  };

  @ApiProperty({
    description: 'SKU images processed for this specific upload',
  })
  @Column('jsonb', { nullable: true })
  processedSkuImages?: Array<{  
    imageUrl?: string;
    r2Key?: string;
    uri?: string;
    tiktokUrl?: string;
    width?: number;
    height?: number;
    useCase?: string;
  }>;

  @ManyToOne(() => StagedProduct, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'stagedProductId' })
  stagedProduct: StagedProduct;

  @ApiProperty({
    description: 'Product ID (after successful upload)',
    example: 1,
    required: false,
  })
  @Column({ nullable: true })
  productId?: number;

  @ManyToOne(() => Product, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'productId' })
  product?: Product;

  @ApiProperty({
    description: 'TikTok Shop information',
    required: false,
  })
  @ManyToOne(() => TikTokShop, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tiktokShopId' })
  tiktokShop?: TikTokShop;
}
