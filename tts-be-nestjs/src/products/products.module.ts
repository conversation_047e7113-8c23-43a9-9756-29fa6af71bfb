import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { Product } from './entities/product.entity';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { TikTokApplication } from 'src/tiktok-shop/entities/tiktok-application.entity';
import { Sku } from './entities/sku.entity';
import { ProductImage } from './entities/product-image.entity';
import { Category } from './entities/category.entity';
import { Brand } from './entities/brand.entity';
import { Attribute } from './entities/attribute.entity';
import { StagedProduct } from './entities/staged-product.entity';
import { ProductUpload } from './entities/product-upload.entity';
import { Template } from './entities/template.entity';
import { CrawledProduct } from './entities/crawled-product.entity';
import { CrawlSchedule } from './entities/crawl-schedule.entity';
import { TikTokProductMapper } from './mappers/tiktok-product.mapper';
import { ImageUploadService } from './services/image-upload.service';
import { ImageUploadController } from './controllers/image-upload.controller';
import { ProductMetadataService } from './services/product-metadata.service';
import { ProductMetadataController } from './controllers/product-metadata.controller';
import { CrawledProductService } from './services/crawled-product.service';
import { CrawlScheduleService } from './services/crawl-schedule.service';
import { CrawledProductController } from './controllers/crawled-product.controller';
import { CrawlScheduleController } from './controllers/crawl-schedule.controller';

import { SkuManagementController } from './controllers/sku-management.controller';
import { SkuManagementService } from './services/sku-management.service';
import { StagedProductController } from './controllers/staged-product.controller';
import { StagedProductService } from './services/staged-product.service';
import { ProductUploadController } from './controllers/product-upload.controller';
import { ProductUploadService } from './services/product-upload.service';
import { TemplateController } from './controllers/template.controller';
import { TemplateService } from './services/template.service';
import { TiktokShopModule } from 'src/tiktok-shop/tiktok-shop.module';
import { QueuesModule } from 'src/queues';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { TempFileStorageService } from 'src/common/services/temp-file-storage.service';
import { AuthModule } from 'src/auth/auth.module';
import { User } from 'src/auth/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      Sku,
      ProductImage,
      Category,
      Brand,
      Attribute,
      StagedProduct,
      ProductUpload,
      Template,
      CrawledProduct,
      CrawlSchedule,
      TikTokApplication,
      TikTokShop,
      User,
    ]),
    TiktokShopModule,
    QueuesModule,
    AuthModule,
  ],
  controllers: [
    ProductsController,
    ImageUploadController,
    ProductMetadataController,
    SkuManagementController,
    StagedProductController,
    ProductUploadController,
    TemplateController,
    CrawledProductController,
    CrawlScheduleController,
  ],
  providers: [
    ProductsService,
    TikTokClientFactory,
    TikTokProductMapper,
    ImageUploadService,
    ProductMetadataService,
    SkuManagementService,
    StagedProductService,
    ProductUploadService,
    TemplateService,
    CrawledProductService,
    CrawlScheduleService,
    TempFileStorageService,
  ],
  exports: [
    ProductsService,
    ImageUploadService,
    ProductMetadataService,
    SkuManagementService,
    StagedProductService,
    ProductUploadService,
    TemplateService,
    CrawledProductService,
    CrawlScheduleService,
  ],
})
export class ProductsModule {}
