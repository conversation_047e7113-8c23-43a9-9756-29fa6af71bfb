import { Injectable, Logger } from '@nestjs/common';
import { CreateProductDto, CreateSkuDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { ProductStatus } from '../entities/product.entity';
import { getProperty } from '../../common/utils/property-name.util';

/**
 * Mapper class for transforming TikTok API responses to DTOs
 */
@Injectable()
export class TikTokProductMapper {
  private readonly logger = new Logger(TikTokProductMapper.name);

  /**
   * Maps TikTok Shop product data to CreateProductDto
   * @param productData Raw product data from TikTok API
   * @param tiktokShopId TikTok Shop ID from internal system
   * @returns CreateProductDto
   */
  mapToCreateProductDto(
    productData: any,
    tiktokShopId: number,
  ): CreateProductDto {
    // Extract product data
    const productDto: CreateProductDto = {
      idTT: productData.id,
      title: productData.title || '',
      status: this.mapProductStatus(productData.status),
      tiktokShopId: tiktokShopId,
      skus: [],
      // Optional fields with property name handling
      salesRegions: getProperty(productData, 'salesRegions', []),
      productSyncFailReasons: getProperty(
        productData,
        'productSyncFailReasons',
        [],
      ),
      isNotForSale: getProperty(productData, 'isNotForSale', false),
      listingQualityTier: getProperty<string>(
        productData,
        'listingQualityTier',
        '',
      ),
      // Store create and update time if available
      createTimeTT: getProperty(productData, 'createTime'),
      updateTimeTT: getProperty(productData, 'updateTime'),
      // Store the complete raw TikTok response for debugging and future extensibility
      rawTikTokResponse: productData,
    };

    // Process recommended categories if available
    const recommendedCategories = getProperty(
      productData,
      'recommendedCategories',
    );
    if (recommendedCategories && recommendedCategories.length > 0) {
      productDto.recommendedCategories = recommendedCategories.map(
        (cat: any) => ({
          id: cat.id,
          localName: getProperty(cat, 'localName', ''),
        }),
      );
    }

    // Process integrated platform statuses if available
    const integratedPlatformStatuses = getProperty(
      productData,
      'integratedPlatformStatuses',
    );
    if (integratedPlatformStatuses && integratedPlatformStatuses.length > 0) {
      productDto.integratedPlatformStatuses = integratedPlatformStatuses.map(
        (status: any) => ({
          platform: status.platform,
          status: status.status,
        }),
      );
    }

    // Process audit status if available
    if (productData.audit) {
      productDto.audit = {
        status: productData.audit.status,
      };
    }

    return productDto;
  }

  /**
   * Maps TikTok Shop SKU data to CreateSkuDto
   * @param skuData Raw SKU data from TikTok API
   * @returns CreateSkuDto
   */
  mapToCreateSkuDto(skuData: any): CreateSkuDto {
    // Extract SKU data
    const skuDto: CreateSkuDto = {
      idTT: skuData.id,
      sellerSku: getProperty<string>(skuData, 'sellerSku', '') || '',
      inventory: [],
      price: {
        salePrice: null,
        taxExclusivePrice: null,
        currency: 'USD',
      },
      listPrice: null,
    };

    // Process inventory if available
    if (skuData.inventory && skuData.inventory.length > 0) {
      skuDto.inventory = skuData.inventory.map((inv: any) => ({
        quantity: inv.quantity || 0,
        warehouseId: getProperty(inv, 'warehouseId', ''),
      }));
    }

    // Process price if available
    if (skuData.price) {
      skuDto.price = {
        taxExclusivePrice:
          getProperty(skuData.price, 'taxExclusivePrice') !== undefined
            ? parseFloat(
                getProperty<string>(skuData.price, 'taxExclusivePrice') || '0',
              )
            : null,
        salePrice:
          getProperty(skuData.price, 'salePrice') !== undefined
            ? parseFloat(getProperty<string>(skuData.price, 'salePrice') || '0')
            : null,
        currency: skuData.price.currency || 'USD',
        unitPrice:
          getProperty(skuData.price, 'unitPrice') !== undefined
            ? parseFloat(getProperty<string>(skuData.price, 'unitPrice') || '0')
            : null,
      };
    }

    // Process list price if available
    const listPrice = getProperty(skuData, 'listPrice');
    if (listPrice) {
      skuDto.listPrice = {
        amount: parseFloat(listPrice.amount) || null,
        currency: listPrice.currency || 'USD',
      };
    }

    // Process external list prices if available
    const externalListPrices = getProperty(skuData, 'externalListPrices');
    if (externalListPrices && externalListPrices.length > 0) {
      skuDto.externalListPrices = externalListPrices.map((price: any) => ({
        amount: parseFloat(price.amount) || 0,
        currency: price.currency || 'USD',
        source: price.source || '',
      }));
    }

    // Process sales attributes if available
    const salesAttributes = getProperty(skuData, 'salesAttributes');
    if (salesAttributes && salesAttributes.length > 0) {
      skuDto.salesAttributes = this.mapSalesAttributes(salesAttributes);
    }

    // Process additional properties
    skuDto.externalSkuId = getProperty(skuData, 'externalSkuId');

    // Process combined SKUs if available
    const combinedSkus = getProperty(skuData, 'combinedSkus');
    if (combinedSkus && combinedSkus.length > 0) {
      skuDto.combinedSkus = combinedSkus.map((cs: any) => ({
        productId: getProperty(cs, 'productId', ''),
        skuId: getProperty(cs, 'skuId', ''),
        skuCount: getProperty(cs, 'skuCount', 1),
      }));
    }

    // Process global listing policy if available
    const globalListingPolicy = getProperty(skuData, 'globalListingPolicy');
    if (globalListingPolicy) {
      skuDto.globalListingPolicy = {
        priceSync: getProperty(globalListingPolicy, 'priceSync', false),
        inventoryType: getProperty(globalListingPolicy, 'inventoryType', ''),
      };

      // Process replicate source if available
      const replicateSource = getProperty(
        globalListingPolicy,
        'replicateSource',
      );
      if (replicateSource) {
        skuDto.globalListingPolicy.replicateSource = {
          productId:
            getProperty<string>(replicateSource, 'productId', '') || '',
          shopId: getProperty<string>(replicateSource, 'shopId', '') || '',
          skuId: getProperty<string>(replicateSource, 'skuId', '') || '',
        };
      }
    }

    // Process additional fields
    skuDto.skuUnitCount = getProperty(skuData, 'skuUnitCount');
    skuDto.externalUrls = getProperty(skuData, 'externalUrls');

    // Process pre-sale information if available
    const preSale = getProperty(skuData, 'preSale');
    if (preSale) {
      skuDto.preSale = {
        type: preSale.type || '',
      };

      // Process fulfillment type if available
      const fulfillmentType = getProperty(preSale, 'fulfillmentType');
      if (fulfillmentType) {
        skuDto.preSale.fulfillmentType = {
          handlingDurationDays:
            getProperty<number>(fulfillmentType, 'handlingDurationDays', 0) ||
            0,
        };
      }
    }

    // Process identifier code if available
    const identifierCode = getProperty(skuData, 'identifierCode');
    if (identifierCode) {
      skuDto.identifierCode = {
        code: identifierCode.code || '',
        type: identifierCode.type || '',
      };
    }

    // Process extra identifier codes if available
    skuDto.extraIdentifierCodes = getProperty(skuData, 'extraIdentifierCodes');

    return skuDto;
  }

  /**
   * Maps TikTok Shop detailed product data to UpdateProductDto
   * @param productData Raw detailed product data from TikTok API
   * @param tiktokShopId TikTok Shop ID from internal system
   * @returns UpdateProductDto
   */
  mapToUpdateProductDto(
    productData: any,
    tiktokShopId: number,
  ): UpdateProductDto {
    // Create base product DTO
    const updateProductDto: UpdateProductDto = {
      idTT: productData.id,
      title: productData.title,
      status: this.mapProductStatus(productData.status),
      tiktokShopId: tiktokShopId,
      skus: [],
      // Store create and update time
      createTimeTT: getProperty(productData, 'createTime'),
      updateTimeTT: getProperty(productData, 'updateTime'),
      // Add description if available
      description: productData.description,
      // Store the complete raw TikTok response for debugging and future extensibility
      rawTikTokResponse: productData,
    };

    // Process category chains if available
    const categoryChains = getProperty(productData, 'categoryChains');
    if (categoryChains && categoryChains.length > 0) {
      // Create category path
      updateProductDto.category = categoryChains
        .map((cat: any) => getProperty(cat, 'localName', ''))
        .join('/');

      // Store full category chains data
      updateProductDto.categoryChains = categoryChains.map((cat: any) => ({
        id: cat.id,
        parentId: getProperty(cat, 'parentId', ''),
        localName: getProperty(cat, 'localName', ''),
        isLeaf: getProperty(cat, 'isLeaf', false),
      }));
    }

    // Process brand if available
    if (productData.brand) {
      updateProductDto.brandInfo = productData.brand.name;
      updateProductDto.brand = {
        id: productData.brand.id || '',
        name: productData.brand.name || '',
      };
    }

    // Handle main images
    const mainImages = getProperty(productData, 'mainImages');
    if (mainImages) {
      // Store the full main_images data
      updateProductDto.mainImages = mainImages;

      // Extract URLs for the simple productImages array
      const imageUrls: string[] = [];
      mainImages.forEach((img: any) => {
        if (img && img.urls && img.urls.length > 0) {
          imageUrls.push(img.urls[0]);
        }
      });

      if (imageUrls.length > 0) {
        updateProductDto.productImages = imageUrls;
      }
    }

    // Handle video information
    if (productData.video) {
      updateProductDto.videoInfo = productData.video;
    }

    // Handle size chart
    const sizeChart = getProperty(productData, 'sizeChart');
    if (sizeChart) {
      updateProductDto.sizeChart = sizeChart;
    }

    // Handle package dimensions
    const packageDimensions = getProperty(productData, 'packageDimensions');
    if (packageDimensions) {
      updateProductDto.packageDimensions = packageDimensions;

      // Extract values for the simple package dimension fields
      if (packageDimensions.length) {
        updateProductDto.packageLength = parseFloat(packageDimensions.length);
      }
      if (packageDimensions.width) {
        updateProductDto.packageWidth = parseFloat(packageDimensions.width);
      }
      if (packageDimensions.height) {
        updateProductDto.packageHeight = parseFloat(packageDimensions.height);
      }
    }

    // Handle package weight
    const packageWeight = getProperty(productData, 'packageWeight');
    if (packageWeight) {
      updateProductDto.packageWeight = packageWeight;
    }

    // Handle additional fields
    updateProductDto.isCodAllowed = getProperty(productData, 'isCodAllowed');
    updateProductDto.productAttributes = getProperty(
      productData,
      'productAttributes',
    );

    // Handle audit
    if (productData.audit) {
      updateProductDto.audit = {
        status: productData.audit.status,
        preApprovedReasons: getProperty(
          productData.audit,
          'preApprovedReasons',
        ),
      };
    }

    // Handle additional properties
    updateProductDto.auditFailedReasons = getProperty(
      productData,
      'auditFailedReasons',
    );
    updateProductDto.deliveryOptions = getProperty(
      productData,
      'deliveryOptions',
    );
    updateProductDto.externalProductId = getProperty(
      productData,
      'externalProductId',
    );
    updateProductDto.productTypes = getProperty(productData, 'productTypes');
    updateProductDto.listingQualityTier = getProperty(
      productData,
      'listingQualityTier',
    );
    updateProductDto.integratedPlatformStatuses = getProperty(
      productData,
      'integratedPlatformStatuses',
    );
    updateProductDto.manufacturerIds = getProperty(
      productData,
      'manufacturerIds',
    );
    updateProductDto.responsiblePersonIds = getProperty(
      productData,
      'responsiblePersonIds',
    );
    updateProductDto.shippingInsuranceRequirement = getProperty(
      productData,
      'shippingInsuranceRequirement',
    );
    updateProductDto.minimumOrderQuantity = getProperty(
      productData,
      'minimumOrderQuantity',
    );
    updateProductDto.isPreOwned = getProperty(productData, 'isPreOwned');
    updateProductDto.isNotForSale = getProperty(productData, 'isNotForSale');
    updateProductDto.certifications = getProperty(
      productData,
      'certifications',
    );
    updateProductDto.globalProductAssociation = getProperty(
      productData,
      'globalProductAssociation',
    );

    // Process SKUs if available
    if (productData.skus && productData.skus.length > 0) {
      this.logger.log(
        `Processing ${productData.skus.length} SKUs for product ${productData.id}`,
      );
      updateProductDto.skus = productData.skus.map((skuData: any) =>
        this.mapToCreateSkuDto(skuData),
      );
    }

    return updateProductDto;
  }

  /**
   * Maps TikTok Shop product status to internal ProductStatus enum
   * @param status TikTok Shop product status
   * @returns ProductStatus enum value
   */
  mapProductStatus(status: string): ProductStatus {
    switch (status) {
      case 'ACTIVATE':
        return ProductStatus.ACTIVATE;
      case 'DRAFT':
        return ProductStatus.DRAFT;
      case 'PENDING':
        return ProductStatus.PENDING;
      case 'FAILED':
        return ProductStatus.FAILED;
      case 'SELLER_DEACTIVATED':
        return ProductStatus.SELLER_DEACTIVATED;
      case 'PLATFORM_DEACTIVATED':
        return ProductStatus.PLATFORM_DEACTIVATED;
      case 'FREEZE':
        return ProductStatus.FREEZE;
      case 'DELETED':
        return ProductStatus.DELETED;
      case 'AUDITING':
        return ProductStatus.AUDITING;
      default:
        return ProductStatus.DRAFT;
    }
  }

  /**
   * Maps sales attributes from TikTok API response
   * @param salesAttrs Sales attributes from TikTok API
   * @returns Mapped sales attributes
   */
  private mapSalesAttributes(salesAttrs: any[]): any[] {
    return salesAttrs.map((attr: any) => {
      const salesAttr: any = {
        id: attr.id || '',
        name: attr.name || '',
        valueId: getProperty(attr, 'valueId', ''),
        valueName: getProperty(attr, 'valueName', ''),
      };

      // Process SKU image if available
      const skuImg = getProperty(attr, 'skuImg');
      if (skuImg) {
        salesAttr.skuImg = {
          height: skuImg.height,
          width: skuImg.width,
          thumbUrls: getProperty(skuImg, 'thumbUrls', []),
          uri: skuImg.uri || '',
          urls: skuImg.urls || [],
        };
      }

      // Process supplementary SKU images if available
      const supplementarySkuImages = getProperty(
        attr,
        'supplementarySkuImages',
      );
      if (supplementarySkuImages && supplementarySkuImages.length > 0) {
        salesAttr.supplementarySkuImages = supplementarySkuImages.map(
          (img: any) => ({
            height: img.height,
            width: img.width,
            thumbUrls: getProperty(img, 'thumbUrls', []),
            uri: img.uri || '',
            urls: img.urls || [],
          }),
        );
      }

      return salesAttr;
    });
  }
}
