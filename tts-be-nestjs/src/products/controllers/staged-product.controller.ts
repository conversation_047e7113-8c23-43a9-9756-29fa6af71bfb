import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Patch,
  Logger,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { StagedProductService } from '../services/staged-product.service';
import {
  CreateStagedProductDto,
  CreateStagedProductFromTemplateDto,
  CreateStagedProductFromTemplateAndUploadDto,
  StagedProductResponseDto,
  UpdateStagedProductDto,
} from '../dto/staged-product.dto';
import { ProductUploadResponseDto } from '../dto/product-upload.dto';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import {
  StagedProductQueryDto,
  StagedProductWithLatestUploadDto,
} from '../dto/staged-product-query.dto';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@ApiTags('Staged Products')
@Controller('staged-products')
export class StagedProductController {
  private readonly logger = new Logger(StagedProductController.name);

  constructor(private readonly stagedProductService: StagedProductService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new staged product' })
  @ApiResponse({
    status: 201,
    description: 'The staged product has been successfully created',
    type: StagedProductResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createStagedProduct(
    @Body() createStagedProductDto: CreateStagedProductDto,
    @CurrentUser('id') userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(`Creating new staged product for user: ${userId}`);
    return this.stagedProductService.createStagedProduct(
      createStagedProductDto,
      userId,
    );
  }

  @Post('from-template')
  @ApiOperation({ summary: 'Create a new staged product from a template' })
  @ApiResponse({
    status: 201,
    description:
      'The staged product has been successfully created from template',
    type: StagedProductResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async createStagedProductFromTemplate(
    @Body() createFromTemplateDto: CreateStagedProductFromTemplateDto,
    @CurrentUser('id') userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Creating staged product from template ${createFromTemplateDto.templateId} for user: ${userId}`,
    );
    return this.stagedProductService.createStagedProductFromTemplate(
      createFromTemplateDto,
      userId,
    );
  }

  @Post('from-template-and-upload')
  @ApiOperation({
    summary: 'Create a new staged product from a template and immediately upload to TikTok Shop'
  })
  @ApiResponse({
    status: 201,
    description:
      'The staged product has been successfully created from template and queued for upload',
    type: ProductUploadResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Template or TikTok shop not found' })
  async createStagedProductFromTemplateAndUpload(
    @Body() createFromTemplateAndUploadDto: CreateStagedProductFromTemplateAndUploadDto,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Creating staged product from template ${createFromTemplateAndUploadDto.templateId} and uploading to shop ${createFromTemplateAndUploadDto.tiktokShopId} for user: ${userId}`,
    );
    return this.stagedProductService.createStagedProductFromTemplateAndUpload(
      createFromTemplateAndUploadDto,
      userId,
    );
  }

  @Get('all-staged-products')
  @ApiOperation({
    summary: 'Get all staged products with pagination, filtering, and sorting',
  })
  @ApiResponse({
    status: 200,
    description: 'List of staged products',
    type: [StagedProductWithLatestUploadDto],
  })
  async getAllStagedProducts(
    @Query() queryDto: StagedProductQueryDto,
    @CurrentUser('id') userId: number,
  ): Promise<PaginatedResult<StagedProductWithLatestUploadDto>> {
    return this.stagedProductService.getAllStagedProducts(queryDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a staged product by ID' })
  @ApiResponse({
    status: 200,
    description: 'The staged product',
    type: StagedProductResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Staged product not found' })
  async getStagedProductById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Getting staged product with ID: ${id} for user: ${userId}`,
    );
    return this.stagedProductService.getStagedProductById(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a staged product' })
  @ApiResponse({
    status: 200,
    description: 'The staged product has been successfully updated',
    type: StagedProductResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Staged product not found' })
  async updateStagedProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStagedProductDto: UpdateStagedProductDto,
    @CurrentUser('id') userId: number,
  ): Promise<StagedProductResponseDto> {
    this.logger.log(
      `Updating staged product with ID: ${id} for user: ${userId}`,
    );
    return this.stagedProductService.updateStagedProduct(
      id,
      updateStagedProductDto,
      userId,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a staged product' })
  @ApiResponse({
    status: 200,
    description: 'The staged product has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Staged product not found' })
  async deleteStagedProduct(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<void> {
    this.logger.log(
      `Deleting staged product with ID: ${id} for user: ${userId}`,
    );
    return this.stagedProductService.deleteStagedProduct(id, userId);
  }

  @Get(':id/uploads')
  @ApiOperation({ summary: 'Get upload history for a specific staged product' })
  @ApiResponse({
    status: 200,
    description: 'List of product uploads for the staged product',
    type: [ProductUploadResponseDto],
  })
  @ApiResponse({ status: 404, description: 'Staged product not found' })
  async getStagedProductUploads(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadResponseDto[]> {
    this.logger.log(
      `Getting upload history for staged product with ID: ${id} for user: ${userId}`,
    );
    return this.stagedProductService.getStagedProductUploads(id, userId);
  }
}
