import { <PERSON>, Get, Query, Logger, ParseInt<PERSON><PERSON><PERSON>, Param } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { ProductMetadataService } from '../services/product-metadata.service';
import { CategoryQueueService } from '../../queues/services/category-queue.service';
import { CategoryResponseDto, CategoryDto } from '../dto/category.dto';
import { BrandResponseDto, BrandDto } from '../dto/brand.dto';
import { AttributeResponseDto } from '../dto/attribute.dto';
import { RoleGuard } from '../../auth/guards/role.guard';
import { UseGuards } from '@nestjs/common';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/auth/enums/user-role.enum';

@ApiTags('Product Metadata')
@Controller('products/metadata')
@UseGuards(RoleGuard)
export class ProductMetadataController {
  private readonly logger = new Logger(ProductMetadataController.name);

  constructor(
    private readonly productMetadataService: ProductMetadataService,
    private readonly categoryQueueService: CategoryQueueService,
  ) {}

  @Get('categories/sync')
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary:
      'Schedule a background job to synchronize product categories from TikTok Shop',
  })
  @ApiResponse({
    status: 200,
    description: 'Job information for the scheduled category synchronization',
    schema: {
      type: 'object',
      properties: {
        jobId: { type: 'string', description: 'ID of the scheduled job' },
        status: { type: 'string', description: 'Status of the job' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'TikTok Shop not found' })
  async synchronizeCategories(
    @Query('tiktokShopId', ParseIntPipe) tiktokShopId: number,
  ) {
    return this.categoryQueueService.addCategorySyncJob(tiktokShopId);
  }

  @Get('categories/sync/status')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get the status of a category synchronization job' })
  @ApiQuery({
    name: 'jobId',
    required: true,
    type: String,
    description: 'Job ID to check status for',
  })
  @ApiResponse({
    status: 200,
    description: 'Job status information',
    schema: {
      type: 'object',
      properties: {
        exists: { type: 'boolean', description: 'Whether the job exists' },
        id: { type: 'string', description: 'Job ID' },
        state: { type: 'string', description: 'Current state of the job' },
        progress: {
          type: 'number',
          description: 'Progress percentage (0-100)',
        },
        data: { type: 'object', description: 'Job input data' },
        returnvalue: {
          type: 'object',
          description: 'Job return value (if completed)',
        },
        failedReason: {
          type: 'string',
          description: 'Reason for failure (if failed)',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getCategorySyncStatus(@Query('jobId') jobId: string) {
    return this.categoryQueueService.getJobStatus(jobId);
  }

  @Get('categories')
  @ApiOperation({
    summary:
      'Get categories from database with optional filters (limited to 100 results)',
    description:
      'Retrieve categories with optional filtering by localName, isLeaf, and permissionStatuses. Results are limited to 100 categories ordered by name.',
    parameters: [
      {
        name: 'localName',
        in: 'query',
        description: 'Filter categories by name (optional, partial match)',
        required: false,
        schema: { type: 'string' },
      },
      {
        name: 'isLeaf',
        in: 'query',
        description: 'Filter categories by isLeaf flag (optional)',
        required: false,
        schema: { type: 'boolean' },
      },
      {
        name: 'permissionStatuses',
        in: 'query',
        description:
          'Filter categories by permission status (optional, comma-separated values like AVAILABLE,INVITE_ONLY)',
        required: false,
        schema: { type: 'string' },
      },
    ],
  })
  @ApiQuery({
    name: 'localName',
    required: false,
    type: String,
    description: 'Filter categories by name (partial match)',
  })
  @ApiQuery({
    name: 'isLeaf',
    required: false,
    type: Boolean,
    description: 'Filter categories by isLeaf flag',
  })
  @ApiQuery({
    name: 'permissionStatuses',
    required: false,
    type: String,
    description:
      'Filter categories by permission status (comma-separated values like AVAILABLE,INVITE_ONLY)',
  })
  @ApiResponse({
    status: 200,
    description: 'List of product categories',
    type: CategoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getCategories(
    @Query('localName') localName?: string,
    @Query('isLeaf') isLeaf?: boolean,
    @Query('permissionStatuses') permissionStatuses?: string,
  ): Promise<CategoryResponseDto> {
    return this.productMetadataService.getCategories(
      localName,
      isLeaf,
      permissionStatuses,
    );
  }

  @Get('brands/sync')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Synchronize brands from TikTok Shop' })
  @ApiResponse({
    status: 200,
    description: 'List of brands',
    type: BrandResponseDto,
  })
  @ApiQuery({
    name: 'brandName',
    required: false,
    type: String,
    description: 'Filter brands by name',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter brands by category ID',
  })
  @ApiQuery({
    name: 'tiktokShopId',
    required: true,
    type: Number,
    description: 'TikTok Shop ID',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'TikTok Shop not found' })
  async synchronizeBrands(
    @Query('tiktokShopId', ParseIntPipe) tiktokShopId: number,
    @Query('brandName') brandName?: string,
    @Query('categoryId') categoryId?: string,
  ): Promise<BrandResponseDto> {
    return this.productMetadataService.synchronizeBrands(
      tiktokShopId,
      brandName,
      categoryId,
    );
  }

  @Get('brands')
  @ApiOperation({
    summary:
      'Get brands from database with optional filters (limited to 100 results)',
    description:
      'Retrieve brands with optional filtering by brandName, isT1Brand, authorizedStatus, and brandStatus. Results are limited to 100 brands ordered by name.',
    parameters: [
      {
        name: 'brandName',
        in: 'query',
        description: 'Filter brands by name (optional, partial match)',
        required: false,
        schema: { type: 'string' },
      },
      {
        name: 'isT1Brand',
        in: 'query',
        description: 'Filter brands by isT1Brand flag (optional)',
        required: false,
        schema: { type: 'boolean' },
      },
      {
        name: 'authorizedStatus',
        in: 'query',
        description: 'Filter brands by authorization status (optional)',
        required: false,
        schema: { type: 'string' },
      },
      {
        name: 'brandStatus',
        in: 'query',
        description: 'Filter brands by brand status (optional)',
        required: false,
        schema: { type: 'string' },
      },
    ],
  })
  @ApiResponse({
    status: 200,
    description: 'List of brands',
    type: BrandResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiQuery({
    name: 'brandName',
    required: false,
    type: String,
    description: 'Filter brands by name (partial match)',
  })
  @ApiQuery({
    name: 'isT1Brand',
    required: false,
    type: Boolean,
    description: 'Filter brands by isT1Brand flag',
  })
  @ApiQuery({
    name: 'authorizedStatus',
    required: false,
    type: String,
    description: 'Filter brands by authorization status',
  })
  @ApiQuery({
    name: 'brandStatus',
    required: false,
    type: String,
    description: 'Filter brands by brand status',
  })
  async getBrands(
    @Query('brandName') brandName?: string,
    @Query('isT1Brand') isT1Brand?: boolean,
    @Query('authorizedStatus') authorizedStatus?: string,
    @Query('brandStatus') brandStatus?: string,
  ): Promise<BrandResponseDto> {
    return this.productMetadataService.getBrands(
      brandName,
      isT1Brand,
      authorizedStatus,
      brandStatus,
    );
  }

  @Get('attributes/sync')
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: 'Synchronize product attributes from Category ID Tiktok',
  })
  @ApiResponse({
    status: 200,
    description: 'List of product attributes',
    type: AttributeResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'TikTok Shop not found' })
  async synchronizeAttributes(
    @Query('tiktokShopId', ParseIntPipe) tiktokShopId: number,
    @Query('categoryIdTT') categoryIdTT: string,
  ): Promise<AttributeResponseDto> {
    return this.productMetadataService.synchronizeAttributes(
      tiktokShopId,
      categoryIdTT,
    );
  }

  @Get('attributes')
  @ApiOperation({
    summary: 'Get product attributes from database with optional filters',
    description:
      'Retrieve product attributes with optional filtering by categoryIdTT, isRequired, and isSalesAttr',
    parameters: [
      {
        name: 'categoryIdTT',
        in: 'query',
        description: 'Filter attributes by category ID Tiktok(optional)',
        required: false,
        schema: { type: 'string' },
      },
      {
        name: 'isRequired',
        in: 'query',
        description: 'Filter attributes by isRequired flag (optional)',
        required: false,
        schema: { type: 'boolean' },
      },
      {
        name: 'isSalesAttr',
        in: 'query',
        description: 'Filter attributes by isSalesAttr flag (optional)',
        required: false,
        schema: { type: 'boolean' },
      },
    ],
  })
  @ApiQuery({
    name: 'categoryIdTT',
    required: false,
    type: String,
    description: 'Filter attributes by category ID Tiktok',
  })
  @ApiQuery({
    name: 'isRequired',
    required: false,
    type: Boolean,
    description: 'Filter attributes by isRequired flag',
  })
  @ApiQuery({
    name: 'isSalesAttr',
    required: false,
    type: Boolean,
    description: 'Filter attributes by isSalesAttr flag',
  })
  @ApiResponse({
    status: 200,
    description: 'List of product attributes',
    type: AttributeResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getAttributes(
    @Query('categoryIdTT') categoryIdTT?: string,
    @Query('isRequired') isRequired?: boolean,
    @Query('isSalesAttr') isSalesAttr?: boolean,
  ): Promise<AttributeResponseDto> {
    return this.productMetadataService.getAttributes(
      categoryIdTT,
      isRequired,
      isSalesAttr,
    );
  }

  @Get('categories/:id')
  @ApiOperation({
    summary: 'Get a single category by ID',
    description: 'Retrieve a specific category by its database ID',
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'Category ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Category details',
    type: CategoryDto,
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getCategoryById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<CategoryDto> {
    return this.productMetadataService.getCategoryById(id);
  }

  @Get('brands/:id')
  @ApiOperation({
    summary: 'Get a single brand by ID',
    description: 'Retrieve a specific brand by its database ID',
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'Brand ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Brand details',
    type: BrandDto,
  })
  @ApiResponse({ status: 404, description: 'Brand not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getBrandById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BrandDto> {
    return this.productMetadataService.getBrandById(id);
  }
}
