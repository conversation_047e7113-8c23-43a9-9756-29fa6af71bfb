import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Logger,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SkuManagementService } from '../services/sku-management.service';
import {
  UpdateSkuInventoryDto,
  UpdateSkuInventoryResponseDto,
} from '../dto/update-sku-inventory.dto';
import { SkuDto, SkuResponseDto } from '../dto/sku.dto';

//TO-DO unnecessary 
@ApiTags('SKU Management')
@Controller('products/skus')
export class SkuManagementController {
  private readonly logger = new Logger(SkuManagementController.name);

  constructor(private readonly skuManagementService: SkuManagementService) {}

  @Patch(':id/inventory')
  @ApiOperation({ summary: 'Update SKU inventory' })
  @ApiParam({ name: 'id', description: 'SKU ID' })
  @ApiResponse({
    status: 200,
    description: 'The updated SKU inventory',
    type: UpdateSkuInventoryResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'SKU not found' })
  async updateSkuInventory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSkuInventoryDto: UpdateSkuInventoryDto,
  ): Promise<UpdateSkuInventoryResponseDto> {
    this.logger.log(`Updating inventory for SKU ID: ${id}`);
    return this.skuManagementService.updateSkuInventory(
      id,
      updateSkuInventoryDto,
    );
  }
}
