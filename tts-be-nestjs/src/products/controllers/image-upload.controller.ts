import {
  Body,
  Controller,
  Post,
  Param,
  Logger,
  NotFoundException,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { ImageUploadService } from '../services/image-upload.service';
import {
  UploadImageDto,
  UploadImageResponseDto,
} from '../dto/upload-image.dto';
import {
  R2UploadRequestDto,
  R2PresignedUrlResponseDto,
  R2UploadResponseDto,
} from '../dto/r2-upload.dto';
import { v4 as uuid } from 'uuid';

@ApiTags('Product Images')
@Controller('products/images')
export class ImageUploadController {
  private readonly logger = new Logger(ImageUploadController.name);

  constructor(private readonly imageUploadService: ImageUploadService) {}

  @Post('upload/:tiktokShopId')
  @ApiOperation({ summary: 'Upload an image to TikTok Shop' })
  @ApiResponse({
    status: 201,
    description: 'The image has been successfully uploaded',
    type: UploadImageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'TikTok Shop not found' })
  async uploadImage(
    @Param('tiktokShopId') tiktokShopId: number,
    @Body() uploadImageDto: UploadImageDto,
  ): Promise<UploadImageResponseDto> {
    // Upload the image
    return this.imageUploadService.uploadImage(tiktokShopId, uploadImageDto);
  }

  @Post('upload-to-r2')
  @ApiOperation({ summary: 'Upload a file directly to R2 storage' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({
    status: 201,
    description: 'The file has been successfully uploaded to R2',
    type: R2UploadResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async uploadFileToR2(
    @UploadedFile() file: Express.Multer.File,
    @Body('folder') folder: string = 'uploads',
  ): Promise<R2UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    this.logger.log(
      `Uploading file ${file.originalname} to R2 in folder ${folder}`,
    );

    // Upload to R2
    return this.imageUploadService.uploadFileToR2(file, folder);
  }

  @Post('presigned-url')
  @ApiOperation({
    summary: 'Generate a presigned URL for client-side uploads to R2',
  })
  @ApiResponse({
    status: 201,
    description: 'Presigned URL generated successfully',
    type: R2PresignedUrlResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getPresignedUrl(
    @Body() dto: R2UploadRequestDto,
  ): Promise<R2PresignedUrlResponseDto> {
    this.logger.log(
      `Generating presigned URL for ${dto.fileName} in folder ${dto.folder || 'uploads'}`,
    );

    // Generate presigned URL
    return this.imageUploadService.generatePresignedUrl(dto);
  }
}
