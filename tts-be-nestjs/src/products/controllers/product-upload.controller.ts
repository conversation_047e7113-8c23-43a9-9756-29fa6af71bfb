import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Logger,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ProductUploadService } from '../services/product-upload.service';
import {
  CreateProductUploadDto,
  ProductUploadResponseDto,
  ProductUploadStatusResponseDto,
} from '../dto/product-upload.dto';
import {
  ProductUploadOptionsDto,
  ProductValidationResultDto,
  ValidateProductDto,
} from '../dto/product-validation.dto';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';

@ApiTags('Product Uploads')
@Controller('product-uploads')
export class ProductUploadController {
  private readonly logger = new Logger(ProductUploadController.name);

  constructor(private readonly productUploadService: ProductUploadService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new product upload' })
  @ApiResponse({
    status: 201,
    description: 'The product upload has been successfully created',
    type: ProductUploadResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({
    status: 404,
    description: 'Staged product or TikTok shop not found',
  })
  async createProductUpload(
    @Body()
    createProductUploadDto: CreateProductUploadDto & ProductUploadOptionsDto,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Creating new product upload for staged product ID: ${createProductUploadDto.stagedProductId} and TikTok shop ID: ${createProductUploadDto.tiktokShopId} for user: ${userId}`,
    );
    // Extract options from the combined DTO
    const { skipValidation, forceUpload, ...uploadDto } =
      createProductUploadDto;
    const options = { skipValidation, forceUpload };

    return this.productUploadService.createProductUpload(
      uploadDto as CreateProductUploadDto,
      options,
      userId,
    );
  }

  //to-do: this endpoint need to be tested to assure dont creating product
  @Post('validate')
  @ApiOperation({ summary: 'Validate a product without creating it' })
  @ApiResponse({
    status: 200,
    description: 'Validation results',
    type: ProductValidationResultDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({
    status: 404,
    description: 'Staged product or TikTok shop not found',
  })
  async validateProduct(
    @Body() validateProductDto: ValidateProductDto,
    @CurrentUser('id') userId: number,
  ): Promise<ProductValidationResultDto> {
    this.logger.log(
      `Validating product for staged product ID: ${validateProductDto.stagedProductId} and TikTok shop ID: ${validateProductDto.tiktokShopId} for user: ${userId}`,
    );

    // Create a product upload with skipValidation=false and forceUpload=true
    // This will process the validation but won't fail if validation fails
    const productUpload = await this.productUploadService.createProductUpload(
      {
        stagedProductId: validateProductDto.stagedProductId,
        tiktokShopId: validateProductDto.tiktokShopId,
      },
      {
        skipValidation: false,
        forceUpload: true,
      },
      userId,
    );

    // Return the validation results from the job status
    // This is a simplified implementation - in a real-world scenario,
    // you would need to wait for the validation to complete and retrieve the results
    return {
      isValid: true, // This is a placeholder - the actual validation happens in the background job
      errors: [],
      warnings: [],
      message: `Validation started for product upload ID: ${productUpload.id}. Check the status endpoint for results. Images will be processed and validated in the background job.`,
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Get product uploads by staged product ID',
    description:
      'Note: This endpoint is also available at GET /products/staged/:id/uploads which is the preferred way to access this functionality',
  })
  @ApiQuery({
    name: 'stagedProductId',
    required: true,
    type: Number,
    description: 'Staged product ID',
  })
  @ApiResponse({
    status: 200,
    description: 'List of product uploads for the staged product',
    type: [ProductUploadResponseDto],
  })
  @ApiResponse({ status: 404, description: 'Staged product not found' })
  async getProductUploadsByProductId(
    @Query('stagedProductId', ParseIntPipe) stagedProductId: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadResponseDto[]> {
    this.logger.log(
      `Getting product uploads for staged product ID: ${stagedProductId} for user: ${userId}`,
    );
    return this.productUploadService.getProductUploadsByProductId(
      stagedProductId,
      userId,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product upload by ID' })
  @ApiParam({ name: 'id', description: 'Product upload ID' })
  @ApiResponse({
    status: 200,
    description: 'The product upload',
    type: ProductUploadResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Product upload not found' })
  async getProductUploadById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadResponseDto> {
    this.logger.log(
      `Getting product upload with ID: ${id} for user: ${userId}`,
    );
    return this.productUploadService.getProductUploadById(id, userId);
  }

  @Get(':id/status')
  @ApiOperation({ summary: 'Get the status of a product upload' })
  @ApiParam({ name: 'id', description: 'Product upload ID' })
  @ApiResponse({
    status: 200,
    description: 'The product upload status',
    type: ProductUploadStatusResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Product upload not found' })
  async getProductUploadStatus(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ProductUploadStatusResponseDto> {
    this.logger.log(
      `Getting status for product upload ID: ${id} for user: ${userId}`,
    );
    return this.productUploadService.getProductUploadStatus(id, userId);
  }
}
