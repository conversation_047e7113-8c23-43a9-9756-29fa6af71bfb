import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Patch,
  Param,
  Body,
  UseGuards,
  ParseIntPipe,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User } from '../../auth/entities/user.entity';
import { CrawlScheduleService } from '../services/crawl-schedule.service';
import {
  CreateCrawlScheduleDto,
  UpdateCrawlScheduleDto,
  UpdateScheduleStatusDto,
  UpdateScheduleProgressDto
} from '../dto/create-crawl-schedule.dto';
import { CrawlSchedule } from '../entities/crawl-schedule.entity';

@ApiTags('Crawl Schedules')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('crawl-schedules')
export class CrawlScheduleController {
  constructor(private readonly crawlScheduleService: CrawlScheduleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new crawl schedule' })
  @ApiResponse({
    status: 201,
    description: 'The crawl schedule has been successfully created.',
    type: CrawlSchedule,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async create(
    @Body() createCrawlScheduleDto: CreateCrawlScheduleDto,
    @CurrentUser() user: User,
  ): Promise<CrawlSchedule> {
    return this.crawlScheduleService.create(createCrawlScheduleDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all crawl schedules for the current user' })
  @ApiResponse({
    status: 200,
    description: 'List of crawl schedules.',
    type: [CrawlSchedule],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findAll(@CurrentUser() user: User): Promise<CrawlSchedule[]> {
    return this.crawlScheduleService.findAll(user.id);
  }

  @Get('incomplete')
  @ApiOperation({ summary: 'Get incomplete crawl schedules for automation' })
  @ApiResponse({
    status: 200,
    description: 'List of incomplete crawl schedules.',
    type: [CrawlSchedule],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getIncompleteSchedules(@CurrentUser() user: User): Promise<CrawlSchedule[]> {
    return this.crawlScheduleService.getReadySchedules(user.id);
  }

  @Get('next-available')
  @ApiOperation({ summary: 'Get and claim the next available schedule for automation' })
  @ApiResponse({
    status: 200,
    description: 'Next available schedule (claimed automatically).',
    type: CrawlSchedule,
  })
  @ApiResponse({ status: 404, description: 'No available schedules found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getNextAvailableSchedule(@CurrentUser() user: User): Promise<CrawlSchedule> {
    const schedule = await this.crawlScheduleService.getNextAvailableSchedule(user.id);
    if (!schedule) {
      throw new NotFoundException('No available schedules found');
    }
    return schedule;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific crawl schedule' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule details.',
    type: CrawlSchedule,
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<CrawlSchedule> {
    return this.crawlScheduleService.findOne(id, user.id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a crawl schedule' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule has been successfully updated.',
    type: CrawlSchedule,
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCrawlScheduleDto: UpdateCrawlScheduleDto,
    @CurrentUser() user: User,
  ): Promise<CrawlSchedule> {
    return this.crawlScheduleService.update(id, updateCrawlScheduleDto, user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a crawl schedule' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<{ message: string }> {
    await this.crawlScheduleService.remove(id, user.id);
    return { message: 'Crawl schedule deleted successfully' };
  }

  @Post(':id/toggle')
  @ApiOperation({ summary: 'Toggle active status of a crawl schedule' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule status has been toggled.',
    type: CrawlSchedule,
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async toggleActive(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<CrawlSchedule> {
    return this.crawlScheduleService.toggleActive(id, user.id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update crawl schedule status during execution' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule status has been updated.',
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() statusData: UpdateScheduleStatusDto,
    @CurrentUser() user: User,
  ): Promise<{ message: string }> {
    await this.crawlScheduleService.updateScheduleStatus(
      id,
      statusData.status,
      statusData.productCount || 0,
      statusData.error,
      user.id,
    );
    return { message: 'Schedule status updated successfully' };
  }

  @Patch(':id/progress')
  @ApiOperation({ summary: 'Update crawl schedule progress during execution' })
  @ApiResponse({
    status: 200,
    description: 'The crawl schedule progress has been updated.',
  })
  @ApiResponse({ status: 404, description: 'Crawl schedule not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async updateProgress(
    @Param('id', ParseIntPipe) id: number,
    @Body() progressData: UpdateScheduleProgressDto,
    @CurrentUser() user: User,
  ): Promise<{ message: string }> {
    await this.crawlScheduleService.updateScheduleProgress(
      id,
      progressData.progress,
      user.id,
    );
    return { message: 'Schedule progress updated successfully' };
  }
}
