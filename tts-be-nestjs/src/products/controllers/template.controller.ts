import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { TemplateService } from '../services/template.service';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';
import { Template } from '../entities/template.entity';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';
import { TemplateQueryDto } from '../dto/template-query.dto';

@ApiTags('templates')
@Controller('templates')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TemplateController {
  constructor(private readonly templateService: TemplateService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new template' })
  @ApiResponse({
    status: 201,
    description: 'The template has been successfully created.',
    type: Template,
  })
  create(
    @Body() createTemplateDto: CreateTemplateDto,
    @CurrentUser('id') userId: number,
  ): Promise<Template> {
    return this.templateService.create(createTemplateDto, userId);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all templates for the current user with pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Return paginated templates.',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Template' },
        },
        meta: {
          type: 'object',
          properties: {
            totalItems: {
              type: 'number',
              description: 'Total number of items across all pages',
            },
            itemCount: {
              type: 'number',
              description: 'Actual number of items returned in this response',
            },
            itemsPerPage: {
              type: 'number',
              description: 'Number of items per page',
            },
            totalPages: {
              type: 'number',
              description: 'Total number of pages',
            },
            currentPage: { type: 'number', description: 'Current page number' },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'sortField',
    required: false,
    enum: ['id', 'title', 'createdAt', 'updatedAt'],
    description: 'Field to sort by',
    example: 'updatedAt',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
    example: 'DESC',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter templates by name (case-insensitive partial matching)',
    example: 'Gildan',
  })
  async findAll(
    @CurrentUser('id') userId: number,
    @Query() templateQuery: TemplateQueryDto,
  ): Promise<PaginatedResult<Template>> {
    return this.templateService.findAll(userId, templateQuery);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a template by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the template.',
    type: Template,
  })
  @ApiResponse({ status: 404, description: 'Template not found.' })
  findOne(
    @Param('id') id: string,
    @CurrentUser('userId') userId: number,
  ): Promise<Template> {
    return this.templateService.findOne(+id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a template' })
  @ApiResponse({
    status: 200,
    description: 'The template has been successfully updated.',
    type: Template,
  })
  @ApiResponse({ status: 404, description: 'Template not found.' })
  update(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @CurrentUser('id') userId: number,
  ): Promise<Template> {
    return this.templateService.update(+id, updateTemplateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a template' })
  @ApiResponse({
    status: 200,
    description: 'The template has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Template not found.' })
  remove(
    @Param('id') id: string,
    @CurrentUser('userId') userId: number,
  ): Promise<void> {
    return this.templateService.remove(+id, userId);
  }
}
