import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User } from '../../auth/entities/user.entity';
import { CrawledProductService } from '../services/crawled-product.service';
import { CreateCrawledProductDto } from '../dto/create-crawled-product.dto';
import { CrawledProductQueryDto } from '../dto/crawled-product-query.dto';
import { UpdateCrawledProductDto, ManageCrawledProductUsersDto, BulkDeleteCrawledProductsDto, ShareSelectedProductsDto } from '../dto/update-crawled-product.dto';
import { CrawledProduct } from '../entities/crawled-product.entity';
import { PaginatedResult } from '../../common/interfaces/paginated-result.interface';

@ApiTags('Crawled Products')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('crawled-products')
export class CrawledProductController {
  constructor(private readonly crawledProductService: CrawledProductService) {}

  @Post()
  @ApiOperation({ summary: 'Store a crawled product' })
  @ApiResponse({
    status: 201,
    description: 'The crawled product has been successfully stored.',
    type: CrawledProduct,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async create(
    @Body() createCrawledProductDto: CreateCrawledProductDto,
    @CurrentUser() user: User,
  ): Promise<CrawledProduct> {
    return this.crawledProductService.create(createCrawledProductDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all crawled products for the current user' })
  @ApiResponse({
    status: 200,
    description: 'List of crawled products with pagination.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findAll(
    @Query() query: CrawledProductQueryDto,
    @CurrentUser() user: User,
  ): Promise<PaginatedResult<CrawledProduct>> {
    return this.crawledProductService.findAll(query, user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific crawled product' })
  @ApiResponse({
    status: 200,
    description: 'The crawled product details.',
    type: CrawledProduct,
  })
  @ApiResponse({ status: 404, description: 'Crawled product not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<CrawledProduct> {
    return this.crawledProductService.findOne(id, user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a crawled product' })
  @ApiResponse({
    status: 200,
    description: 'The crawled product has been successfully updated.',
    type: CrawledProduct,
  })
  @ApiResponse({ status: 404, description: 'Crawled product not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCrawledProductDto,
    @CurrentUser() user: User,
  ): Promise<CrawledProduct> {
    return this.crawledProductService.update(id, updateDto, user.id);
  }

  @Put(':id/users')
  @ApiOperation({ summary: 'Manage users for a crawled product' })
  @ApiResponse({
    status: 200,
    description: 'Users have been successfully managed for the crawled product.',
    type: CrawledProduct,
  })
  @ApiResponse({ status: 404, description: 'Crawled product not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async manageUsers(
    @Param('id', ParseIntPipe) id: number,
    @Body() manageUsersDto: ManageCrawledProductUsersDto,
    @CurrentUser() user: User,
  ): Promise<CrawledProduct> {
    return this.crawledProductService.manageUsers(id, manageUsersDto, user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a crawled product' })
  @ApiResponse({
    status: 200,
    description: 'The crawled product has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Crawled product not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<{ message: string }> {
    await this.crawledProductService.remove(id, user.id);
    return { message: 'Crawled product deleted successfully' };
  }

  @Patch(':id/toggle-public')
  @ApiOperation({ summary: 'Toggle public status of a crawled product' })
  @ApiResponse({
    status: 200,
    description: 'The crawled product public status has been successfully toggled.',
    type: CrawledProduct,
  })
  @ApiResponse({ status: 404, description: 'Crawled product not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async togglePublicStatus(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: User,
  ): Promise<CrawledProduct> {
    return this.crawledProductService.togglePublicStatus(id, user.id);
  }

  @Post('bulk-delete')
  @ApiOperation({ summary: 'Bulk delete crawled products' })
  @ApiResponse({
    status: 200,
    description: 'The crawled products have been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'No crawled products found or access denied.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteCrawledProductsDto,
    @CurrentUser() user: User,
  ): Promise<{ message: string; deletedCount: number }> {
    const result = await this.crawledProductService.bulkDelete(bulkDeleteDto, user.id);
    return {
      message: `${result.deletedCount} crawled products deleted successfully`,
      deletedCount: result.deletedCount
    };
  }

  @Post('share-selected')
  @ApiOperation({ summary: 'Share selected crawled products' })
  @ApiResponse({
    status: 200,
    description: 'The crawled products sharing status has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'No crawled products found or access denied.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async shareSelected(
    @Body() shareDto: ShareSelectedProductsDto,
    @CurrentUser() user: User,
  ): Promise<{ message: string; updatedCount: number; products: CrawledProduct[] }> {
    const result = await this.crawledProductService.shareSelected(shareDto, user.id);
    const action = shareDto.isPublic ? 'shared publicly' : 'made private';
    return {
      message: `${result.updatedCount} crawled products ${action} successfully`,
      updatedCount: result.updatedCount,
      products: result.products
    };
  }
}
