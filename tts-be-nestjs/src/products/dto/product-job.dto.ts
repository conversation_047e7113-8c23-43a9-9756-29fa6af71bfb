import { ApiProperty } from '@nestjs/swagger';

export enum ProductJobStatus {
  QUEUED = 'queued',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DELAYED = 'delayed',
  WAITING = 'waiting',
  PAUSED = 'paused',
  STUCK = 'stuck',
}

export class ProductJobDto {
  @ApiProperty({
    description: 'Job ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  jobId: string;

  @ApiProperty({
    description: 'Job status',
    enum: ProductJobStatus,
    example: ProductJobStatus.QUEUED,
  })
  status: ProductJobStatus;

  @ApiProperty({
    description: 'Job progress (0-100)',
    example: 50,
  })
  progress: number;

  @ApiProperty({
    description: 'Job data',
    example: { tiktokShopId: 1, productId: '123456789' },
  })
  data: any;

  @ApiProperty({
    description: 'Job result',
    example: { success: true, productId: '123456789' },
    required: false,
  })
  result?: any;

  @ApiProperty({
    description: 'Error message if job failed',
    example: 'Failed to create product: Invalid category ID',
    required: false,
  })
  error?: string;

  @ApiProperty({
    description: 'Job creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Job completion timestamp',
    example: '2023-01-01T00:05:00.000Z',
    required: false,
  })
  completedAt?: string;
}
