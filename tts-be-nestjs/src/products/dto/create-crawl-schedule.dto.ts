import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsIn, IsNumber, IsBoolean, IsOptional, Min, Max, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCrawlScheduleDto {
  @ApiProperty({
    description: 'Name/description for this crawl schedule',
    example: 'Vintage T-Shirts Daily Crawl',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Keywords to search for (comma-separated)',
    example: 'vintage t-shirt, retro clothing, cotton tee',
  })
  @IsString()
  keywords: string;

  @ApiProperty({
    description: 'Target marketplace for crawling',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
  })
  @IsIn(['etsy', 'ebay', 'amazon'])
  marketplace: string;

  @ApiProperty({
    description: 'Crawl frequency in minutes (minimum 60 minutes)',
    example: 1440,
    minimum: 60,
    maximum: 10080, // 1 week
  })
  @IsNumber()
  @Min(60)
  @Max(10080)
  frequencyMinutes: number;

  @ApiProperty({
    description: 'Maximum number of products to crawl per run',
    example: 50,
    minimum: 1,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  maxProductsPerRun?: number;

  @ApiProperty({
    description: 'Whether this schedule should be active immediately',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateCrawlScheduleDto {
  @ApiProperty({
    description: 'Name/description for this crawl schedule',
    example: 'Vintage T-Shirts Daily Crawl',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Keywords to search for (comma-separated)',
    example: 'vintage t-shirt, retro clothing, cotton tee',
    required: false,
  })
  @IsOptional()
  @IsString()
  keywords?: string;

  @ApiProperty({
    description: 'Target marketplace for crawling',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
    required: false,
  })
  @IsOptional()
  @IsIn(['etsy', 'ebay', 'amazon'])
  marketplace?: string;

  @ApiProperty({
    description: 'Crawl frequency in minutes (minimum 60 minutes)',
    example: 1440,
    minimum: 60,
    maximum: 10080,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(10080)
  frequencyMinutes?: number;

  @ApiProperty({
    description: 'Maximum number of products to crawl per run',
    example: 50,
    minimum: 1,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  maxProductsPerRun?: number;

  @ApiProperty({
    description: 'Whether this schedule is active',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateScheduleStatusDto {
  @ApiProperty({
    description: 'Status of the schedule execution',
    example: 'running',
    enum: ['pending', 'running', 'completed', 'failed'],
  })
  @IsIn(['pending', 'running', 'completed', 'failed'])
  status: string;

  @ApiProperty({
    description: 'Number of products processed',
    example: 25,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  productCount?: number;

  @ApiProperty({
    description: 'Error message if execution failed',
    example: 'Rate limit exceeded',
    required: false,
  })
  @IsOptional()
  @IsString()
  error?: string;
}

export class ProgressDto {
  @ApiProperty({
    description: 'Current progress count',
    example: 15,
  })
  @IsNumber()
  @Min(0)
  current: number;

  @ApiProperty({
    description: 'Total expected count',
    example: 50,
  })
  @IsNumber()
  @Min(0)
  total: number;
}

export class UpdateScheduleProgressDto {
  @ApiProperty({
    description: 'Progress information',
    type: ProgressDto,
  })
  @ValidateNested()
  @Type(() => ProgressDto)
  progress: ProgressDto;
}
