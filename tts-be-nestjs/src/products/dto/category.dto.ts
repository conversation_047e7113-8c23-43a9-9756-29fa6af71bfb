import { ApiProperty } from '@nestjs/swagger';

export class CategoryDto {
  @ApiProperty({
    description: 'Internal category ID',
    example: 1,
    required: false,
  })
  id?: number;

  @ApiProperty({
    description: 'TikTok Shop category ID',
    example: '853000',
  })
  idTT: string;

  @ApiProperty({
    description: 'Parent TikTok Shop category ID',
    example: '851848',
    required: false,
  })
  parentIdTT?: string;

  @ApiProperty({
    description: 'Category name',
    example: "Women's Clothing",
  })
  localName: string;

  @ApiProperty({
    description: 'Whether this is a leaf category',
    example: true,
  })
  isLeaf: boolean;

  @ApiProperty({
    description: 'Category version',
    example: 'v2',
    required: false,
  })
  version?: string;

  @ApiProperty({
    description: 'Permission statuses for this category',
    example: ['AVAILABLE', 'INVITE_ONLY'],
    required: false,
  })
  permissionStatuses?: string[];

  @ApiProperty({
    description: 'Last updated date',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  updatedAt?: Date;
}

export class CategoryResponseDto {
  @ApiProperty({
    description: 'List of categories',
    type: [CategoryDto],
  })
  categories: CategoryDto[];
}

export class CategoryQueryDto {
  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Parent category ID (optional)',
    example: '851848',
    required: false,
  })
  parentId?: string;

  @ApiProperty({
    description: 'Category version',
    example: 'v2',
    required: false,
    default: 'v2',
  })
  version?: string;
}
