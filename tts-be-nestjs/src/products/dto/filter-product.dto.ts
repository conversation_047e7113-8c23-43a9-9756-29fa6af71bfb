import { IsOptional, IsString, <PERSON><PERSON><PERSON>ber, Min, <PERSON>Enum, IsInt } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ProductStatus } from '../entities/product.entity';

export class FilterProductDto {
  @ApiPropertyOptional({ description: 'Search term for product title', example: 'Shirt' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Product name (legacy field)', example: 'Shirt' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Product status', enum: ProductStatus })
  @IsOptional()
  @IsEnum(ProductStatus)
  status?: ProductStatus;

  @ApiPropertyOptional({ description: 'TikTok Shop ID', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  tiktokShopId?: number;

  @ApiPropertyOptional({ description: 'Category ID', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  categoryId?: number;

  @ApiPropertyOptional({ description: 'Brand ID', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  brandId?: number;

  @ApiPropertyOptional({ description: 'Min Price', example: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minPrice?: number;

  @ApiPropertyOptional({ description: 'Max Price', example: 100 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maxPrice?: number;

  @ApiPropertyOptional({ description: 'Create time from (Unix timestamp)', example: 1640995200 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  createTimeFrom?: number;

  @ApiPropertyOptional({ description: 'Create time to (Unix timestamp)', example: 1672531199 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  createTimeTo?: number;
}
