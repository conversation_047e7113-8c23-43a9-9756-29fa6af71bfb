import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class InventoryUpdateItemDto {
  @ApiProperty({
    description: 'Quantity to set',
    example: 100,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Warehouse ID',
    example: '7000714532876273420',
    required: false,
  })
  @IsString()
  @IsOptional()
  warehouseId?: string;
}

export class UpdateSkuInventoryDto {
  @ApiProperty({
    description: 'Inventory updates',
    type: [InventoryUpdateItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InventoryUpdateItemDto)
  inventory: InventoryUpdateItemDto[];
}

export class UpdateSkuInventoryResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message',
    example: 'Inventory updated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Updated inventory',
    type: [InventoryUpdateItemDto],
  })
  inventory: InventoryUpdateItemDto[];
}
