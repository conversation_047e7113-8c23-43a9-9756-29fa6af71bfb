import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator';

export class R2UploadRequestDto {
  @ApiProperty({
    description: 'File name',
    example: 'image.jpg',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    description: 'Content type',
    example: 'image/jpeg',
  })
  @IsString()
  @IsNotEmpty()
  contentType: string;

  @ApiProperty({
    description: 'Folder path (optional)',
    example: 'product-images',
    required: false,
  })
  @IsString()
  @IsOptional()
  folder?: string;
}

export class R2PresignedUrlResponseDto {
  @ApiProperty({
    description: 'Object key in R2 storage',
    example:
      'product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Presigned URL for uploading',
    example:
      'https://example.r2.cloudflarestorage.com/bucket/key?X-Amz-Algorithm=...',
  })
  presignedUrl: string;

  @ApiProperty({
    description: 'Public URL for accessing the file after upload',
    example:
      'https://pub-123456.r2.dev/product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  publicUrl: string;

  @ApiProperty({
    description: 'Expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;
}

export class R2UploadResponseDto {
  @ApiProperty({
    description: 'Object key in R2 storage',
    example:
      'product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Public URL for accessing the file',
    example:
      'https://pub-123456.r2.dev/product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'image.jpg',
  })
  filename: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
  })
  size: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
  })
  mimetype: string;
}
