import { ApiProperty } from '@nestjs/swagger';

export class BrandDto {
  @ApiProperty({
    description: 'Internal brand ID',
    example: 1,
    required: false,
  })
  id?: number;

  @ApiProperty({
    description: 'TikTok Shop brand ID',
    example: '7082427311584347905',
  })
  idTT: string;

  @ApiProperty({
    description: 'Brand name',
    example: 'Nike',
  })
  name: string;

  @ApiProperty({
    description: 'Brand authorization status',
    example: 'AUTHORIZED',
    required: false,
  })
  authorizedStatus?: string;

  @ApiProperty({
    description: 'Brand status in the requested category',
    example: 'AVAILABLE',
    required: false,
  })
  brandStatus?: string;

  @ApiProperty({
    description: 'Whether this is a T1 brand (internationally renowned)',
    example: true,
    required: false,
  })
  isT1Brand?: boolean;

  @ApiProperty({
    description: 'Brand image URL',
    example: 'https://example.com/brand.jpg',
    required: false,
  })
  imageUrl?: string;

  @ApiProperty({
    description: 'Brand authorization letter URL',
    example: 'https://example.com/authorization.pdf',
    required: false,
  })
  authorizationLetterUrl?: string;

  @ApiProperty({
    description: 'Brand authorization start date',
    example: '2023-01-01',
    required: false,
  })
  authorizationStartDate?: string;

  @ApiProperty({
    description: 'Brand authorization end date',
    example: '2023-12-31',
    required: false,
  })
  authorizationEndDate?: string;

  @ApiProperty({
    description: 'Last updated date',
    example: '2023-05-15T10:30:00.000Z',
    required: false,
  })
  updatedAt?: Date;
}

export class BrandResponseDto {
  @ApiProperty({
    description: 'List of brands',
    type: [BrandDto],
  })
  brands: BrandDto[];
}

export class BrandQueryDto {
  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
    required: false,
  })
  categoryId?: string;
}
