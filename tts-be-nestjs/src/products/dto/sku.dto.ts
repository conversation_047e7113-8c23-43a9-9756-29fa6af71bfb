import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class SkuPriceDto {
  @ApiProperty({
    description: 'Sale price',
    example: 19.99,
  })
  @IsNumber()
  salePrice: number;

  @ApiProperty({
    description: 'Tax exclusive price',
    example: 18.99,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  taxExclusivePrice?: number;

  @ApiProperty({
    description: 'Currency',
    example: 'USD',
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Unit price',
    example: 1,
  })
  @IsNumber()
  unitPrice: number;
}

export class SkuInventoryItemDto {
  @ApiProperty({
    description: 'Quantity',
    example: 100,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Warehouse ID',
    example: '7000714532876273420',
    required: false,
  })
  @IsString()
  @IsOptional()
  warehouseId?: string;
}

export class SkuImageDto {
  @ApiProperty({
    description: 'Image URL',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'R2 storage key',
    example: 'products/123/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  r2Key?: string;

  @ApiProperty({
    description: 'URI',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  uri?: string;
}

export class SkuSalesAttributeDto {
  @ApiProperty({
    description: 'Attribute ID',
    example: '7082427311584347905',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Attribute value ID',
    example: '7082427311584347906',
  })
  @IsString()
  valueId: string;

  @ApiProperty({
    description: 'Attribute name',
    example: 'Color',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Attribute value name',
    example: 'Red',
    required: false,
  })
  @IsString()
  @IsOptional()
  valueName?: string;

  @ApiProperty({
    description: 'SKU image for this attribute',
    type: SkuImageDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SkuImageDto)
  skuImg?: SkuImageDto;

  @ApiProperty({
    description: 'Supplementary SKU images',
    type: [SkuImageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SkuImageDto)
  supplementarySkuImages?: SkuImageDto[];
}

export class SkuDto {
  @ApiProperty({
    description: 'SKU ID',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'TikTok Shop SKU ID',
    example: '7082427311584347905',
    required: false,
  })
  @IsString()
  @IsOptional()
  idTT?: string;

  @ApiProperty({
    description: 'Seller SKU',
    example: 'SKU-123',
  })
  @IsString()
  sellerSku: string;

  @ApiProperty({
    description: 'Product ID',
    example: 1,
  })
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Price information',
    type: SkuPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => SkuPriceDto)
  price: SkuPriceDto;

  @ApiProperty({
    description: 'Inventory information',
    type: [SkuInventoryItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SkuInventoryItemDto)
  inventory: SkuInventoryItemDto[];

  @ApiProperty({
    description: 'Sales attributes',
    type: [SkuSalesAttributeDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SkuSalesAttributeDto)
  @IsOptional()
  salesAttributes?: SkuSalesAttributeDto[];

  @ApiProperty({
    description: 'Created date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class SkuResponseDto {
  @ApiProperty({
    description: 'List of SKUs',
    type: [SkuDto],
  })
  skus: SkuDto[];

  @ApiProperty({
    description: 'Total number of SKUs',
    example: 10,
  })
  total: number;
}
