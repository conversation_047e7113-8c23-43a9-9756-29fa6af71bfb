import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsArray, IsNumber, IsNotEmpty } from 'class-validator';

export class UpdateCrawledProductDto {
  @ApiProperty({
    description: 'Whether this product can be shared with other users',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class ManageCrawledProductUsersDto {
  @ApiProperty({
    description: 'Array of user IDs to add to this crawled product',
    example: [1, 2, 3],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  addUserIds?: number[];

  @ApiProperty({
    description: 'Array of user IDs to remove from this crawled product',
    example: [4, 5],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  removeUserIds?: number[];
}

export class BulkDeleteCrawledProductsDto {
  @ApiProperty({
    description: 'Array of crawled product IDs to delete',
    example: [1, 2, 3, 4, 5],
    type: [Number],
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];
}

export class ShareSelectedProductsDto {
  @ApiProperty({
    description: 'Array of crawled product IDs to share',
    example: [1, 2, 3, 4, 5],
    type: [Number],
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];

  @ApiProperty({
    description: 'Whether to make the products public (true) or private (false)',
    example: true,
  })
  @IsBoolean()
  isPublic: boolean;
}
