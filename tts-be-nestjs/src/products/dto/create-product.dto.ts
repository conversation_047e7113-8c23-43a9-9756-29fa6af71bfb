import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
  IsEnum,
  IsArray,
  IsPositive,
  IsObject,
  IsOptional,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProductStatus } from '../entities/product.entity';
import { Type } from 'class-transformer';

export class CreateSkuDto {
  @ApiProperty({ description: 'SKU ID of Tiktok Shop', example: '1234567890' })
  @IsString()
  idTT: string;

  @ApiProperty({
    description: 'An internal code/name for managing SKUs',
    example: 'GILDAN-5000-BLK-S',
  })
  @IsString()
  @MaxLength(100)
  sellerSku: string;

  @ApiProperty({
    description: 'SKU list prices from external platforms',
    example: [
      {
        amount: '29.99',
        currency: 'USD',
        source: 'SHOPIFY_COMPARE_AT_PRICE',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  externalListPrices?: {
    amount: number;
    currency: string;
    source: string;
  }[];

  @ApiProperty({
    description: 'SKU inventory information',
    example: [
      {
        quantity: 100,
        warehouseId: '123456',
      },
    ],
  })
  @IsArray()
  inventory: {
    quantity: number;
    warehouseId: string;
  }[];

  @ApiProperty({
    description: 'SKU price information',
    example: {
      salePrice: 24.99,
      taxExclusivePrice: 26.99,
      currency: 'USD',
      unitPrice: 1,
    },
  })
  @IsObject()
  price: {
    salePrice: number | null;
    taxExclusivePrice: number | null;
    currency: string;
    unitPrice?: number | null;
  };

  @ApiProperty({
    description: 'SKU list price information',
    example: {
      amount: 29.99,
      currency: 'USD',
    },
  })
  @IsOptional()
  @IsObject()
  listPrice?: {
    amount: number | null;
    currency: string;
  } | null;

  @ApiPropertyOptional({
    description: 'Sales attributes',
    example: [
      {
        id: '100000',
        name: 'Color',
        valueId: '100000',
        valueName: 'Red',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  salesAttributes?: {
    id: string;
    name: string;
    valueId: string;
    valueName: string;
    skuImg?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    supplementarySkuImages?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
  }[];

  @ApiPropertyOptional({
    description: 'External SKU ID',
    example: '1729592969712207234',
  })
  @IsOptional()
  @IsString()
  externalSkuId?: string;

  @ApiPropertyOptional({
    description: 'Combined SKUs',
    example: [
      {
        productId: '1729582718312380123',
        skuId: '1729582718312380123',
        skuCount: 1,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  combinedSkus?: {
    productId: string;
    skuId: string;
    skuCount: number;
  }[];

  @ApiPropertyOptional({
    description: 'Global listing policy',
    example: {
      priceSync: true,
      inventoryType: 'SHARED',
      replicateSource: {
        productId: '1729592969712203232',
        shopId: '7295929697122032321',
        skuId: '1729592969712203232',
      },
    },
  })
  @IsOptional()
  @IsObject()
  globalListingPolicy?: {
    priceSync?: boolean;
    inventoryType?: string;
    replicateSource?: {
      productId: string;
      shopId: string;
      skuId: string;
    };
  };

  @ApiPropertyOptional({
    description: 'SKU unit count',
    example: '1.00',
  })
  @IsOptional()
  @IsString()
  skuUnitCount?: string;

  @ApiPropertyOptional({
    description: 'External URLs',
    example: ['https://example.com/path1', 'https://example.com/path2'],
  })
  @IsOptional()
  @IsArray()
  externalUrls?: string[];

  @ApiPropertyOptional({
    description: 'Pre-sale information',
    example: {
      type: 'PRE_ORDER',
      fulfillmentType: {
        handlingDurationDays: 3,
      },
    },
  })
  @IsOptional()
  @IsObject()
  preSale?: {
    type: string;
    fulfillmentType?: {
      handlingDurationDays: number;
    };
  };

  @ApiPropertyOptional({
    description: 'Identifier code',
    example: {
      code: '10000000000010',
      type: 'GTIN',
    },
  })
  @IsOptional()
  @IsObject()
  identifierCode?: {
    code: string;
    type: string;
  };

  @ApiPropertyOptional({
    description: 'Extra identifier codes',
    example: ['00012345678905', '9780596520687'],
  })
  @IsOptional()
  @IsArray()
  extraIdentifierCodes?: string[];
}

export class CreateProductDto {
  @ApiProperty({
    description: 'Product ID of TikTok Shop',
    example: '1234567890',
  })
  @IsString()
  idTT: string;

  @ApiProperty({
    description: 'Category',
    example: 'Women Tops/Hoodies & Sweatshirts/Women Pullover Sweatshirts',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;

  @ApiPropertyOptional({
    description: 'Category chains from TikTok Shop',
    example: [
      {
        id: '853000',
        parentId: '851848',
        localName: 'Botol & Stoples Penyimpanan',
        isLeaf: true,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  categoryChains?: {
    id: string;
    parentId: string;
    localName: string;
    isLeaf: boolean;
  }[];

  @ApiProperty({ description: 'Brand', example: 'Gildan' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  brandInfo?: string;

  @ApiPropertyOptional({
    description: 'Brand information from TikTok Shop',
    example: { id: '7082427311584347905', name: 'brand xxx aaa' },
  })
  @IsOptional()
  @IsObject()
  brand?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Product title',
    example: 'T-Shirt Gildan 5000 1 side',
  })
  @IsString()
  @MaxLength(300)
  title: string;

  @ApiPropertyOptional({
    description: 'Product description',
    example:
      'Gildan 5000 - Classic Heavy Cotton T-Shirt - Durability Meets Comfort!',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Size chart information from TikTok Shop',
    example: {
      image: {
        urls: ['https://example.com/size-chart.png'],
      },
      template: { id: '7267563252536723205' },
    },
  })
  @IsOptional()
  @IsObject()
  sizeChart?: {
    image?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    template?: {
      id: string;
    };
  };

  @ApiPropertyOptional({
    description: 'Product Image List',
    example: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  productImages?: string[];

  @ApiPropertyOptional({
    description: 'Main images information from TikTok Shop',
    example: [
      {
        height: 600,
        width: 600,
        thumbUrls: ['https://example.com/thumb.jpg'],
        uri: 'tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4',
        urls: ['https://example.com/image.jpg'],
      },
    ],
  })
  @IsOptional()
  @IsArray()
  mainImages?: {
    height?: number;
    width?: number;
    thumbUrls?: string[];
    uri?: string;
    urls?: string[];
  }[];

  @ApiPropertyOptional({
    description: 'Video information from TikTok Shop',
    example: {
      id: 'v09ea0g40000cj91373c77u3mid3g1s0',
      coverUrl: 'https://example.com/cover.jpg',
      format: 'MP4',
      url: 'https://example.com/video.mp4',
      width: 1280,
      height: 480,
      size: 1000,
    },
  })
  @IsOptional()
  @IsObject()
  videoInfo?: {
    id?: string;
    coverUrl?: string;
    format?: string;
    url?: string;
    width?: number;
    height?: number;
    size?: number;
  };

  @ApiPropertyOptional({ example: 12, description: 'Package Length in inch' })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  packageLength?: number;

  @ApiPropertyOptional({ example: 10, description: 'Package Width in inch' })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  packageWidth?: number;

  @ApiPropertyOptional({ example: 1.5, description: 'Package Height in inch' })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  packageHeight?: number;

  @ApiPropertyOptional({
    description: 'Package dimensions information from TikTok Shop',
    example: {
      length: '10',
      width: '10',
      height: '10',
      unit: 'CENTIMETER',
    },
  })
  @IsOptional()
  @IsObject()
  packageDimensions?: {
    length?: string;
    width?: string;
    height?: string;
    unit?: string;
  };

  @ApiPropertyOptional({
    description: 'Package weight information from TikTok Shop',
    example: {
      value: '1.32',
      unit: 'KILOGRAM',
    },
  })
  @IsOptional()
  @IsObject()
  packageWeight?: {
    value?: string;
    unit?: string;
  };

  @ApiProperty({
    description: 'Product status in TikTok Shop',
    example: ProductStatus.DRAFT,
    enum: ProductStatus,
  })
  @IsString()
  @IsEnum(ProductStatus)
  status: ProductStatus;

  @ApiProperty({
    description: 'Tiktok Shop ID from internal system',
    example: 1,
  })
  @IsNumber()
  tiktokShopId: number;

  @ApiPropertyOptional({
    description: 'User ID who owns this product',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiPropertyOptional({
    description: 'Product sync fail reasons',
    example: ['The required qualification is missed.'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  productSyncFailReasons?: string[];

  @ApiPropertyOptional({
    description: 'Recommended categories',
    example: [{ id: '853000', localName: 'Botol & Stoples Penyimpanan' }],
  })
  @IsOptional()
  @IsArray()
  recommendedCategories?: {
    id: string;
    localName: string;
  }[];

  @ApiPropertyOptional({
    description: 'Is not for sale flag',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isNotForSale?: boolean;

  @ApiPropertyOptional({
    description: 'Integrated platform statuses',
    example: [{ platform: 'TOKOPEDIA', status: 'PLATFORM_DEACTIVATED' }],
  })
  @IsOptional()
  @IsArray()
  integratedPlatformStatuses?: {
    platform: string;
    status: string;
  }[];

  @ApiPropertyOptional({
    description: 'Sales regions',
    example: ['US', 'GB'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  salesRegions?: string[];

  @ApiPropertyOptional({
    description: 'Audit status',
    example: {
      status: 'AUDITING',
      preApprovedReasons: ['Reason 1', 'Reason 2'],
    },
  })
  @IsOptional()
  @IsObject()
  audit?: {
    status: string;
    preApprovedReasons?: string[];
  };

  @ApiPropertyOptional({
    description: 'Audit failed reasons',
    example: [
      {
        position: 'PRODUCT_TITLE',
        reasons: ['Title is too short'],
        suggestions: ['Make the title longer'],
        listingPlatform: 'TIKTOK_SHOP',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  auditFailedReasons?: {
    position?: string;
    reasons?: string[];
    suggestions?: string[];
    listingPlatform?: string;
  }[];

  @ApiPropertyOptional({ description: 'Is COD allowed', example: true })
  @IsOptional()
  @IsBoolean()
  isCodAllowed?: boolean;

  @ApiPropertyOptional({
    description: 'Product attributes',
    example: [
      {
        id: '100000',
        name: 'Color',
        values: [
          {
            id: '100001',
            name: 'Red',
          },
        ],
      },
    ],
  })
  @IsOptional()
  @IsArray()
  productAttributes?: {
    id: string;
    name: string;
    values: {
      id: string;
      name: string;
    }[];
  }[];

  @ApiPropertyOptional({
    description: 'Delivery options',
    example: [
      {
        id: 'STANDARD',
        name: 'Standard Delivery',
        isAvailable: true,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  deliveryOptions?: {
    id: string;
    name: string;
    isAvailable: boolean;
  }[];

  @ApiPropertyOptional({
    description: 'External product ID',
    example: 'EXT-12345',
  })
  @IsOptional()
  @IsString()
  externalProductId?: string;

  @ApiPropertyOptional({
    description: 'Product types',
    example: ['NORMAL'],
  })
  @IsOptional()
  @IsArray()
  productTypes?: string[];

  @ApiPropertyOptional({
    description: 'Manufacturer IDs',
    example: ['MFG-12345'],
  })
  @IsOptional()
  @IsArray()
  manufacturerIds?: string[];

  @ApiPropertyOptional({
    description: 'Responsible person IDs',
    example: ['RP-12345'],
  })
  @IsOptional()
  @IsArray()
  responsiblePersonIds?: string[];

  @ApiPropertyOptional({
    description: 'Shipping insurance requirement',
    example: 'REQUIRED',
  })
  @IsOptional()
  @IsString()
  shippingInsuranceRequirement?: string;

  @ApiPropertyOptional({
    description: 'Minimum order quantity',
    example: 2,
  })
  @IsOptional()
  @IsNumber()
  minimumOrderQuantity?: number;

  @ApiPropertyOptional({
    description: 'Is pre-owned',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isPreOwned?: boolean;

  @ApiPropertyOptional({
    description: 'Certifications',
    example: [
      {
        id: 'CERT-12345',
        title: 'ISO 9001',
        files: [
          {
            id: 'FILE-12345',
            urls: ['https://example.com/cert.pdf'],
            name: 'ISO Certificate',
            format: 'PDF',
          },
        ],
      },
    ],
  })
  @IsOptional()
  @IsArray()
  certifications?: {
    id: string;
    title: string;
    files?: {
      id: string;
      urls: string[];
      name: string;
      format: string;
    }[];
    images?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
    expirationDate?: number;
  }[];

  @ApiPropertyOptional({
    description: 'Global product association',
    example: {
      globalProductId: 'GP-12345',
      skuMappings: [
        {
          globalSkuId: 'GS-12345',
          localSkuId: 'LS-12345',
        },
      ],
    },
  })
  @IsOptional()
  @IsObject()
  globalProductAssociation?: {
    globalProductId: string;
    skuMappings: {
      globalSkuId: string;
      localSkuId: string;
    }[];
  };

  @ApiPropertyOptional({
    description: 'Listing quality tier',
    example: 'POOR',
  })
  @IsOptional()
  @IsString()
  listingQualityTier?: string;

  @ApiProperty({ type: [CreateSkuDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSkuDto)
  skus: CreateSkuDto[];

  @ApiPropertyOptional({
    description: 'Create time from TikTok Shop',
    example: 1745013035,
  })
  @IsOptional()
  @IsNumber()
  createTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Update time from TikTok Shop',
    example: 1745013084,
  })
  @IsOptional()
  @IsNumber()
  updateTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Complete raw TikTok response for debugging and future extensibility',
    example: { id: '1234567890', title: 'Product Title', status: 'ACTIVATE' },
  })
  @IsOptional()
  rawTikTokResponse?: any;
}
