import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ImageUseCase } from './upload-image.dto';
import { ProductJobStatus } from './product-job.dto';

export class CreateTikTokProductImageDto {
  @ApiProperty({
    description: 'Image URI from TikTok Shop (if already uploaded)',
    example: 'tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5',
    required: false,
  })
  @IsString()
  @IsOptional()
  uri?: string;

  @ApiProperty({
    description: 'Base64 encoded image data (if not already uploaded)',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageData?: string;

  @ApiProperty({
    description: 'URL of the image to upload (if not already uploaded)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'The use case of the image',
    enum: ImageUseCase,
    example: ImageUseCase.MAIN_IMAGE,
  })
  @IsEnum(ImageUseCase)
  useCase: ImageUseCase;
}

export class CreateTikTokProductSkuAttributeDto {
  @ApiProperty({
    description: 'Attribute id',
    example: '7082427311584347905',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Attribute value id',
    example: '7082427311584347906',
  })
  @IsString()
  @IsNotEmpty()
  valueId: string;

  @ApiPropertyOptional({
    description: 'Attribute name',
    example: '7082427311584347905',
  })
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Attribute value name',
    example: '7082427311584347906',
  })
  @IsString()
  valueName?: string;

  @ApiPropertyOptional({
    description: 'Main uri image of sku',
    example: '7082427311584347906',
  })
  @IsString()
  skuImage?: string;
}

export class CreateTikTokProductSkuDto {
  @ApiProperty({
    description: 'SKU external ID',
    example: 'SKU-123',
  })
  @IsString()
  @IsNotEmpty()
  externalSkuId: string;

  @ApiProperty({
    description: 'SKU sales attributes',
    type: [CreateTikTokProductSkuAttributeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTikTokProductSkuAttributeDto)
  salesAttributes: CreateTikTokProductSkuAttributeDto[];

  @ApiProperty({
    description: 'SKU price in cents',
    example: 1999,
  })
  @IsNumber()
  price: number;

  @ApiProperty({
    description: 'SKU stock quantity',
    example: 100,
  })
  @IsNumber()
  stock: number;

  @ApiProperty({
    description: 'Warehouse ID for inventory',
    example: '7000714532876273420',
    required: false,
  })
  @IsString()
  @IsOptional()
  warehouseId?: string;
}

export class CreateTikTokProductAttributeValueDto {
  @ApiProperty({
    description: 'Attribute value ID',
    example: '7082427311584347906',
    required: false,
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Attribute value name (custom value if ID not provided)',
    example: 'Cotton',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class CreateTikTokProductAttributeDto {
  @ApiProperty({
    description: 'Attribute ID',
    example: '7082427311584347905',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Attribute values',
    type: [CreateTikTokProductAttributeValueDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTikTokProductAttributeValueDto)
  values: CreateTikTokProductAttributeValueDto[];
}

export class CreateTikTokProductDto {
  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  @IsNumber()
  tiktokShopId: number;

  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
  })
  @IsString()
  @IsNotEmpty()
  categoryId: string;

  @ApiProperty({
    description: 'Brand ID',
    example: '7082427311584347905',
  })
  @IsString()
  @IsNotEmpty()
  brandId: string;

  @ApiProperty({
    description: 'Product images',
    type: [CreateTikTokProductImageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTikTokProductImageDto)
  images: CreateTikTokProductImageDto[];

  @ApiProperty({
    description: 'Product attributes',
    type: [CreateTikTokProductAttributeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTikTokProductAttributeDto)
  attributes: CreateTikTokProductAttributeDto[];

  @ApiProperty({
    description: 'Product SKUs',
    type: [CreateTikTokProductSkuDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTikTokProductSkuDto)
  skus: CreateTikTokProductSkuDto[];

  @ApiProperty({
    description: 'Package dimensions',
    example: {
      length: '20',
      width: '15',
      height: '5',
      unit: 'CENTIMETER',
    },
  })
  @ValidateNested()
  @Type(() => Object)
  packageDimensions: {
    length: string;
    width: string;
    height: string;
    unit: string;
  };

  @ApiProperty({
    description: 'Package weight',
    example: {
      value: '500',
      unit: 'GRAM',
    },
  })
  @ValidateNested()
  @Type(() => Object)
  packageWeight: {
    value: string;
    unit: string;
  };

  @ApiProperty({
    description: 'Size chart image URI (if already uploaded)',
    example: 'tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5',
    required: false,
  })
  @IsString()
  @IsOptional()
  sizeChartId?: string;

  @ApiProperty({
    description:
      'Base64 encoded size chart image data (if not already uploaded)',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
    required: false,
  })
  @IsString()
  @IsOptional()
  sizeChartImageData?: string;

  @ApiProperty({
    description:
      'URL of the size chart image to upload (if not already uploaded)',
    example: 'https://example.com/size-chart.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  sizeChartImageUrl?: string;
}

export class CreateTikTokProductResponseDto {
  @ApiProperty({
    description: 'Job ID for tracking the product creation process',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  jobId: string;

  @ApiProperty({
    description: 'Status of the job',
    enum: ProductJobStatus,
    example: ProductJobStatus.QUEUED,
  })
  status: ProductJobStatus;
}
