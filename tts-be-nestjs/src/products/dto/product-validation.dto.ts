import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for validation error or warning
 */
export class ValidationErrorDto {
  @ApiProperty({
    description: 'Field name that has the error',
    example: 'title',
  })
  field: string;

  @ApiProperty({
    description: 'Error message',
    example: 'Title is required',
  })
  message: string;

  @ApiProperty({
    description: 'Error code from TikTok API',
    example: 'MISSING_FIELD',
  })
  code: string;

  @ApiProperty({
    description: 'Error level (ERROR or WARNING)',
    example: 'ERROR',
    enum: ['ERROR', 'WARNING'],
  })
  level: 'ERROR' | 'WARNING';
}

/**
 * DTO for product validation result
 */
export class ProductValidationResultDto {
  @ApiProperty({
    description: 'Whether the product validation passed',
    example: false,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'List of validation errors',
    type: [ValidationErrorDto],
  })
  errors: ValidationErrorDto[];

  @ApiProperty({
    description: 'List of validation warnings',
    type: [ValidationErrorDto],
  })
  warnings: ValidationErrorDto[];

  @ApiProperty({
    description: 'Raw response from TikTok API',
    required: false,
  })
  rawResponse?: any;

  @ApiProperty({
    description: 'Message about the validation process',
    example: 'Validation started for product upload ID: 123',
    required: false,
  })
  message?: string;
}

/**
 * DTO for product validation request
 */
export class ValidateProductDto {
  @ApiProperty({
    description: 'Staged product ID',
    example: 1,
  })
  stagedProductId: number;

  @ApiProperty({
    description: 'TikTok shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Whether to force validation even if images are not processed',
    example: false,
    required: false,
  })
  forceValidation?: boolean;
}

/**
 * DTO for product upload options
 */
export class ProductUploadOptionsDto {
  @ApiProperty({
    description: 'Whether to skip validation',
    example: false,
    required: false,
  })
  skipValidation?: boolean;

  @ApiProperty({
    description:
      'Whether to force upload despite validation warnings or errors',
    example: false,
    required: false,
  })
  forceUpload?: boolean;
}
