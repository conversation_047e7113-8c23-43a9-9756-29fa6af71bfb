import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, ValidateIf } from 'class-validator';

export enum ImageUseCase {
  MAIN_IMAGE = 'MAIN_IMAGE',
  ATTRIBUTE_IMAGE = 'ATTRIBUTE_IMAGE',
  DESCRIPTION_IMAGE = 'DESCRIPTION_IMAGE',
  CERTIFICATION_IMAGE = 'CERTIFICATION_IMAGE',
  SIZE_CHART_IMAGE = 'SIZE_CHART_IMAGE',
}

export class UploadImageDto {
  @ApiProperty({
    description:
      'Base64 encoded image data (required if imageUrl, tempFilePath, or r2Key is not provided)',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
    required: false,
  })
  @ValidateIf((o) => !o.imageUrl && !o.tempFilePath && !o.r2Key)
  @IsString()
  imageData?: string;

  @ApiProperty({
    description:
      'URL of the image to upload (required if imageData, tempFilePath, or r2Key is not provided)',
    example: 'https://example.com/images/product.jpg',
    required: false,
  })
  @ValidateIf((o) => !o.imageData && !o.tempFilePath && !o.r2Key)
  @IsString()
  imageUrl?: string;

  @ApiProperty({
    description:
      'Path to temporary file containing the image data (required if imageData, imageUrl, or r2Key is not provided)',
    example: '/tmp/tiktok-shop-temp/image-12345.jpg',
    required: false,
  })
  @ValidateIf((o) => !o.imageData && !o.imageUrl && !o.r2Key)
  @IsString()
  tempFilePath?: string;

  @ApiProperty({
    description:
      'Cloudflare R2 storage key for the image (required if imageData, imageUrl, or tempFilePath is not provided)',
    example: 'products/123/1621234567890/image-1',
    required: false,
  })
  @ValidateIf((o) => !o.imageData && !o.imageUrl && !o.tempFilePath)
  @IsString()
  r2Key?: string;

  @ApiProperty({
    description: 'The TikTok Shop ID for organizing uploads',
    example: '7082427311584347905',
    required: false,
  })
  @IsString()
  @IsOptional()
  shopId?: string;

  @ApiProperty({
    description: 'The use case for the image',
    enum: ImageUseCase,
    example: ImageUseCase.MAIN_IMAGE,
    required: false,
  })
  @IsEnum(ImageUseCase)
  @IsOptional()
  useCase?: ImageUseCase = ImageUseCase.MAIN_IMAGE;
}

export class UploadImageResponseDto {
  @ApiProperty({
    description: 'The URI of the uploaded image',
    example: 'tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5',
  })
  uri: string;

  @ApiProperty({
    description: 'The URL to access the uploaded image',
    example:
      'https://p16-tiktokshop-sg.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5~tplv-tiktokshop-v1:0:0:q-70.webp',
  })
  url: string;

  @ApiProperty({
    description: 'The width of the image',
    example: 800,
  })
  width: number;

  @ApiProperty({
    description: 'The height of the image',
    example: 600,
  })
  height: number;

  @ApiProperty({
    description: 'The use case of the image',
    enum: ImageUseCase,
    example: ImageUseCase.MAIN_IMAGE,
  })
  useCase: ImageUseCase;

  @ApiProperty({
    description: 'The R2 storage key of the image (if stored in R2)',
    example: 'products/123/1621234567890/image-1',
    required: false,
  })
  r2Key?: string;
}
