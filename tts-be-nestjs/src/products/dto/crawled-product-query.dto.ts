import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsIn, IsDateString, IsBoolean, IsNumber, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationQueryDto } from '../../common/dto/pagination-query.dto';

export class CrawledProductQueryDto extends PaginationQueryDto {
  @ApiProperty({
    description: 'Filter by marketplace',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
    required: false,
  })
  @IsOptional()
  @IsIn(['etsy', 'ebay', 'amazon'])
  marketplace?: string;

  @ApiProperty({
    description: 'Search in product titles',
    example: 'vintage t-shirt',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Filter by seller name',
    example: 'VintageClothingCo',
    required: false,
  })
  @IsOptional()
  @IsString()
  sellerName?: string;

  @ApiProperty({
    description: 'Filter by market ID',
    example: 'etsy-1452582422',
    required: false,
  })
  @IsOptional()
  @IsString()
  marketId?: string;

  @ApiProperty({
    description: 'Filter by public status',
    example: true,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isPublic?: boolean;

  @ApiProperty({
    description: 'Filter products extracted after this date',
    example: '2024-01-01T00:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  extractedAfter?: string;

  @ApiProperty({
    description: 'Filter products extracted before this date',
    example: '2024-01-31T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  extractedBefore?: string;

  @ApiProperty({
    description: 'Filter products with minimum review count',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minReviewCount?: number;

  @ApiProperty({
    description: 'Filter products with maximum review count',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxReviewCount?: number;

}
