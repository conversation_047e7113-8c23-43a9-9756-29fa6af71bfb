import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUrl, IsOptional, IsArray, ValidateNested, IsIn, IsBoolean, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCrawledProductImageDto {
  @ApiProperty({
    description: 'Original image URL from marketplace',
    example: 'https://i.etsystatic.com/12345/r/il/abc123/1234567890/il_794xN.1234567890_xyz.jpg',
  })
  @IsUrl()
  imageUrl: string;

  @ApiProperty({
    description: 'Whether this is the primary/main product image',
    example: true,
    required: false,
  })
  @IsOptional()
  isPrimary?: boolean;

  @ApiProperty({
    description: 'Order/position of the image in the product gallery',
    example: 1,
    required: false,
  })
  @IsOptional()
  sortOrder?: number;
}

export class CreateCrawledProductDto {
  @ApiProperty({
    description: 'Product title extracted from marketplace',
    example: 'Vintage Cotton T-Shirt - Premium Quality',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Direct URL to the product page',
    example: 'https://www.etsy.com/listing/123456789/vintage-cotton-t-shirt',
  })
  @IsUrl()
  productUrl: string;

  @ApiProperty({
    description: 'Marketplace source platform',
    example: 'etsy',
    enum: ['etsy', 'ebay', 'amazon'],
  })
  @IsIn(['etsy', 'ebay', 'amazon'])
  marketplace: string;

  @ApiProperty({
    description: 'Unique marketplace identifier to prevent duplicates across platforms',
    example: 'etsy-1452582422',
  })
  @IsString()
  marketId: string;

  @ApiProperty({
    description: 'Name of the seller/shop',
    example: 'VintageClothingCo',
    required: false,
  })
  @IsOptional()
  @IsString()
  sellerName?: string;

  @ApiProperty({
    description: 'Number of reviews for this product',
    example: 127,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  reviewCount?: number;

  @ApiProperty({
    description: 'Additional metadata extracted from the product page',
    example: {
      price: '$29.99',
      currency: 'USD',
      availability: 'In Stock',
      rating: '4.8',
    },
    required: false,
  })
  @IsOptional()
  metadata?: {
    price?: string;
    currency?: string;
    availability?: string;
    rating?: string;
    description?: string;
    [key: string]: any;
  };

  @ApiProperty({
    description: 'Array of product images',
    type: [CreateCrawledProductImageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCrawledProductImageDto)
  images?: CreateCrawledProductImageDto[];



  @ApiProperty({
    description: 'Whether this product can be shared with other users',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}
