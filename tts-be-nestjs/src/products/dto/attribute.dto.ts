import { ApiProperty } from '@nestjs/swagger';
import { IAttributeValue } from '../interfaces/attribute-value.interface';

export class AttributeValueDto implements IAttributeValue {
  @ApiProperty({
    description: 'TikTok Shop attribute value ID',
    example: '7082427311584347905',
  })
  idTT: string;

  @ApiProperty({
    description: 'Attribute value name',
    example: 'Cotton',
  })
  name: string;
}

export class AttributeDto {
  @ApiProperty({
    description: 'Internal attribute ID',
    example: 1,
    required: false,
  })
  id?: number;

  @ApiProperty({
    description: 'TikTok Shop attribute ID',
    example: '7082427311584347905',
  })
  idTT: string;

  @ApiProperty({
    description: 'Attribute name',
    example: 'Material',
  })
  name: string;

  @ApiProperty({
    description: 'Whether this attribute is required',
    example: true,
  })
  isRequired: boolean;

  @ApiProperty({
    description: 'Whether this attribute is a sales attribute',
    example: false,
  })
  isSalesAttr: boolean;

  @ApiProperty({
    description: 'Input type for this attribute',
    example: 'MULTIPLE_SELECT',
    required: false,
  })
  inputType?: string;

  @ApiProperty({
    description: 'Whether this attribute allows multiple selections',
    example: true,
    required: false,
  })
  isMultipleSelection?: boolean;

  @ApiProperty({
    description: 'Whether this attribute is customizable',
    example: false,
    required: false,
  })
  isCustomizable?: boolean;

  @ApiProperty({
    description: 'Value data format for this attribute',
    example: 'POSITIVE_INT_OR_DECIMAL',
    required: false,
  })
  valueDataFormat?: string;

  @ApiProperty({
    description: 'Attribute type (SALES_PROPERTY or PRODUCT_PROPERTY)',
    example: 'SALES_PROPERTY',
    required: false,
  })
  type?: string;

  @ApiProperty({
    description: 'Category IDs this attribute belongs to',
    example: ['853000', '853001'],
    required: false,
    type: [String],
  })
  categoryIdTT?: string[];

  @ApiProperty({
    description: 'Attribute values',
    type: [AttributeValueDto],
  })
  values: AttributeValueDto[];

  @ApiProperty({
    description: 'Last updated date',
    example: '2023-05-15T10:30:00.000Z',
    required: false,
  })
  updatedAt?: Date;
}

export class AttributeResponseDto {
  @ApiProperty({
    description: 'List of attributes',
    type: [AttributeDto],
  })
  attributes: AttributeDto[];
}

export class AttributeQueryDto {
  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
  })
  categoryId: string;
}
