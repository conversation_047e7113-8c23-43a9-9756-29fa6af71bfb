import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsArray,
  IsObject,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export class CreateTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Gildan 1-sided T-shirt',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Product description template',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Category ID', example: 1 })
  @IsNumber()
  categoryId: number;

  @ApiProperty({ description: 'Brand ID', example: 1 })
  @IsNumber()
  brandId: number;

  @ApiProperty({
    description: 'Whether this template is available for all users to use',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiProperty({ description: 'Product images' })
  @IsArray()
  @IsOptional()
  images?: Array<{
    imageUrl?: string;
    r2Key?: string;
  }>;

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
  })
  @IsArray()
  productAttributes?: Array<{
    id: string;
    name?: string;
    values: Array<{
      id?: string;
      name?: string;
    }>;
  }>;

  @ApiProperty({ description: 'Product SKUs template' })
  @IsArray()
  skus?: Array<{
    salesAttributes: Array<{
      name: string;
      valueName: string;
      skuImg?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      };
      supplementarySkuImages?: {
        imageUrl?: string;
        r2Key?: string;
        uri?: string;
      }[];
    }>;
    inventory: Array<{
      quantity: number;
    }>;
    price: {
      amount: number;
      currency: string;
    };
    listPrice?: {
      amount: number;
      currency: string;
    };
    identifierCode?: {
      code: string;
      type: string;
    };
    sellerSku?: string;
  }>;

  @ApiProperty({
    description: 'Size chart information template',
    required: false,
  })
  @IsOptional()
  @IsObject()
  sizeChart?: {
    imageUrl?: string;
    r2Key?: string;
  };

  @ApiProperty({
    description: 'Package dimensions template',
    example: {
      length: '20',
      width: '15',
      height: '5',
      unit: 'CENTIMETER',
    },
  })
  @IsObject()
  packageDimensions?: {
    length: string;
    width: string;
    height: string;
    unit: string;
  };

  @ApiProperty({
    description: 'Package weight template',
    example: {
      value: '500',
      unit: 'GRAM',
    },
  })
  @IsObject()
  packageWeight?: {
    value: string;
    unit: string;
  };
}
