import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CategoryDto } from './category.dto';
import { BrandDto } from './brand.dto';

export class StagedProductImageDto {
  @ApiProperty({
    description: 'URL of the image to upload (if not already uploaded)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'R2 storage key for the image',
    example: 'products/123/1621234567890/image-1',
    required: false,
  })
  @IsString()
  @IsOptional()
  r2Key?: string;
}

export class StagedProductSkuImageDto {
  @ApiProperty({
    description: 'Image URI from TikTok Shop',
    example: 'tos-maliva-i-o3syd03w52-us/c6b2b7b3b4b5b6b7b8b9c0c1c2c3c4c5',
  })
  @IsString()
  @IsOptional()
  uri?: string;

  @ApiProperty({
    description: 'URL of the image to upload (if not already uploaded)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'R2 storage key for the image',
    example: 'products/123/1621234567890/image-1',
    required: false,
  })
  @IsString()
  @IsOptional()
  r2Key?: string;
}

export class StagedProductSkuAttributeDto {
  @ApiProperty({
    description: 'Attribute name',
    example: 'Color',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Attribute value name',
    example: 'Red',
  })
  @IsString()
  @IsNotEmpty()
  valueName: string;

  @ApiProperty({
    description: 'SKU image for this attribute value',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => StagedProductSkuImageDto)
  skuImg?: StagedProductSkuImageDto;

  @ApiProperty({
    description: 'Supplementary SKU images for this attribute value',
    type: [StagedProductSkuImageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductSkuImageDto)
  supplementarySkuImages?: StagedProductSkuImageDto[];
}

export class StagedProductSkuInventoryDto {
  @ApiProperty({
    description: 'SKU quantity',
    example: 100,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Warehouse ID',
    example: '7000714532876273420',
  })
  @IsString()
  @IsOptional()
  warehouseId?: string;
}

export class StagedProductSkuPriceDto {
  @ApiProperty({
    description: 'Price amount',
    example: 19.99,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    default: 'USD',
    required: false,
  })
  @IsString()
  @IsOptional()
  currency: string;
}

export class StagedProductSkuIdentifierCodeDto {
  @ApiProperty({
    description: 'Identifier code',
    example: '10000000000010',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'Identifier type',
    example: 'GTIN',
  })
  @IsString()
  @IsNotEmpty()
  type: string;
}

export class StagedProductSkuDto {
  @ApiProperty({
    description: 'SKU sales attributes',
    type: [StagedProductSkuAttributeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductSkuAttributeDto)
  salesAttributes: StagedProductSkuAttributeDto[];

  @ApiProperty({
    description: 'SKU inventory',
    type: [StagedProductSkuInventoryDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductSkuInventoryDto)
  inventory: StagedProductSkuInventoryDto[];

  @ApiProperty({
    description: 'SKU price',
    type: StagedProductSkuPriceDto,
  })
  @ValidateNested()
  @Type(() => StagedProductSkuPriceDto)
  price: StagedProductSkuPriceDto;

  @ApiProperty({
    description: 'SKU list price',
    type: StagedProductSkuPriceDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => StagedProductSkuPriceDto)
  listPrice?: StagedProductSkuPriceDto;

  @ApiProperty({
    description: 'SKU identifier code',
    type: StagedProductSkuIdentifierCodeDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => StagedProductSkuIdentifierCodeDto)
  identifierCode?: StagedProductSkuIdentifierCodeDto;

  @ApiProperty({
    description:
      'An internal code/name for managing SKUs, not visible to buyers',
    example: 'TS-BLK-S',
    required: false,
    maxLength: 50,
  })
  @IsString()
  @IsOptional()
  sellerSku?: string;
}

export class StagedProductAttributeValueDto {
  @ApiProperty({
    description: 'Attribute value ID',
    example: '7082427311584347906',
    required: false,
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Attribute value name',
    example: 'Red',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class StagedProductAttributeDto {
  @ApiProperty({
    description: 'Attribute ID',
    example: '7082427311584347905',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Attribute name',
    example: 'Color',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Attribute values',
    type: [StagedProductAttributeValueDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductAttributeValueDto)
  values: StagedProductAttributeValueDto[];
}

export class StagedProductPackageDimensionsDto {
  @ApiProperty({
    description: 'Package length',
    example: '20',
  })
  @IsString()
  @IsNotEmpty()
  length: string;

  @ApiProperty({
    description: 'Package width',
    example: '15',
  })
  @IsString()
  @IsNotEmpty()
  width: string;

  @ApiProperty({
    description: 'Package height',
    example: '5',
  })
  @IsString()
  @IsNotEmpty()
  height: string;

  @ApiProperty({
    description: 'Unit for package dimensions',
    example: 'CENTIMETER',
  })
  @IsString()
  @IsNotEmpty()
  unit: string;
}

export class StagedProductPackageWeightDto {
  @ApiProperty({
    description: 'Package weight value',
    example: '500',
  })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiProperty({
    description: 'Unit for package weight',
    example: 'GRAM',
  })
  @IsString()
  @IsNotEmpty()
  unit: string;
}

export class StagedProductSizeChartDto {
  @ApiProperty({
    description:
      'URL of the size chart image to upload (if not already uploaded)',
    example: 'https://example.com/size-chart.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'R2 storage key for the size chart image',
    example: 'products/123/1621234567890/size-chart',
    required: false,
  })
  @IsString()
  @IsOptional()
  r2Key?: string;
}

export class CreateStagedProductDto {
  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
  })
  @IsNumber()
  @IsNotEmpty()
  categoryId: number;

  @ApiProperty({
    description: 'Brand ID',
    example: '7082427311584347905',
  })
  @IsNumber()
  @IsNotEmpty()
  brandId: number;

  @ApiProperty({
    description:
      'Category version (v1 for 3-level category tree, v2 for 7-level US category tree)',
    example: 'v2',
    default: 'v2',
  })
  @IsString()
  @IsOptional()
  categoryVersion?: string;

  @ApiProperty({
    description: 'Product images',
    type: [StagedProductImageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductImageDto)
  images: StagedProductImageDto[];

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
    type: [StagedProductAttributeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductAttributeDto)
  productAttributes: StagedProductAttributeDto[];

  @ApiProperty({
    description: 'Product SKUs',
    type: [StagedProductSkuDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductSkuDto)
  skus: StagedProductSkuDto[];

  @ApiProperty({
    description: 'Package dimensions',
    type: StagedProductPackageDimensionsDto,
  })
  @ValidateNested()
  @Type(() => StagedProductPackageDimensionsDto)
  packageDimensions: StagedProductPackageDimensionsDto;

  @ApiProperty({
    description: 'Package weight',
    type: StagedProductPackageWeightDto,
  })
  @ValidateNested()
  @Type(() => StagedProductPackageWeightDto)
  packageWeight: StagedProductPackageWeightDto;

  @ApiProperty({
    description: 'Size chart information',
    type: StagedProductSizeChartDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => StagedProductSizeChartDto)
  sizeChart?: StagedProductSizeChartDto;
}

export class CreateStagedProductFromTemplateDto {
  @ApiProperty({
    description: 'Template ID to use for creating the staged product',
    example: 1,
  })
  @IsNumber()
  templateId: number;

  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Product images',
    type: [StagedProductImageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductImageDto)
  images: StagedProductImageDto[];
}

export class CreateStagedProductFromTemplateAndUploadDto {
  @ApiProperty({
    description: 'Template ID to use for creating the staged product',
    example: 1,
  })
  @IsNumber()
  templateId: number;

  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Product images',
    type: [StagedProductImageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductImageDto)
  images: StagedProductImageDto[];

  @ApiProperty({
    description: 'TikTok shop ID to upload the product to',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  tiktokShopId: number;

  @ApiProperty({
    description: 'Skip validation during upload',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  skipValidation?: boolean;

  @ApiProperty({
    description: 'Force upload even if there are warnings',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  forceUpload?: boolean;
}

export class UpdateStagedProductDto {
  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  categoryId?: number;

  @ApiProperty({
    description: 'Brand ID',
    example: '7082427311584347905',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  brandId?: number;

  @ApiProperty({
    description:
      'Category version (v1 for 3-level category tree, v2 for 7-level US category tree)',
    example: 'v2',
    default: 'v2',
    required: false,
  })
  @IsString()
  @IsOptional()
  categoryVersion?: string;

  @ApiProperty({
    description: 'Product images',
    type: [StagedProductImageDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductImageDto)
  @IsOptional()
  images?: StagedProductImageDto[];

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
    type: [StagedProductAttributeDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductAttributeDto)
  @IsOptional()
  productAttributes?: StagedProductAttributeDto[];

  @ApiProperty({
    description: 'Product SKUs',
    type: [StagedProductSkuDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StagedProductSkuDto)
  @IsOptional()
  skus?: StagedProductSkuDto[];

  @ApiProperty({
    description: 'Package dimensions',
    type: StagedProductPackageDimensionsDto,
    required: false,
  })
  @ValidateNested()
  @Type(() => StagedProductPackageDimensionsDto)
  @IsOptional()
  packageDimensions?: StagedProductPackageDimensionsDto;

  @ApiProperty({
    description: 'Package weight',
    type: StagedProductPackageWeightDto,
    required: false,
  })
  @ValidateNested()
  @Type(() => StagedProductPackageWeightDto)
  @IsOptional()
  packageWeight?: StagedProductPackageWeightDto;

  @ApiProperty({
    description: 'Size chart information',
    type: StagedProductSizeChartDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => StagedProductSizeChartDto)
  sizeChart?: StagedProductSizeChartDto;
}

export class StagedProductResponseDto {
  @ApiProperty({
    description: 'Staged product ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Product title',
    example: 'Premium Cotton T-Shirt',
  })
  title: string;

  @ApiProperty({
    description: 'Product description',
    example: 'High-quality cotton t-shirt with a comfortable fit.',
  })
  description: string;

  @ApiProperty({
    description: 'Category ID',
    example: '853000',
  })
  categoryId: number;

  @ApiProperty({
    description: 'Brand ID',
    example: '7082427311584347905',
  })
  brandId: number;

  @ApiProperty({
    description:
      'Category version (v1 for 3-level category tree, v2 for 7-level US category tree)',
    example: 'v2',
  })
  categoryVersion: string;

  @ApiProperty({
    description: 'Product images',
    type: [StagedProductImageDto],
  })
  images: StagedProductImageDto[];

  @ApiProperty({
    description: 'Product attributes (aligned with TikTok SDK naming)',
    type: [StagedProductAttributeDto],
  })
  productAttributes: StagedProductAttributeDto[];

  @ApiProperty({
    description: 'Product SKUs',
    type: [StagedProductSkuDto],
  })
  skus: StagedProductSkuDto[];

  @ApiProperty({
    description: 'Package dimensions',
    type: StagedProductPackageDimensionsDto,
  })
  packageDimensions: StagedProductPackageDimensionsDto;

  @ApiProperty({
    description: 'Package weight',
    type: StagedProductPackageWeightDto,
  })
  packageWeight: StagedProductPackageWeightDto;

  @ApiProperty({
    description: 'Size chart information',
    type: StagedProductSizeChartDto,
    required: false,
  })
  sizeChart?: StagedProductSizeChartDto;

  @ApiProperty({
    description: 'Created date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Category information',
    type: CategoryDto,
    required: false,
  })
  category?: CategoryDto;

  @ApiProperty({
    description: 'Brand information',
    type: BrandDto,
    required: false,
  })
  brand?: BrandDto;
}
