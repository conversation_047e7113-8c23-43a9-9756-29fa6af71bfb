import { ApiProperty } from '@nestjs/swagger';
import { ProductStatus } from '../entities/product.entity';

export class TikTokShopResponseDto {
  @ApiProperty({ description: 'TikTok Shop ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'TikTok Shop name', example: 'Maomao beauty shop' })
  name: string;

  @ApiProperty({ description: 'User-friendly name for the TikTok Shop', example: 'My Main Beauty Store' })
  friendly_name?: string;

  @ApiProperty({ description: 'The region of the shop', example: 'GB' })
  region?: string;

  @ApiProperty({ description: 'The TikTok Shop code', example: 'CNGBCBA4LLU8' })
  code: string;
}

export class SkuResponseDto {
  @ApiProperty({ description: 'Internal SKU ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'TikTok Shop SKU ID', example: '1234567890' })
  idTT: string;

  @ApiProperty({
    description: 'An internal code/name for managing SKUs',
    example: 'GILDAN-5000-BLK-S',
  })
  sellerSku: string;

  @ApiProperty({
    description: 'SKU list prices from external platforms',
    example: [
      {
        amount: '29.99',
        currency: 'USD',
        source: 'SHOPIFY_COMPARE_AT_PRICE',
      },
    ],
  })
  externalListPrices?: {
    amount: number;
    currency: string;
    source: string;
  }[];

  @ApiProperty({
    description: 'SKU inventory information',
    example: [
      {
        quantity: 100,
        warehouseId: '123456',
      },
    ],
  })
  inventory: {
    quantity: number;
    warehouseId: string;
  }[];

  @ApiProperty({
    description: 'SKU price information',
    example: {
      salePrice: 24.99,
      taxExclusivePrice: 26.99,
      currency: 'USD',
      unitPrice: 1,
    },
  })
  price: {
    salePrice: number | null;
    taxExclusivePrice: number | null;
    currency: string;
    unitPrice?: number | null;
  };

  @ApiProperty({
    description: 'SKU list price information',
    example: {
      amount: 29.99,
      currency: 'USD',
    },
  })
  listPrice?: {
    amount: number | null;
    currency: string;
  } | null;

  @ApiProperty({
    description: 'Sales attributes',
    example: [
      {
        id: '100000',
        name: 'Color',
        valueId: '100000',
        valueName: 'Red',
      },
    ],
  })
  salesAttributes?: {
    id: string;
    name: string;
    valueId: string;
    valueName: string;
    skuImg?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    supplementarySkuImages?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
  }[];

  @ApiProperty({ description: 'External SKU ID', example: '1729592969712207234' })
  externalSkuId?: string;

  @ApiProperty({
    description: 'Combined SKUs',
    example: [
      {
        productId: '1729582718312380123',
        skuId: '1729582718312380123',
        skuCount: 1,
      },
    ],
  })
  combinedSkus?: {
    productId: string;
    skuId: string;
    skuCount: number;
  }[];

  @ApiProperty({
    description: 'Global listing policy',
    example: {
      priceSync: true,
      inventoryType: 'SHARED',
    },
  })
  globalListingPolicy?: {
    priceSync?: boolean;
    inventoryType?: string;
    replicateSource?: {
      productId: string;
      shopId: string;
      skuId: string;
    };
  };

  @ApiProperty({ description: 'SKU unit count', example: '1.00' })
  skuUnitCount?: string;

  @ApiProperty({
    description: 'External URLs',
    example: ['https://example.com/path1', 'https://example.com/path2'],
  })
  externalUrls?: string[];

  @ApiProperty({
    description: 'Pre-sale information',
    example: {
      type: 'PRE_ORDER',
      fulfillmentType: {
        handlingDurationDays: 3,
      },
    },
  })
  preSale?: {
    type: string;
    fulfillmentType?: {
      handlingDurationDays: number;
    };
  };

  @ApiProperty({
    description: 'Identifier code',
    example: {
      code: '10000000000010',
      type: 'GTIN',
    },
  })
  identifierCode?: {
    code: string;
    type: string;
  };

  @ApiProperty({
    description: 'Extra identifier codes',
    example: ['00012345678905', '9780596520687'],
  })
  extraIdentifierCodes?: string[];

  @ApiProperty({ description: 'Created date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated date' })
  updatedAt: Date;
}

export class ProductResponseDto {
  @ApiProperty({ description: 'Product ID internal system', example: 1 })
  id: number;

  @ApiProperty({
    description: 'Product ID of TikTok Shop',
    example: '7891234567',
  })
  idTT: string;

  @ApiProperty({
    description: 'Category',
    example: 'Women Tops/Hoodies & Sweatshirts/Women Pullover Sweatshirts',
  })
  category?: string;

  @ApiProperty({
    description: 'Category chains from TikTok Shop',
    example: [
      {
        id: '853000',
        parentId: '851848',
        localName: 'Botol & Stoples Penyimpanan',
        isLeaf: true,
      },
    ],
  })
  categoryChains?: {
    id: string;
    parentId: string;
    localName: string;
    isLeaf: boolean;
  }[];

  @ApiProperty({ description: 'Brand', example: 'Gildan' })
  brandInfo?: string;

  @ApiProperty({
    description: 'Brand information from TikTok Shop',
    example: { id: '7082427311584347905', name: 'brand xxx aaa' },
  })
  brand?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Product title',
    example: 'T-Shirt Gildan 5000 1 side',
  })
  title: string;

  @ApiProperty({
    description: 'Product description',
    example:
      'Gildan 5000 - Classic Heavy Cotton T-Shirt - Durability Meets Comfort!',
  })
  description?: string;

  @ApiProperty({
    description: 'Size chart information from TikTok Shop',
    example: {
      image: {
        urls: ['https://example.com/size-chart.png'],
      },
      template: { id: '7267563252536723205' },
    },
  })
  sizeChart?: {
    image?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    };
    template?: {
      id: string;
    };
  };

  @ApiProperty({
    description: 'Product Image List',
    example: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ],
  })
  productImages?: string[];

  @ApiProperty({
    description: 'Main images information from TikTok Shop',
    example: [
      {
        height: 600,
        width: 600,
        thumbUrls: ['https://example.com/thumb.jpg'],
        uri: 'tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4',
        urls: ['https://example.com/image.jpg'],
      },
    ],
  })
  mainImages?: {
    height?: number;
    width?: number;
    thumbUrls?: string[];
    uri?: string;
    urls?: string[];
  }[];

  @ApiProperty({
    description: 'Video information from TikTok Shop',
    example: {
      id: 'v09ea0g40000cj91373c77u3mid3g1s0',
      coverUrl: 'https://example.com/cover.jpg',
      format: 'MP4',
      url: 'https://example.com/video.mp4',
      width: 1280,
      height: 480,
      size: 1000,
    },
  })
  videoInfo?: {
    id?: string;
    coverUrl?: string;
    format?: string;
    url?: string;
    width?: number;
    height?: number;
    size?: number;
  };

  @ApiProperty({ example: 12, description: 'Package Length in inch' })
  packageLength?: number;

  @ApiProperty({ example: 10, description: 'Package Width in inch' })
  packageWidth?: number;

  @ApiProperty({ example: 1.5, description: 'Package Height in inch' })
  packageHeight?: number;

  @ApiProperty({
    description: 'Package dimensions information from TikTok Shop',
    example: {
      length: '10',
      width: '10',
      height: '10',
      unit: 'CENTIMETER',
    },
  })
  packageDimensions?: {
    length?: string;
    width?: string;
    height?: string;
    unit?: string;
  };

  @ApiProperty({
    description: 'Package weight information from TikTok Shop',
    example: {
      value: '1.32',
      unit: 'KILOGRAM',
    },
  })
  packageWeight?: {
    value?: string;
    unit?: string;
  };

  @ApiProperty({
    description: 'Product status in TikTok Shop',
    enum: ProductStatus,
    example: ProductStatus.DRAFT,
  })
  status: ProductStatus;

  @ApiProperty({ description: 'Product sync fail reasons' })
  productSyncFailReasons?: string[];

  @ApiProperty({ description: 'Recommended categories' })
  recommendedCategories?: {
    id: string;
    localName: string;
  }[];

  @ApiProperty({ description: 'Is not for sale flag' })
  isNotForSale: boolean;

  @ApiProperty({ description: 'Integrated platform statuses' })
  integratedPlatformStatuses?: {
    platform: string;
    status: string;
  }[];

  @ApiProperty({ description: 'Sales regions' })
  salesRegions?: string[];

  @ApiProperty({ description: 'Audit status' })
  audit?: {
    status: string;
    preApprovedReasons?: string[];
  };

  @ApiProperty({ description: 'Audit failed reasons' })
  auditFailedReasons?: {
    position?: string;
    reasons?: string[];
    suggestions?: string[];
    listingPlatform?: string;
  }[];

  @ApiProperty({
    description: 'Listing quality tier',
    nullable: true,
  })
  listingQualityTier?: string;

  @ApiProperty({ description: 'Is COD allowed' })
  isCodAllowed?: boolean;

  @ApiProperty({ description: 'Product attributes' })
  productAttributes?: {
    id: string;
    name: string;
    values: {
      id: string;
      name: string;
    }[];
  }[];

  @ApiProperty({ description: 'Create time from TikTok Shop' })
  createTimeTT?: number;

  @ApiProperty({ description: 'Update time from TikTok Shop' })
  updateTimeTT?: number;

  @ApiProperty({ description: 'Delivery options' })
  deliveryOptions?: {
    id: string;
    name: string;
    isAvailable: boolean;
  }[];

  @ApiProperty({ description: 'External product ID' })
  externalProductId?: string;

  @ApiProperty({ description: 'Product types' })
  productTypes?: string[];

  @ApiProperty({ description: 'Manufacturer IDs' })
  manufacturerIds?: string[];

  @ApiProperty({ description: 'Responsible person IDs' })
  responsiblePersonIds?: string[];

  @ApiProperty({ description: 'Shipping insurance requirement' })
  shippingInsuranceRequirement?: string;

  @ApiProperty({ description: 'Minimum order quantity' })
  minimumOrderQuantity?: number;

  @ApiProperty({ description: 'Is pre-owned' })
  isPreOwned?: boolean;

  @ApiProperty({ description: 'Certifications' })
  certifications?: {
    id: string;
    title: string;
    files?: {
      id: string;
      urls: string[];
      name: string;
      format: string;
    }[];
    images?: {
      height?: number;
      width?: number;
      thumbUrls?: string[];
      uri?: string;
      urls?: string[];
    }[];
    expirationDate?: number;
  }[];

  @ApiProperty({ description: 'Global product association' })
  globalProductAssociation?: {
    globalProductId: string;
    skuMappings: {
      globalSkuId: string;
      localSkuId: string;
    }[];
  };

  @ApiProperty({ description: 'SKUs' })
  skus?: SkuResponseDto[];

  @ApiProperty({
    description: 'Tiktok Shop ID from internal system',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({ description: 'TikTok Shop information' })
  tiktokShop?: TikTokShopResponseDto;

  @ApiProperty({ description: 'Created Date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  updatedAt: Date;

  @ApiProperty({
    description: 'The ID of the user who owns this product',
    example: 1,
  })
  userId?: number;
}
