import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsPositive } from 'class-validator';

export class CreateProductVariantDto {
  @ApiProperty({ example: 'Black' })
  @IsString()
  color: string;

  @ApiProperty({ example: 'L' })
  @IsString()
  size: string;

  @ApiProperty({ example: 'https://example.com/variant-black-l.jpg' })
  @IsOptional()
  @IsString()
  variantImage?: string;

  @ApiProperty({ example: 19.99 })
  @IsNumber()
  @IsPositive()
  price: number;

  @ApiProperty({ example: 100 })
  @IsNumber()
  @IsPositive()
  stock: number;

  @ApiProperty()
  @IsNumber()
  productId: number;
}
