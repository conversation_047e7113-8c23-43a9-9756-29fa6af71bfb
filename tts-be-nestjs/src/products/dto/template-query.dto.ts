import { IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationQueryDto } from '../../common/dto/pagination-query.dto';

/**
 * DTO for template query parameters including pagination and filtering
 */
export class TemplateQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Filter templates by name (case-insensitive partial matching)',
    example: '<PERSON>dan',
  })
  @IsOptional()
  @IsString()
  name?: string;
}
