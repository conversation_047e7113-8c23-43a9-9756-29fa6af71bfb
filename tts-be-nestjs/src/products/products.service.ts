import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { CreateProductDto, CreateSkuDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductResponseDto, SkuResponseDto } from './dto/product-response.dto';
import { Product } from './entities/product.entity';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { PaginatedResult } from 'src/common/interfaces/paginated-result.interface';
import { FilterProductDto } from './dto/filter-product.dto';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
// TikTokShop entity is now accessed through TikTokShopService
import { Sku } from './entities/sku.entity';
import { executeInTransaction } from 'src/common/utils/transaction.util';
import { TikTokShopService } from 'src/tiktok-shop/tiktok-shop.service';

import { TikTokProductMapper } from './mappers/tiktok-product.mapper';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private readonly clientFactory: TikTokClientFactory,
    private readonly dataSource: DataSource,
    private readonly tikTokProductMapper: TikTokProductMapper,
    private readonly tikTokShopService: TikTokShopService,
  ) {}

  /**
   * Convert SKU entity to SkuResponseDto
   */
  private mapSkuToResponseDto(sku: Sku): SkuResponseDto {
    return {
      id: sku.id,
      idTT: sku.idTT,
      sellerSku: sku.sellerSku,
      externalListPrices: sku.externalListPrices,
      inventory: sku.inventory,
      price: sku.price,
      listPrice: sku.listPrice,
      salesAttributes: sku.salesAttributes,
      externalSkuId: sku.externalSkuId,
      combinedSkus: sku.combinedSkus,
      globalListingPolicy: sku.globalListingPolicy,
      skuUnitCount: sku.skuUnitCount,
      externalUrls: sku.externalUrls,
      preSale: sku.preSale,
      identifierCode: sku.identifierCode,
      extraIdentifierCodes: sku.extraIdentifierCodes,
      createdAt: sku.createdAt,
      updatedAt: sku.updatedAt,
    };
  }

  /**
   * Convert Product entity to ProductResponseDto (excluding rawTikTokResponse)
   */
  private mapProductToResponseDto(product: Product): ProductResponseDto {
    return {
      id: product.id,
      idTT: product.idTT,
      category: product.category,
      categoryChains: product.categoryChains,
      brandInfo: product.brandInfo,
      brand: product.brand,
      title: product.title,
      description: product.description,
      sizeChart: product.sizeChart,
      productImages: product.productImages,
      mainImages: product.mainImages,
      videoInfo: product.videoInfo,
      packageLength: product.packageLength,
      packageWidth: product.packageWidth,
      packageHeight: product.packageHeight,
      packageDimensions: product.packageDimensions,
      packageWeight: product.packageWeight,
      status: product.status,
      productSyncFailReasons: product.productSyncFailReasons,
      recommendedCategories: product.recommendedCategories,
      isNotForSale: product.isNotForSale,
      integratedPlatformStatuses: product.integratedPlatformStatuses,
      salesRegions: product.salesRegions,
      audit: product.audit,
      auditFailedReasons: product.auditFailedReasons,
      listingQualityTier: product.listingQualityTier,
      isCodAllowed: product.isCodAllowed,
      productAttributes: product.productAttributes,
      createTimeTT: product.createTimeTT,
      updateTimeTT: product.updateTimeTT,
      deliveryOptions: product.deliveryOptions,
      externalProductId: product.externalProductId,
      productTypes: product.productTypes,
      manufacturerIds: product.manufacturerIds,
      responsiblePersonIds: product.responsiblePersonIds,
      shippingInsuranceRequirement: product.shippingInsuranceRequirement,
      minimumOrderQuantity: product.minimumOrderQuantity,
      isPreOwned: product.isPreOwned,
      certifications: product.certifications,
      globalProductAssociation: product.globalProductAssociation,
      skus: product.skus?.map(sku => this.mapSkuToResponseDto(sku)) || [],
      tiktokShopId: product.tiktokShopId,
      tiktokShop: product.tiktokShop ? {
        id: product.tiktokShop.id,
        name: product.tiktokShop.name,
        friendly_name: product.tiktokShop.friendly_name,
        region: product.tiktokShop.region,
        code: product.tiktokShop.code,
      } : undefined,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      userId: product.userId,
      // Note: rawTikTokResponse is intentionally excluded
    };
  }

  /**
   * Create a new product or update an existing one
   * @param createProductDto Product data to create or update
   * @returns Created or updated product
   */
  async create(createProductDto: CreateProductDto): Promise<Product> {
    try {
      return await executeInTransaction(
        this.dataSource,
        async (manager) => {
          // Check if product already exists based on TikTok Shop ID
          const existingProduct = await manager.findOne(Product, {
            where: { idTT: createProductDto.idTT },
            relations: ['skus'],
          });

          if (existingProduct) {
            this.logger.log(
              `Updating existing product with idTT: ${createProductDto.idTT}`,
            );
            return await this.updateExistingProduct(
              existingProduct,
              createProductDto,
              manager,
            );
          } else {
            this.logger.log(
              `Creating new product with idTT: ${createProductDto.idTT}`,
            );
            return await this.createNewProduct(createProductDto, manager);
          }
        },
        this.logger,
      );
    } catch (error) {
      this.handleDatabaseError(error);
      throw error;
    }
  }

  /**
   * Update an existing product with new data
   * @param existingProduct Existing product entity
   * @param createProductDto New product data
   * @param manager Entity manager for transaction
   * @returns Updated product
   */
  private async updateExistingProduct(
    existingProduct: Product,
    createProductDto: CreateProductDto,
    manager: any,
  ): Promise<Product> {
    // Merge existing product with new data
    manager.merge(Product, existingProduct, {
      ...createProductDto,
      status: createProductDto.status,
    });

    // Handle SKUs
    if (createProductDto.skus?.length > 0) {
      await this.processSkusForExistingProduct(
        existingProduct,
        createProductDto.skus,
        manager,
      );
    }

    const updatedProduct = await manager.save(existingProduct);
    return await manager.findOne(Product, {
      where: { id: updatedProduct.id },
      relations: ['skus'],
    });
  }

  /**
   * Process SKUs for an existing product
   * @param existingProduct Existing product entity
   * @param skuDtos SKU DTOs to process
   * @param manager Entity manager for transaction
   */
  private async processSkusForExistingProduct(
    existingProduct: Product,
    skuDtos: CreateSkuDto[],
    manager: any,
  ): Promise<void> {
    for (const skuDto of skuDtos) {
      const existingSku = existingProduct.skus.find(
        (sku) => sku.idTT === skuDto.idTT,
      );

      if (existingSku) {
        // Update existing SKU
        manager.merge(Sku, existingSku, {
          ...skuDto,
          product: existingProduct,
        });
        await manager.save(existingSku);
      } else {
        // Check if SKU with this idTT exists in other products
        const duplicateSku = await manager.findOne(Sku, {
          where: { idTT: skuDto.idTT },
        });

        if (duplicateSku) {
          throw new Error(
            `SKU with idTT ${skuDto.idTT} already exists in another product`,
          );
        }

        // Create new SKU
        const newSku = manager.create(Sku, {
          ...skuDto,
          product: existingProduct,
        });
        existingProduct.skus.push(await manager.save(newSku));
      }
    }
  }

  /**
   * Create a new product
   * @param createProductDto Product data to create
   * @param manager Entity manager for transaction
   * @returns Created product
   */
  private async createNewProduct(
    createProductDto: CreateProductDto,
    manager: any,
  ): Promise<Product> {
    // Check for duplicate product idTT
    if (createProductDto.idTT) {
      const duplicateProduct = await manager.findOne(Product, {
        where: { idTT: createProductDto.idTT },
      });

      if (duplicateProduct) {
        throw new Error(
          `Product with idTT ${createProductDto.idTT} already exists`,
        );
      }
    }

    // Create new product
    const newProduct = manager.create(Product, {
      ...createProductDto,
      status: createProductDto.status,
    });

    // Create and associate SKUs
    if (createProductDto.skus?.length > 0) {
      const skus = await Promise.all(
        createProductDto.skus.map(async (skuDto) => {
          // Check for duplicate SKU idTT
          if (skuDto.idTT) {
            const duplicateSku = await manager.findOne(Sku, {
              where: { idTT: skuDto.idTT },
            });

            if (duplicateSku) {
              throw new Error(`SKU with idTT ${skuDto.idTT} already exists`);
            }
          }

          return manager.create(Sku, {
            ...skuDto,
            product: newProduct,
          });
        }),
      );
      newProduct.skus = skus;
    }

    const savedProduct = await manager.save(newProduct);
    const result = await manager.findOne(Product, {
      where: { id: savedProduct.id },
      relations: ['skus'],
    });

    if (!result) {
      throw new NotFoundException(
        `Product with ID ${savedProduct.id} not found after save`,
      );
    }

    return result;
  }

  /**
   * Handle database errors
   * @param error Error to handle
   */
  private handleDatabaseError(error: any): void {
    if (error.code === '23505') {
      // PostgreSQL unique violation error code
      if (error.detail.includes('products')) {
        throw new Error(`Product with this TikTok Shop ID already exists`);
      } else if (error.detail.includes('skus')) {
        throw new Error(`SKU with this TikTok Shop ID already exists`);
      }
    }
  }

  async findAll(
    paginationQuery: PaginationQueryDto,
    filterDto: FilterProductDto,
    userId: number,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    this.logger.log(
      `Fetching products with pagination: ${JSON.stringify(paginationQuery)} and filters: ${JSON.stringify(filterDto)} for user: ${userId}`,
    );

    // Note: SKUs are not loaded in list view for performance optimization
    // Use findOne() method to get detailed product information including SKUs
    const { limit = 20, page = 1, sortField = 'updatedAt', sortDirection = 'DESC' } = paginationQuery;
    const {
      search,
      name,
      status,
      tiktokShopId,
      categoryId,
      brandId,
      minPrice,
      maxPrice,
      createTimeFrom,
      createTimeTo
    } = filterDto;

    // Calculate the skip value based on page and limit
    const skip = (page - 1) * limit;

    const queryBuilder = this.productRepository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.tiktokShop', 'tiktokShop')
      .distinct(true);

    // Add userId filter
    queryBuilder.andWhere('product.userId = :userId', { userId });

    // Search filter (search in title)
    if (search) {
      queryBuilder.andWhere('product.title ILIKE :search', { search: `%${search}%` });
    }

    // Legacy name filter (for backward compatibility)
    if (name) {
      queryBuilder.andWhere('product.title ILIKE :name', { name: `%${name}%` });
    }

    // Status filter
    if (status) {
      queryBuilder.andWhere('product.status = :status', { status });
    }

    // TikTok Shop filter
    if (tiktokShopId) {
      queryBuilder.andWhere('product.tiktokShopId = :tiktokShopId', { tiktokShopId });
    }

    // Category filter (using categoryChains JSONB field)
    if (categoryId) {
      queryBuilder.andWhere(
        "product.categoryChains::text ILIKE :categoryId",
        { categoryId: `%"id":"${categoryId}"%` }
      );
    }

    // Brand filter (using brand JSONB field)
    if (brandId) {
      queryBuilder.andWhere(
        "product.brand->>'id' = :brandId",
        { brandId: brandId.toString() }
      );
    }

    // Price filters - removed since SKUs are not joined in list view for performance
    // Price filtering can be implemented at the individual product level if needed
    if (minPrice !== undefined || maxPrice !== undefined) {
      this.logger.warn('Price filtering is not supported in product list view for performance reasons. Use individual product endpoints for detailed price information.');
    }

    // Date range filters
    if (createTimeFrom) {
      const fromDate = new Date(createTimeFrom * 1000);
      queryBuilder.andWhere('product.createdAt >= :createTimeFrom', { createTimeFrom: fromDate });
    }

    if (createTimeTo) {
      const toDate = new Date(createTimeTo * 1000);
      queryBuilder.andWhere('product.createdAt <= :createTimeTo', { createTimeTo: toDate });
    }

    // Sorting
    const validSortFields = ['title', 'status', 'createdAt', 'updatedAt', 'createTimeTT', 'updateTimeTT'];
    const actualSortField = validSortFields.includes(sortField) ? sortField : 'updatedAt';
    const actualSortDirection = sortDirection.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    queryBuilder.orderBy(`product.${actualSortField}`, actualSortDirection as 'ASC' | 'DESC');
    queryBuilder.take(limit);
    queryBuilder.skip(skip);

    const [data, total] = await queryBuilder.getManyAndCount();

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    // Map Product entities to ProductResponseDto (excluding rawTikTokResponse)
    const mappedData = data.map(product => this.mapProductToResponseDto(product));

    return {
      data: mappedData,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Internal method to find a product entity (for internal service use)
   */
  private async findOneInternal(id: number, userId: number): Promise<Product> {
    this.logger.log(`Finding product with ID: ${id} for user: ${userId}`);
    const product = await this.productRepository.findOne({
      where: { id, userId },
      relations: ['skus', 'tiktokShop'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  /**
   * Public method to find a product (returns DTO for API responses)
   */
  async findOne(id: number, userId: number): Promise<ProductResponseDto> {
    const product = await this.findOneInternal(id, userId);
    return this.mapProductToResponseDto(product);
  }

  async exportProducts(filterDto: FilterProductDto, userId: number): Promise<string> {
    this.logger.log(`Exporting products with filters: ${JSON.stringify(filterDto)} for user: ${userId}`);

    // Get all products without pagination for export
    const paginationQuery = new PaginationQueryDto();
    paginationQuery.limit = 10000; // Large limit to get all products
    paginationQuery.page = 1;
    const result = await this.findAll(paginationQuery, filterDto, userId);

    // CSV headers
    const headers = [
      'ID',
      'TikTok Product ID',
      'Title',
      'Status',
      'Brand',
      'Category',
      'SKU Count',
      'Min Price',
      'Max Price',
      'Currency',
      'Created At',
      'Updated At'
    ];

    // Convert products to CSV rows
    const rows = result.data.map(product => {
      const prices = product.skus?.map(sku => sku.price?.salePrice || 0).filter(price => price > 0) || [];
      const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
      const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;
      const currency = product.skus?.[0]?.price?.currency || 'USD';

      return [
        product.id,
        product.idTT || '',
        `"${(product.title || '').replace(/"/g, '""')}"`, // Escape quotes in CSV
        product.status,
        product.brand?.name || product.brandInfo || '',
        product.categoryChains?.[product.categoryChains.length - 1]?.localName || product.category || '',
        product.skus?.length || 0,
        minPrice.toFixed(2),
        maxPrice.toFixed(2),
        currency,
        product.createdAt,
        product.updatedAt
      ];
    });

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    return csvContent;
  }

  /**
   * Update a product by ID
   * @param id Product ID
   * @param updateProductDto Updated product data
   * @param userId User ID
   * @returns Updated product
   */
  async update(
    id: number,
    updateProductDto: UpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    this.logger.log(`Updating product with ID: ${id} for user: ${userId}`);
    // Handle status conversion if needed
    if (updateProductDto.status) {
      updateProductDto.status = updateProductDto.status;
    }

    // Find the product
    const product = await this.findOneInternal(id, userId);
    if (!product) {
      throw new NotFoundException(
        `You don't have permission to update Product with ID ${id}`,
      );
    }

    return await executeInTransaction(
      this.dataSource,
      async (manager) => {
        // Extract SKUs to handle them separately
        const skus = updateProductDto.skus;
        delete updateProductDto.skus;

        // Update the product
        manager.merge(Product, product, updateProductDto);
        const updatedProduct = await manager.save(product);

        // Handle SKUs if they are provided
        if (skus && skus.length > 0) {
          await this.processSkusForUpdate(updatedProduct, skus, manager);
        }

        // Return the updated product with its SKUs
        const result = await manager.findOne(Product, {
          where: { id: updatedProduct.id },
          relations: ['skus'],
        });

        if (!result) {
          throw new NotFoundException(
            `Product with ID ${updatedProduct.id} not found after update`,
          );
        }

        return this.mapProductToResponseDto(result);
      },
      this.logger,
    );
  }

  /**
   * Process SKUs for product update
   * @param product Product entity
   * @param skus SKU DTOs to process
   * @param manager Entity manager for transaction
   */
  private async processSkusForUpdate(
    product: Product,
    skus: CreateSkuDto[],
    manager: any,
  ): Promise<void> {
    // Create a set of SKU IDs from the API response for quick lookup
    const apiSkuIds = new Set(skus.map((sku) => sku.idTT));

    // Find SKUs in our database that don't exist in the API response
    const skusToRemove = product.skus.filter((sku) => !apiSkuIds.has(sku.idTT));

    // Remove SKUs that don't exist in the API response
    if (skusToRemove.length > 0) {
      this.logger.log(
        `Removing ${skusToRemove.length} SKUs that don't exist in the API response`,
      );
      for (const skuToRemove of skusToRemove) {
        await manager.remove(skuToRemove);
      }
    }

    // Process each SKU from the API response
    for (const skuDto of skus) {
      // Find the corresponding SKU in our database
      const existingSku = product.skus.find((sku) => sku.idTT === skuDto.idTT);

      if (existingSku) {
        // Update existing SKU
        await this.updateExistingSku(existingSku, skuDto, manager);
      } else {
        // Create new SKU
        await this.createNewSku(product.id, skuDto, manager);
      }
    }
  }

  /**
   * Update an existing SKU
   * @param existingSku Existing SKU entity
   * @param skuDto Updated SKU data
   * @param manager Entity manager for transaction
   */
  private async updateExistingSku(
    existingSku: Sku,
    skuDto: CreateSkuDto,
    manager: any,
  ): Promise<void> {
    // Update basic properties
    existingSku.sellerSku = skuDto.sellerSku;
    existingSku.price = skuDto.price;
    existingSku.inventory = skuDto.inventory;
    existingSku.listPrice = skuDto.listPrice || null;

    // Update additional properties if they exist in the DTO
    if (skuDto.externalListPrices)
      existingSku.externalListPrices = skuDto.externalListPrices;
    if (skuDto.salesAttributes)
      existingSku.salesAttributes = skuDto.salesAttributes;
    if (skuDto.externalSkuId) existingSku.externalSkuId = skuDto.externalSkuId;
    if (skuDto.combinedSkus) existingSku.combinedSkus = skuDto.combinedSkus;
    if (skuDto.globalListingPolicy)
      existingSku.globalListingPolicy = skuDto.globalListingPolicy;
    if (skuDto.skuUnitCount) existingSku.skuUnitCount = skuDto.skuUnitCount;
    if (skuDto.externalUrls) existingSku.externalUrls = skuDto.externalUrls;
    if (skuDto.preSale) existingSku.preSale = skuDto.preSale;
    if (skuDto.identifierCode)
      existingSku.identifierCode = skuDto.identifierCode;
    if (skuDto.extraIdentifierCodes)
      existingSku.extraIdentifierCodes = skuDto.extraIdentifierCodes;

    // Save the updated SKU
    await manager.save(existingSku);
  }

  /**
   * Create a new SKU
   * @param productId Product ID
   * @param skuDto SKU data
   * @param manager Entity manager for transaction
   */
  private async createNewSku(
    productId: number,
    skuDto: CreateSkuDto,
    manager: any,
  ): Promise<void> {
    // Create new SKU
    const newSku = new Sku();
    newSku.idTT = skuDto.idTT;
    newSku.sellerSku = skuDto.sellerSku;
    newSku.price = skuDto.price;
    newSku.inventory = skuDto.inventory || [];
    newSku.listPrice = skuDto.listPrice || null;
    newSku.productId = productId;

    // Set additional properties if they exist in the DTO
    if (skuDto.externalListPrices)
      newSku.externalListPrices = skuDto.externalListPrices;
    if (skuDto.salesAttributes) newSku.salesAttributes = skuDto.salesAttributes;
    if (skuDto.externalSkuId) newSku.externalSkuId = skuDto.externalSkuId;
    if (skuDto.combinedSkus) newSku.combinedSkus = skuDto.combinedSkus;
    if (skuDto.globalListingPolicy)
      newSku.globalListingPolicy = skuDto.globalListingPolicy;
    if (skuDto.skuUnitCount) newSku.skuUnitCount = skuDto.skuUnitCount;
    if (skuDto.externalUrls) newSku.externalUrls = skuDto.externalUrls;
    if (skuDto.preSale) newSku.preSale = skuDto.preSale;
    if (skuDto.identifierCode) newSku.identifierCode = skuDto.identifierCode;
    if (skuDto.extraIdentifierCodes)
      newSku.extraIdentifierCodes = skuDto.extraIdentifierCodes;

    // Save the new SKU
    await manager.save(newSku);
  }

  async remove(id: number): Promise<void> {
    const result = await this.productRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
  }

  async synchronize_tiktok(
    tiktokShopId: number,
    userId: number,
  ): Promise<{ created: number; updated: number }> {
    this.logger.log(
      `Synchronizing products for TikTok Shop ID: ${tiktokShopId} for user: ${userId}`,
    );
    // Find the TikTokShop by ID
    const tiktokShop =
      await this.tikTokShopService.findTikTokShopById(tiktokShopId);

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }

    // Verify that the TikTokShop belongs to the user
    if (tiktokShop.userId !== userId) {
      throw new NotFoundException(
        `You don't have access to TikTok Shop with ID ${tiktokShopId}`,
      );
    }

    // Create client using the app_key from the TikTokShop
    const client = await this.clientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    try {
      let pageToken: string | undefined = undefined;
      let totalProcessed = 0;
      let createdCount = 0;
      let updatedCount = 0;
      let hasMoreProducts = true;
      const maxProductsToProcess = 500; // Limit to avoid memory issues

      while (hasMoreProducts && totalProcessed < maxProductsToProcess) {
        // Call the TikTok Shop API to search for products
        const result = await client.api.ProductV202502Api.ProductsSearchPost(
          100, // page size - get up to 100 products at once
          tiktokShop.access_token,
          'application/json',
          pageToken, // page token for pagination
          tiktokShop.cipher, // shop cipher
          {}, // empty request body
        );

        // Process the products data
        if (
          result.body?.data?.products &&
          result.body.data.products.length > 0
        ) {
          this.logger.log(
            `Processing ${result.body.data.products.length} products from TikTok Shop`,
          );
          // Process each product
          for (const productData of result.body.data.products) {
            try {
              // Transform TikTok product data to our format
              const productDto = this.processProductData(
                productData,
                tiktokShopId,
              );

              // Add userId to the product data
              productDto.userId = userId;

              // Save or update the product
              const saveResult = await this.saveProduct(productDto);
              const savedProduct = saveResult.product;

              // Track created/updated count
              if (saveResult.isNew) {
                createdCount++;
              } else {
                updatedCount++;
              }

              // Call the TikTok Shop API to get product details
              try {
                // Reuse the existing client instead of creating a new one
                // Call the TikTok Shop API to get detailed product information
                const detailResult =
                  await client.api.ProductV202309Api.ProductsProductIdGet(
                    productDto.idTT, // TikTok Shop product ID
                    tiktokShop.access_token,
                    'application/json',
                    false, // returnUnderReviewVersion
                    tiktokShop.cipher, // shop cipher
                  );

                if (detailResult.body?.data) {
                  this.logger.log(
                    `Getting detailed information for product ${productDto.idTT}`,
                  );

                  // Create UpdateProductDto from the detailed product data
                  const updateProductDto =
                    this.tikTokProductMapper.mapToUpdateProductDto(
                      detailResult.body.data,
                      tiktokShopId,
                    );

                  // Update the product with detailed information
                  await this.update(savedProduct.id, updateProductDto, userId);
                }
              } catch (detailError) {
                this.logger.error(
                  `Error getting detailed information for product ${productDto.idTT}: ${detailError.message}`,
                );
                // Continue with next product even if detail fetch fails
              }
              totalProcessed++;
            } catch (productError) {
              this.logger.error(
                `Error processing product ${productData.id}: ${productError.message}`,
              );
              // Continue with next product even if one fails
            }
          }

          // Check if there are more products to fetch
          // Handle both camelCase and snake_case property names in the API response
          const nextPageToken =
            result.body.data.nextPageToken ||
            (result.body.data as any).next_page_token;
          if (nextPageToken) {
            pageToken = nextPageToken;
          } else {
            hasMoreProducts = false;
          }
        } else {
          this.logger.log('No products found from TikTok Shop');
          hasMoreProducts = false;
        }
      }

      this.logger.log(
        `Successfully processed ${totalProcessed} products from TikTok Shop. Created: ${createdCount}, Updated: ${updatedCount}`,
      );

      return { created: createdCount, updated: updatedCount };
    } catch (error) {
      this.logger.error(
        'Error synchronizing products from TikTok Shop:',
        error,
      );
      throw new Error(`Failed to synchronize products: ${error.message}`);
    }
  }

  /**
   * Process TikTok Shop product data into our internal format
   */
  private processProductData(
    productData: any,
    tiktokShopId: number,
  ): CreateProductDto {
    // Use the mapper to transform the product data
    return this.tikTokProductMapper.mapToCreateProductDto(
      productData,
      tiktokShopId,
    );
    // Skip processing SKUs at this step as we'll get full data from ProductV202309Api.ProductsProductIdGet
  }

  // The processSkuData method has been replaced by TikTokProductMapper.mapToCreateSkuDto

  // The mapProductStatus method has been replaced by TikTokProductMapper.mapProductStatus

  /**
   * Save or update a product and its SKUs
   */
  private async saveProduct(productDto: CreateProductDto): Promise<{ product: Product; isNew: boolean }> {
    // Get the query runner to manage transactions
    const queryRunner =
      this.productRepository.manager.connection.createQueryRunner();

    // Start transaction
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if product already exists based on TikTok Shop ID
      const existingProduct = await queryRunner.manager.findOne(Product, {
        where: { idTT: productDto.idTT },
        relations: ['skus'],
      });

      if (existingProduct) {
        this.logger.log(
          `Updating existing product with idTT: ${productDto.idTT}`,
        );

        // Update product fields individually to avoid circular references
        existingProduct.idTT = productDto.idTT;
        existingProduct.title = productDto.title;
        existingProduct.status = productDto.status;
        existingProduct.tiktokShopId = productDto.tiktokShopId;
        if (productDto.userId) existingProduct.userId = productDto.userId; // Update userId if available

        // Update optional fields
        if (productDto.salesRegions)
          existingProduct.salesRegions = productDto.salesRegions;
        if (productDto.productSyncFailReasons)
          existingProduct.productSyncFailReasons =
            productDto.productSyncFailReasons;
        if (productDto.isNotForSale !== undefined)
          existingProduct.isNotForSale = productDto.isNotForSale;
        if (productDto.listingQualityTier !== undefined)
          existingProduct.listingQualityTier = productDto.listingQualityTier;
        if (productDto.recommendedCategories)
          existingProduct.recommendedCategories =
            productDto.recommendedCategories;
        if (productDto.integratedPlatformStatuses)
          existingProduct.integratedPlatformStatuses =
            productDto.integratedPlatformStatuses;
        if (productDto.audit) existingProduct.audit = productDto.audit;

        // Update create and update time from TikTok Shop
        if (productDto.createTimeTT)
          existingProduct.createTimeTT = productDto.createTimeTT;
        if (productDto.updateTimeTT)
          existingProduct.updateTimeTT = productDto.updateTimeTT;

        // Optional fields that might be in the entity but not in the DTO
        if (productDto.category) existingProduct.category = productDto.category;
        if (productDto.brandInfo)
          existingProduct.brandInfo = productDto.brandInfo;
        if (productDto.description)
          existingProduct.description = productDto.description;
        if (productDto.productImages)
          existingProduct.productImages = productDto.productImages;
        if (productDto.packageLength)
          existingProduct.packageLength = productDto.packageLength;
        if (productDto.packageWidth)
          existingProduct.packageWidth = productDto.packageWidth;
        if (productDto.packageHeight)
          existingProduct.packageHeight = productDto.packageHeight;

        // Save the updated product first
        const updatedProduct = await queryRunner.manager.save(existingProduct);

        // Skip handling SKUs at this step as we'll get full data from ProductV202309Api.ProductsProductIdGet

        // Commit transaction
        await queryRunner.commitTransaction();

        // Return the updated product with its SKUs
        // Use a direct query instead of this.findOne to avoid circular references
        const result = await queryRunner.manager.findOne(Product, {
          where: { id: updatedProduct.id },
          relations: ['skus'],
        });

        if (!result) {
          throw new Error(
            `Product with ID ${updatedProduct.id} not found after update`,
          );
        }

        return { product: result, isNew: false };
      } else {
        this.logger.log(`Creating new product with idTT: ${productDto.idTT}`);

        // Create new product without SKUs first
        const newProduct = new Product();

        // Set required fields
        newProduct.idTT = productDto.idTT;
        newProduct.title = productDto.title;
        newProduct.status = productDto.status;
        newProduct.tiktokShopId = productDto.tiktokShopId;
        if (productDto.userId) newProduct.userId = productDto.userId; // Set userId from DTO if available
        newProduct.skus = []; // Initialize with empty array

        // Set optional fields
        if (productDto.salesRegions)
          newProduct.salesRegions = productDto.salesRegions;
        if (productDto.productSyncFailReasons)
          newProduct.productSyncFailReasons = productDto.productSyncFailReasons;
        if (productDto.isNotForSale !== undefined)
          newProduct.isNotForSale = productDto.isNotForSale;
        if (productDto.listingQualityTier !== undefined)
          newProduct.listingQualityTier = productDto.listingQualityTier;
        if (productDto.recommendedCategories)
          newProduct.recommendedCategories = productDto.recommendedCategories;
        if (productDto.integratedPlatformStatuses)
          newProduct.integratedPlatformStatuses =
            productDto.integratedPlatformStatuses;
        if (productDto.audit) newProduct.audit = productDto.audit;

        // Set create and update time from TikTok Shop
        if (productDto.createTimeTT)
          newProduct.createTimeTT = productDto.createTimeTT;
        if (productDto.updateTimeTT)
          newProduct.updateTimeTT = productDto.updateTimeTT;

        // Optional fields that might be in the entity but not in the DTO
        if (productDto.category) newProduct.category = productDto.category;
        if (productDto.brandInfo) newProduct.brandInfo = productDto.brandInfo;
        if (productDto.description)
          newProduct.description = productDto.description;
        if (productDto.productImages)
          newProduct.productImages = productDto.productImages;
        if (productDto.packageLength)
          newProduct.packageLength = productDto.packageLength;
        if (productDto.packageWidth)
          newProduct.packageWidth = productDto.packageWidth;
        if (productDto.packageHeight)
          newProduct.packageHeight = productDto.packageHeight;

        // Save the product first to get an ID
        const savedProduct = await queryRunner.manager.save(newProduct);

        // Skip creating and associating SKUs at this step as we'll get full data from ProductV202309Api.ProductsProductIdGet

        // Commit transaction
        await queryRunner.commitTransaction();

        // Return the saved product with its SKUs
        // Use a direct query instead of this.findOne to avoid circular references
        const result = await queryRunner.manager.findOne(Product, {
          where: { id: savedProduct.id },
          relations: ['skus'],
        });

        if (!result) {
          throw new Error(
            `Product with ID ${savedProduct.id} not found after save`,
          );
        }

        return { product: result, isNew: true };
      }
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error saving product with idTT ${productDto.idTT}: ${error.message}`,
      );
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  // The findTikTokShop method has been replaced by TikTokShopService.findTikTokShopById

  // The createUpdateProductDtoFromDetailResponse method has been replaced by TikTokProductMapper.mapToUpdateProductDto

  /**
   * Synchronize detailed product information from TikTok Shop
   * @param id Product ID from internal system
   * @param userId User ID
   */
  async synchronize_detail_tiktok(
    id: number,
    userId: number,
  ): Promise<ProductResponseDto> {
    this.logger.log(
      `Synchronizing product details for product ID: ${id} for user: ${userId}`,
    );
    // Find the product by ID
    const product = await this.findOneInternal(id, userId);

    if (!product || !product.idTT) {
      throw new NotFoundException(
        `Product with ID ${id} not found or missing TikTok Shop ID`,
      );
    }

    // Find the TikTokShop by ID
    const tiktokShop = await this.tikTokShopService.findTikTokShopById(
      product.tiktokShopId,
    );

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${product.tiktokShopId} not found`,
      );
    }

    // Create client using the app_key from the TikTokShop
    const client = await this.clientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    try {
      // Call the TikTok Shop API to get product details
      const result = await client.api.ProductV202309Api.ProductsProductIdGet(
        product.idTT, // TikTok Shop product ID
        tiktokShop.access_token,
        'application/json',
        false, // returnUnderReviewVersion
        tiktokShop.cipher, // shop cipher
      );

      // Process the product data
      if (result.body?.data) {
        this.logger.log(
          `Processing detailed information for product ${product.idTT}`,
        );

        // Use the mapper to create UpdateProductDto from the detailed product data
        const updateProductDto = this.tikTokProductMapper.mapToUpdateProductDto(
          result.body.data,
          product.tiktokShopId,
        );

        // Update the product with detailed information using the update method
        // This will handle SKUs properly (create new ones, update existing ones, remove old ones)
        return await this.update(id, updateProductDto, userId);
      } else {
        this.logger.warn(
          `No detailed information found for product ${product.idTT}`,
        );
        return this.mapProductToResponseDto(product);
      }
    } catch (error: any) {
      this.logger.error(
        `Error synchronizing product details from TikTok Shop: ${error.message}`,
      );
      throw new Error(
        `Failed to synchronize product details: ${error.message}`,
      );
    }
  }
}
