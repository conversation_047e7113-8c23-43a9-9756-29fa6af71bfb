import { Process, Processor } from '@nestjs/bull';
import {
  Logger,
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { TikTokShopService } from 'src/tiktok-shop/tiktok-shop.service';
import { Product, ProductStatus } from 'src/products/entities/product.entity';
import { StagedProduct } from 'src/products/entities/staged-product.entity';
import {
  ProductUpload,
  ProductUploadStatus,
} from 'src/products/entities/product-upload.entity';
import { Category } from 'src/products/entities/category.entity';
import { Brand } from 'src/products/entities/brand.entity';
import { ImageUseCase } from 'src/products/dto/upload-image.dto';
import { ProductsService } from 'src/products/products.service';
import { ImageUploadService } from 'src/products/services/image-upload.service';
import {
  ProductValidationResultDto,
  ValidationErrorDto,
} from 'src/products/dto/product-validation.dto';
import { CloudStorageService } from 'src/common/cloud-storage/cloud-storage.service';
import { WarehouseResponseDto } from 'src/tiktok-shop/dto/warehouse-response.dto';

@Injectable()
@Processor('product-creation')
export class ProductUploadQueueProcessor {
  private readonly logger = new Logger(ProductUploadQueueProcessor.name);

  constructor(
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    @InjectRepository(StagedProduct)
    private readonly stagedProductRepository: Repository<StagedProduct>,
    @InjectRepository(ProductUpload)
    private readonly productUploadRepository: Repository<ProductUpload>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    private readonly tikTokClientFactory: TikTokClientFactory,
    private readonly tikTokShopService: TikTokShopService,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => ProductsService))
    private readonly productsService: ProductsService,
    private readonly imageUploadService: ImageUploadService,
    private readonly cloudStorageService: CloudStorageService,
  ) {}

  /**
   * Transform categoryId and brandId from internal database IDs to TikTok IDs (idTT)
   * @param categoryId Internal category ID (categories.id)
   * @param brandId Internal brand ID (brands.id)
   * @returns Object containing TikTok category and brand IDs
   */
  private async transformToTikTokIds(
    categoryId: number,
    brandId: number,
  ): Promise<{ categoryIdTT: string; brandIdTT: string }> {
    this.logger.log(
      `Transforming internal IDs to TikTok IDs - categoryId: ${categoryId}, brandId: ${brandId}`,
    );

    // Fetch category and brand in parallel for better performance
    const [category, brand] = await Promise.all([
      this.categoryRepository.findOne({
        where: { id: categoryId },
        select: ['id', 'idTT', 'localName'],
      }),
      this.brandRepository.findOne({
        where: { id: brandId },
        select: ['id', 'idTT', 'name'],
      }),
    ]);

    // Validate that category exists
    if (!category) {
      throw new NotFoundException(
        `Category with ID ${categoryId} not found in database. Please ensure categories are synchronized.`,
      );
    }

    // Validate that brand exists
    if (!brand) {
      throw new NotFoundException(
        `Brand with ID ${brandId} not found in database. Please ensure brands are synchronized.`,
      );
    }

    this.logger.log(
      `Successfully transformed IDs - Category: ${category.localName} (${category.id} → ${category.idTT}), Brand: ${brand.name} (${brand.id} → ${brand.idTT})`,
    );

    return {
      categoryIdTT: category.idTT,
      brandIdTT: brand.idTT,
    };
  }

  /**
   * Process a product upload job
   * @param job The Bull job
   * @returns Job result
   */
  @Process('create-product')
  async handleCreateProduct(job: Job) {
    this.logger.log(`Processing job ${job.id} of type ${job.name}`);
    this.logger.debug('Job data:', job.data);

    try {
      // Extract job data
      const {
        productUploadId,
        stagedProductId,
        tiktokShopId,
        userId,
        options,
      } = job.data;
      const skipValidation = options?.skipValidation || true;
      const forceUpload = options?.forceUpload || true;

      if (!productUploadId || !stagedProductId || !tiktokShopId) {
        throw new BadRequestException(
          'Missing required job data: productUploadId, stagedProductId, or tiktokShopId',
        );
      }

      // Log userId if available
      if (userId) {
        this.logger.log(`Processing job for user: ${userId}`);
      }

      // Get product upload
      const productUpload = await this.productUploadRepository.findOne({
        where: { id: productUploadId },
      });
      if (!productUpload) {
        throw new NotFoundException(
          `Product upload with ID ${productUploadId} not found`,
        );
      }

      // Get staged product
      const stagedProduct = await this.stagedProductRepository.findOne({
        where: { id: stagedProductId },
      });
      if (!stagedProduct) {
        throw new NotFoundException(
          `Staged product with ID ${stagedProductId} not found`,
        );
      }

      // Get TikTok Shop
      const tiktokShop = await this.tikTokShopRepository.findOne({
        where: { id: tiktokShopId },
      });
      if (!tiktokShop) {
        throw new NotFoundException(
          `TikTok Shop with ID ${tiktokShopId} not found`,
        );
      }

      // Update product upload status
      productUpload.status = ProductUploadStatus.IN_PROGRESS;
      await this.productUploadRepository.save(productUpload);

      // Initialize progress
      await job.progress(10);
      this.logger.log('Product upload job: 10% - Initialized');

      // Step 1: Process images with retry logic
      await this.processImages(stagedProduct, tiktokShop, productUpload);

      // Update progress
      await job.progress(40);
      this.logger.log('Product upload job: 40% - Images processed');

      // Update product upload progress
      productUpload.progress = 40;
      await this.productUploadRepository.save(productUpload);

      // Step 2: Prepare product data for TikTok API (do this ONCE)
      this.logger.log('Preparing product data for TikTok API');
      const tikTokProductData = await this.prepareTikTokProductData(
        stagedProduct,
        productUpload,
        tiktokShopId,
      );

      // Update progress
      await job.progress(50);
      this.logger.log('Product upload job: 50% - Product data prepared');

      // Step 3: Validate product if not skipping validation
      let validationResult: ProductValidationResultDto | null = null;
      if (!skipValidation) {
        validationResult = await this.checkProductListing(
          tikTokProductData,
          stagedProduct,
          tiktokShop,
        );

        // Update progress
        await job.progress(60);
        this.logger.log('Product upload job: 60% - Product validated');

        // Update product upload progress
        productUpload.progress = 60;
        await this.productUploadRepository.save(productUpload);

        // If validation fails and not forcing upload, update status and return
        if (validationResult && !validationResult.isValid && !forceUpload) {
          productUpload.status = ProductUploadStatus.FAILED;
          productUpload.errorMessage = 'Product validation failed';
          productUpload.progress = 100;
          await this.productUploadRepository.save(productUpload);

          return {
            success: false,
            message: 'Product validation failed',
            validationErrors: validationResult?.errors || [],
            validationWarnings: validationResult?.warnings || [],
          };
        }
      }

      // Create TikTok client
      const client = await this.tikTokClientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Update progress
      await job.progress(70);
      this.logger.log('Product upload job: 70% - Ready to create product');

      // Update product upload progress
      productUpload.progress = 70;
      await this.productUploadRepository.save(productUpload);

      // Create product in TikTok Shop
      const result = await client.api.ProductV202309Api.ProductsPost(
        '', // productId - empty for new products
        tiktokShop.access_token,
        'application/json',
        tiktokShop.cipher,
        tikTokProductData,
        { headers: {} },
      );

      // Check if the request was successful
      if (result.body?.code !== 0 || !result.body?.data?.productId) {
        throw new BadRequestException(
          `Failed to create product: ${result.body?.message || 'Unknown error'}`,
        );
      }

      // Update progress
      await job.progress(80);
      this.logger.log(
        'Product upload job: 80% - Product created in TikTok Shop',
      );

      // Update product upload progress
      productUpload.progress = 80;
      await this.productUploadRepository.save(productUpload);

      // Save minimal product to database
      let savedProduct = await this.saveProductToDatabase(
        result.body.data.productId,
        stagedProduct,
        tiktokShopId,
      );

      // Update progress
      await job.progress(90);
      this.logger.log(
        'Product upload job: 90% - Minimal product saved to database',
      );

      // Synchronize detailed product information from TikTok Shop
      try {
        this.logger.log(
          `Synchronizing detailed product information for product ID: ${savedProduct.id}`,
        );

        // Get userId from job data or from stagedProduct if not provided in job data
        let userIdForSync = userId;
        if (!userIdForSync) {
          // If userId is not provided in job data, try to get it from stagedProduct
          this.logger.log(
            'userId not provided in job data, trying to get it from stagedProduct',
          );
          userIdForSync = stagedProduct.userId;

          if (!userIdForSync) {
            // If still no userId, try to get it from tiktokShop
            this.logger.log(
              'userId not found in stagedProduct, trying to get it from tiktokShop',
            );
            userIdForSync = tiktokShop.userId;
          }
        }

        if (!userIdForSync) {
          this.logger.warn(
            'No userId found for synchronization, this may cause issues with data access control',
          );
        } else {
          this.logger.log(`Using userId: ${userIdForSync} for synchronization`);
        }

        // Call synchronize_detail_tiktok with the product ID and userId
        const syncedProduct = await this.productsService.synchronize_detail_tiktok(
          savedProduct.id,
          userIdForSync,
        );
        this.logger.log(
          `Successfully synchronized detailed product information for product ID: ${syncedProduct.id}`,
        );
      } catch (syncError) {
        this.logger.error(
          `Error synchronizing product details: ${syncError.message}`,
          syncError.stack,
        );
        // Continue even if synchronization fails, as the basic product data is already saved
        // But log a warning that the product data is incomplete
        this.logger.warn(
          `Product ID: ${savedProduct.id} has incomplete data due to synchronization failure`,
        );
      }

      // Update progress
      await job.progress(100);
      this.logger.log('Product upload job: 100% - Completed');

      // Update product upload status
      productUpload.status = ProductUploadStatus.COMPLETED;
      productUpload.tiktokProductId = result.body.data.productId;
      productUpload.progress = 100;
      productUpload.completedAt = new Date();
      // Add reference to the saved product
      productUpload.productId = savedProduct.id;
      await this.productUploadRepository.save(productUpload);

      return {
        success: true,
        message: 'Product upload job completed successfully',
        jobId: job.id,
        productId: result.body.data.productId,
        internalProductId: savedProduct.id,
      };
    } catch (error) {
      this.logger.error(`Error processing job ${job.id}:`, error);

      // Update job progress to indicate failure
      await job.progress(100);

      // Update product upload status
      try {
        const { productUploadId } = job.data;
        if (productUploadId) {
          const productUpload = await this.productUploadRepository.findOne({
            where: { id: productUploadId },
          });
          if (productUpload) {
            productUpload.status = ProductUploadStatus.FAILED;
            productUpload.errorMessage =
              error instanceof Error ? error.message : 'Unknown error occurred';
            productUpload.progress = 100;
            await this.productUploadRepository.save(productUpload);
          }
        }
      } catch (updateError) {
        this.logger.error(
          `Error updating product upload status: ${updateError.message}`,
          updateError.stack,
        );
      }

      // Return error information instead of throwing
      // This allows the job to be marked as completed with error information
      return {
        success: false,
        message:
          error instanceof Error ? error.message : 'Unknown error occurred',
        error: error instanceof Error ? error.stack : String(error),
        jobId: job.id,
      };
    }
  }

  /**
   * Prepare product data for TikTok API
   * @param stagedProduct Staged product data
   * @param productUpload Product upload data containing processed images and size chart
   * @param tiktokShopId TikTok Shop ID for warehouse retrieval
   * @returns TikTok API product data
   */
  private async prepareTikTokProductData(
    stagedProduct: StagedProduct,
    productUpload: ProductUpload,
    tiktokShopId: number,
  ) {
    // Process size chart if provided in productUpload
    let sizeChart: any = undefined;
    if (productUpload.sizeChart?.uri) {
      sizeChart = {
        image: {
          uri: productUpload.sizeChart.uri,
        },
      };
    }

    // Get default warehouse for the TikTok shop
    let defaultWarehouse: WarehouseResponseDto;
    try {
      defaultWarehouse = await this.tikTokShopService.getDefaultWarehouse(tiktokShopId);
      this.logger.log(`Using default warehouse: ${defaultWarehouse.idTT} (${defaultWarehouse.name}) for TikTok Shop ID: ${tiktokShopId}`);
    } catch (error) {
      this.logger.error(`Failed to get default warehouse for TikTok Shop ID: ${tiktokShopId}: ${error.message}`);
      throw new BadRequestException(`No default warehouse found for TikTok Shop. Please ensure warehouses are synchronized.`);
    }

    // Create SKUs
    const skus = stagedProduct.skus.map((sku) => {
      // Create sales attributes
      const salesAttributes = sku.salesAttributes.map((attr) => {
        const salesAttr: any = {
          name: attr.name,
          valueName: attr.valueName,
        };

        // Add skuImg if provided - map from processedSkuImages
        if (attr.skuImg && (attr.skuImg.r2Key || attr.skuImg.imageUrl)) {
          // Find the corresponding uploaded image from processedSkuImages
          const uploadedSkuImage = productUpload.processedSkuImages?.find(
            (processedImg) =>
              (attr.skuImg?.r2Key && processedImg.r2Key === attr.skuImg.r2Key) ||
              (attr.skuImg?.imageUrl && processedImg.imageUrl === attr.skuImg.imageUrl)
          );

          if (uploadedSkuImage?.uri) {
            salesAttr.skuImg = {
              uri: uploadedSkuImage.uri,
            };
          } else {
            this.logger.warn(
              `SKU image not found in processedSkuImages for r2Key: ${attr.skuImg?.r2Key} or imageUrl: ${attr.skuImg?.imageUrl}`
            );
          }
        }

        // Add supplementarySkuImages if provided
        if (
          attr.supplementarySkuImages &&
          attr.supplementarySkuImages.length > 0
        ) {
          salesAttr.supplementarySkuImages = attr.supplementarySkuImages.map(
            (img) => ({
              uri: img.uri,
            }),
          );
        }

        return salesAttr;
      });

      // Create inventory items from the inventory array
      // Use the default warehouse ID from the database instead of placeholder
      const inventory = sku.inventory.map((inv) => ({
        quantity: inv.quantity,
        warehouseId: defaultWarehouse.idTT, // Use warehouse ID from database
      }));

      // Create the SKU object
      const skuObj: any = {
        salesAttributes,
        price: {
          amount: sku.price.amount.toString(),
          currency: sku.price.currency,
        },
        inventory,
      };

      // Add listPrice if provided
      if (sku.listPrice) {
        skuObj.listPrice = {
          amount: sku.listPrice.amount.toString(),
          currency: sku.listPrice.currency,
        };
      }

      // Add identifierCode if provided
      if (sku.identifierCode) {
        skuObj.identifierCode = {
          code: sku.identifierCode.code,
          type: sku.identifierCode.type,
        };
      }

      // Add sellerSku if provided
      if (sku.sellerSku) {
        skuObj.sellerSku = sku.sellerSku;
      }

      return skuObj;
    });

    // Transform categoryId and brandId from internal database IDs to TikTok IDs
    // StagedProduct stores internal IDs (categories.id, brands.id), but TikTok API needs idTT
    const { categoryIdTT, brandIdTT } = await this.transformToTikTokIds(
      stagedProduct.categoryId,
      stagedProduct.brandId,
    );

    // Create the final product data
    return {
      title: stagedProduct.title,
      description: stagedProduct.description,
      categoryVersion: stagedProduct.categoryVersion || 'v2', // Default to v2 for US market
      categoryId: categoryIdTT, // Use TikTok category ID
      brandId: brandIdTT, // Use TikTok brand ID
      mainImages: productUpload.processedImages,
      sizeChart,
      packageDimensions: stagedProduct.packageDimensions,
      packageWeight: stagedProduct.packageWeight,
      productAttributes: stagedProduct.productAttributes,
      skus,
    };
  }

  /**
   * Save minimal product to database
   * @param tikTokProductId TikTok product ID
   * @param stagedProduct Staged product data (used for userId and reference)
   * @param tiktokShopId TikTok Shop ID
   * @returns Saved product with minimal data
   */
  private async saveProductToDatabase(
    tikTokProductId: string,
    stagedProduct: StagedProduct,
    tiktokShopId: number,
  ) {
    // Get TikTok Shop
    const tiktokShop = await this.tikTokShopRepository.findOne({
      where: { id: tiktokShopId },
    });
    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }
    this.logger.log(`Saving minimal product ${tikTokProductId} to database`);

    // Use a transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create minimal product entity with only essential fields
      // The rest will be populated by synchronize_detail_tiktok
      const product = new Product();
      product.idTT = tikTokProductId;
      product.tiktokShopId = tiktokShopId;
      product.status = ProductStatus.ACTIVATE; // Default status

      // Set a temporary title until synchronization completes
      product.title = `Product ${tikTokProductId} (Synchronizing...)`;

      // Set userId from stagedProduct if available, otherwise from tiktokShop
      if (stagedProduct.userId) {
        product.userId = stagedProduct.userId;
        this.logger.log(
          `Setting userId: ${stagedProduct.userId} from stagedProduct`,
        );
      } else if (tiktokShop.userId) {
        product.userId = tiktokShop.userId;
        this.logger.log(`Setting userId: ${tiktokShop.userId} from tiktokShop`);
      } else {
        this.logger.warn('No userId found in stagedProduct or tiktokShop');
      }

      // Save minimal product
      const savedProduct = await queryRunner.manager.save(product);

      // Commit transaction
      await queryRunner.commitTransaction();

      this.logger.log(
        `Saved minimal product with ID: ${savedProduct.id}, TikTok ID: ${tikTokProductId}, userId: ${savedProduct.userId || 'not set'}`,
      );
      return savedProduct;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error saving product to database: ${error.message}`,
        error.stack,
      );
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Process images with retry logic
   * @param stagedProduct Staged product
   * @param tiktokShop TikTok shop
   * @param productUpload ProductUpload entity to store results
   * @returns Processed staged product
   */
  private async processImages(
    stagedProduct: StagedProduct,
    tiktokShop: TikTokShop,
    productUpload: ProductUpload,
  ): Promise<StagedProduct> {
    this.logger.log(
      `Processing images for staged product ID: ${stagedProduct.id}`,
    );

    const MAX_RETRIES = 3;
    const processedImages: Array<{
      uri: string;
      url?: string;
      tiktokUrl?: string;
      width?: number;
      height?: number;
      useCase?: string;
    }> = [];

    // Process main images
    for (const image of stagedProduct.images) {
      // If image has r2Key or imageUrl, upload it with retry logic
      if (image.r2Key || image.imageUrl) {
        let retries = 0;
        let success = false;
        let uploadResult: {
          uri: string;
          url: string;
          width: number;
          height: number;
          useCase: string;
        } | null = null;

        while (retries < MAX_RETRIES && !success) {
          try {
            uploadResult = await this.imageUploadService.uploadImage(
              tiktokShop.id,
              {
                imageUrl: image.r2Key
                  ? `${this.cloudStorageService.getPublicUrl()}/${image.r2Key}`
                  : image.imageUrl,
                useCase: ImageUseCase.MAIN_IMAGE, // Default to MAIN_IMAGE
              },
            );

            success = true;
          } catch (error) {
            retries++;
            this.logger.warn(
              `Image upload failed, retry ${retries}/${MAX_RETRIES}: ${error.message}`,
            );

            // Exponential backoff
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * Math.pow(2, retries - 1)),
            );

            if (retries >= MAX_RETRIES) {
              throw new Error(
                `Failed to upload image after ${MAX_RETRIES} attempts: ${error.message}`,
              );
            }
          }
        }

        if (uploadResult) {
          processedImages.push({
            uri: uploadResult.uri,
            url: image.imageUrl || '',
            tiktokUrl: uploadResult.url,
            width: uploadResult.width,
            height: uploadResult.height,
            useCase: uploadResult.useCase,
          });
        } else {
          this.logger.error('Upload result is null after successful upload');
          throw new Error('Upload result is null after successful upload');
        }
      } else {
        this.logger.warn(`Skipping image without URI, imageData, or imageUrl`);
        // Don't add this image to processedImages since it has no usable content
      }
    }

    // Process SKU images with deduplication
    const processedSkuImages: Array<{
      imageUrl?: string;
      r2Key?: string;
      uri: string;
      tiktokUrl?: string;
      width?: number;
      height?: number;
      useCase?: string;
    }> = [];

    // Collect all unique SKU images for deduplication
    const uniqueSkuImages = new Map<string, {
      imageUrl?: string;
      r2Key?: string;
      originalSkuImg: any;
    }>();

    // Gather all SKU images and deduplicate by imageUrl or r2Key
    for (const sku of stagedProduct.skus) {
      for (const attr of sku.salesAttributes) {
        if (attr.skuImg && (attr.skuImg.r2Key || attr.skuImg.imageUrl)) {
          const key = attr.skuImg.r2Key || attr.skuImg.imageUrl;
          if (key && !uniqueSkuImages.has(key)) {
            uniqueSkuImages.set(key, {
              imageUrl: attr.skuImg.imageUrl,
              r2Key: attr.skuImg.r2Key,
              originalSkuImg: attr.skuImg,
            });
          }
        }
      }
    }

    // Upload each unique SKU image
    for (const [, skuImageData] of uniqueSkuImages) {
      let retries = 0;
      let success = false;
      let uploadResult: {
        uri: string;
        url: string;
        width: number;
        height: number;
        useCase: string;
      } | null = null;

      while (retries < MAX_RETRIES && !success) {
        try {
          uploadResult = await this.imageUploadService.uploadImage(
            tiktokShop.id,
            {
              imageUrl: skuImageData.r2Key
                ? `${this.cloudStorageService.getPublicUrl()}/${skuImageData.r2Key}`
                : skuImageData.imageUrl,
              useCase: ImageUseCase.ATTRIBUTE_IMAGE,
            },
          );

          success = true;
        } catch (error) {
          retries++;
          this.logger.warn(
            `SKU image upload failed, retry ${retries}/${MAX_RETRIES}: ${error.message}`,
          );

          // Exponential backoff
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * Math.pow(2, retries - 1)),
          );

          if (retries >= MAX_RETRIES) {
            throw new Error(
              `Failed to upload SKU image after ${MAX_RETRIES} attempts: ${error.message}`,
            );
          }
        }
      }

      if (uploadResult) {
        processedSkuImages.push({
          imageUrl: skuImageData.imageUrl,
          r2Key: skuImageData.r2Key,
          uri: uploadResult.uri,
          tiktokUrl: uploadResult.url,
          width: uploadResult.width,
          height: uploadResult.height,
          useCase: uploadResult.useCase,
        });
      } else {
        this.logger.error('SKU image upload result is null after successful upload');
        throw new Error('SKU image upload result is null after successful upload');
      }
    }

    // Process size chart if provided
    let processedSizeChart:
      | {
          uri: string;
          tiktokUrl?: string;
          width?: number;
          height?: number;
          useCase?: string;
        }
      | undefined = undefined;

    if (stagedProduct.sizeChart) {
      // If size chart has r2Key or imageUrl, upload it with retry logic
      if (stagedProduct.sizeChart.r2Key || stagedProduct.sizeChart.imageUrl) {
        let retries = 0;
        let success = false;
        let uploadResult: {
          uri: string;
          url: string;
          width: number;
          height: number;
          useCase: string;
        } | null = null;

        while (retries < MAX_RETRIES && !success) {
          try {
            uploadResult = await this.imageUploadService.uploadImage(
              tiktokShop.id,
              {
                imageUrl: stagedProduct.sizeChart.r2Key
                  ? `${this.cloudStorageService.getPublicUrl()}/${stagedProduct.sizeChart.r2Key}`
                  : stagedProduct.sizeChart.imageUrl,
                useCase: ImageUseCase.SIZE_CHART_IMAGE,
              },
            );

            success = true;
          } catch (error) {
            retries++;
            this.logger.warn(
              `Size chart upload failed, retry ${retries}/${MAX_RETRIES}: ${error.message}`,
            );

            // Exponential backoff
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * Math.pow(2, retries - 1)),
            );

            if (retries >= MAX_RETRIES) {
              throw new Error(
                `Failed to upload size chart after ${MAX_RETRIES} attempts: ${error.message}`,
              );
            }
          }
        }

        // Store size chart upload result
        if (uploadResult) {
          processedSizeChart = {
            uri: uploadResult.uri,
            tiktokUrl: uploadResult.url,
            width: uploadResult.width,
            height: uploadResult.height,
            useCase: uploadResult.useCase,
          };
        } else {
          this.logger.error('Upload result is null after successful upload');
          throw new Error('Upload result is null after successful upload');
        }
      } else {
        // If size chart has no r2Key or imageUrl, log a warning
        this.logger.warn(`Size chart provided but has no r2Key or imageUrl`);
      }
    }

    // Store processed images, SKU images, and size chart in ProductUpload entity
    productUpload.processedImages = processedImages;
    productUpload.processedSkuImages = processedSkuImages;
    if (processedSizeChart) {
      productUpload.sizeChart = processedSizeChart;
    }
    await this.productUploadRepository.save(productUpload);

    // Clean up any temporary files
    this.cleanupTempFiles(stagedProduct);

    return stagedProduct;
  }

  /**
   * Check product listing with TikTok API
   * @param tikTokProductData Prepared TikTok product data
   * @param stagedProduct Staged product (for logging purposes)
   * @param tiktokShop TikTok shop
   * @returns Validation result
   */
  /**
   * Clean up R2 storage files associated with a staged product
   * @param stagedProduct Staged product entity
   */
  private cleanupTempFiles(stagedProduct: StagedProduct): void {
    // No need to clean up files anymore as we're using R2 storage
    // The files will be kept in R2 for reference
    this.logger.debug(
      `Skipping cleanup for staged product ID: ${stagedProduct.id} as we're using R2 storage`,
    );
  }

  private async checkProductListing(
    tikTokProductData: any,
    stagedProduct: StagedProduct,
    tiktokShop: TikTokShop,
  ): Promise<ProductValidationResultDto> {
    this.logger.log(
      `Checking product listing for staged product ID: ${stagedProduct.id}`,
    );

    // Create TikTok client
    const client = await this.tikTokClientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    // Call validation API
    // Note: The TikTok API documentation might be inconsistent with the actual implementation
    // We're using a direct approach with type assertions to bypass TypeScript checks
    const result = await (
      client.api.ProductV202309Api as any
    ).ProductsListingCheckPost(
      tikTokProductData,
      tiktokShop.access_token,
      tiktokShop.cipher,
      { headers: {} },
    );

    // Initialize result object
    const validationResult: ProductValidationResultDto = {
      isValid: false,
      errors: [],
      warnings: [],
      rawResponse: result.body,
    };

    // Check if the request was successful
    if (result.body?.code === 0) {
      // Process validation results
      // Handle different response formats using a more generic approach
      const responseData = result.body?.data || {};
      const checkResult =
        responseData.checkResult || responseData.check_result || responseData;

      if (checkResult) {
        // Handle different property names
        validationResult.isValid =
          checkResult.isPassed || checkResult.is_passed || false;

        // Process errors and warnings
        if (checkResult.errors && Array.isArray(checkResult.errors)) {
          checkResult.errors.forEach((error: any) => {
            const validationError: ValidationErrorDto = {
              // Handle different property names
              field: error.fieldName || error.field_name || '',
              message: error.message || '',
              code: error.errorCode || error.error_code || '',
              level: error.level === 'ERROR' ? 'ERROR' : 'WARNING',
            };

            if (validationError.level === 'ERROR') {
              validationResult.errors.push(validationError);
            } else {
              validationResult.warnings.push(validationError);
            }
          });
        }
      }
    } else {
      throw new BadRequestException(
        `Failed to validate product: ${result.body?.message || 'Unknown error'}`,
      );
    }

    return validationResult;
  }
}
