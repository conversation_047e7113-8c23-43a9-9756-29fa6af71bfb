import { Process, Processor } from '@nestjs/bull';
import {
  Logger,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { TikTokShop } from 'src/tiktok-shop/entities/tiktok-shop.entity';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { Category } from 'src/products/entities/category.entity';
import { getProperty } from 'src/common/utils/property-name.util';

@Injectable()
@Processor('category-sync')
export class CategoryQueueProcessor {
  private readonly logger = new Logger(CategoryQueueProcessor.name);
  private readonly BATCH_SIZE = 50; // Process categories in batches of 50

  constructor(
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    private readonly tikTokClientFactory: TikTokClientFactory,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Process a category synchronization job
   * @param job The Bull job
   * @returns Job result
   */
  @Process('sync-categories')
  async handleSyncCategories(job: Job) {
    this.logger.log(`Processing job ${job.id} of type ${job.name}`);
    this.logger.debug('Job data:', job.data);

    try {
      // Extract job data
      const { tiktokShopId } = job.data;
      if (!tiktokShopId) {
        throw new BadRequestException(
          'Missing required job data: tiktokShopId',
        );
      }

      // Get TikTok Shop
      const tiktokShop = await this.tikTokShopRepository.findOne({
        where: { id: tiktokShopId },
      });
      if (!tiktokShop) {
        throw new NotFoundException(
          `TikTok Shop with ID ${tiktokShopId} not found`,
        );
      }

      // Initialize progress
      await job.progress(10);
      this.logger.log('Category synchronization job: 10% - Initialized');

      // Create TikTok client
      const client = await this.tikTokClientFactory.createClientByAppKey(
        tiktokShop.app_key,
      );

      // Call TikTok API to get categories
      const result = await client.api.ProductV202309Api.CategoriesGet(
        tiktokShop.access_token,
        'application/json',
        undefined, // locale (optional)
        undefined, // keyword (optional)
        'v2', // categoryVersion - using v2 for US shops
        undefined, // listingPlatform (optional)
        tiktokShop.cipher,
        { headers: {} },
      );

      // Check if the request was successful
      if (result.body?.code !== 0 || !result.body?.data?.categories) {
        throw new BadRequestException(
          `Failed to get categories: ${result.body?.message || 'Unknown error'}`,
        );
      }

      // Update progress
      await job.progress(30);
      this.logger.log(
        'Category synchronization job: 30% - Retrieved categories from TikTok API',
      );

      // Transform the response
      const apiCategories = result.body.data.categories
        .map((category: any) => {
          // Get properties using getProperty utility to handle both camelCase and snake_case
          const id = getProperty(category, 'id');
          const parentId = getProperty(category, 'parentId');
          const localName = getProperty(category, 'localName');
          const isLeaf = getProperty(category, 'isLeaf');
          const permissionStatuses = getProperty(
            category,
            'permissionStatuses',
            [],
          );

          // Check if required properties are missing
          if (!id || !localName || isLeaf === undefined) {
            this.logger.error(
              `Skipping category with missing required properties: ${JSON.stringify(category, null, 2)}`,
            );
            return null; // Return null to filter out this category
          }

          return {
            idTT: id,
            parentIdTT: parentId,
            localName: localName,
            isLeaf: isLeaf,
            version: 'v2',
            permissionStatuses: permissionStatuses,
          };
        })
        .filter((category) => category !== null); // Filter out null categories

      // Update progress
      await job.progress(50);
      this.logger.log(
        `Category synchronization job: 50% - Transformed ${apiCategories.length} categories`,
      );

      // Save categories to database in batches
      const totalCategories = apiCategories.length;
      let processedCount = 0;
      const savedCategories: Category[] = [];

      // Process categories in batches
      for (let i = 0; i < apiCategories.length; i += this.BATCH_SIZE) {
        const batch = apiCategories.slice(i, i + this.BATCH_SIZE);
        const savedBatch = await this.saveCategoryBatch(batch);
        savedCategories.push(...savedBatch);

        processedCount += batch.length;
        const progressPercentage =
          50 + Math.floor((processedCount / totalCategories) * 40);
        await job.progress(progressPercentage);
        this.logger.log(
          `Category synchronization job: ${progressPercentage}% - Processed ${processedCount}/${totalCategories} categories`,
        );
      }

      // Update progress
      await job.progress(100);
      this.logger.log(
        `Category synchronization job: 100% - Completed. Saved ${savedCategories.length} categories`,
      );

      return {
        success: true,
        message: 'Category synchronization job completed successfully',
        jobId: job.id,
        categoriesCount: savedCategories.length,
      };
    } catch (error) {
      this.logger.error(`Error processing job ${job.id}:`, error);

      // Update job progress to indicate failure
      await job.progress(100);

      // Return error information instead of throwing
      // This allows the job to be marked as completed with error information
      return {
        success: false,
        message:
          error instanceof Error ? error.message : 'Unknown error occurred',
        error: error instanceof Error ? error.stack : String(error),
        jobId: job.id,
      };
    }
  }

  /**
   * Save a batch of categories to the database
   * @param categories Batch of categories to save
   * @returns Saved categories
   */
  private async saveCategoryBatch(categories: any[]): Promise<Category[]> {
    this.logger.log(
      `Saving batch of ${categories.length} categories to database`,
    );

    const savedCategories: Category[] = [];

    for (const categoryData of categories) {
      try {
        // Check if category already exists
        let category = await this.categoryRepository.findOne({
          where: { idTT: categoryData.idTT },
        });

        if (category) {
          // Update existing category
          category.parentIdTT = categoryData.parentIdTT;
          category.localName = categoryData.localName;
          category.isLeaf = categoryData.isLeaf;
          category.version = categoryData.version;
          category.permissionStatuses = categoryData.permissionStatuses;
        } else {
          // Create new category
          category = new Category();
          category.idTT = categoryData.idTT;
          category.parentIdTT = categoryData.parentIdTT;
          category.localName = categoryData.localName;
          category.isLeaf = categoryData.isLeaf;
          category.version = categoryData.version;
          category.permissionStatuses = categoryData.permissionStatuses;
        }

        // Save category
        const savedCategory = await this.categoryRepository.save(category);
        savedCategories.push(savedCategory);
      } catch (error) {
        this.logger.error(
          `Error saving category ${categoryData.idTT}: ${error.message}`,
          error.stack,
        );
        // Continue with next category
      }
    }

    this.logger.log(
      `Successfully saved batch of ${savedCategories.length} categories`,
    );
    return savedCategories;
  }
}
