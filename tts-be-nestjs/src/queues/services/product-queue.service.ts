import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

@Injectable()
export class ProductQueueService {
  private readonly logger = new Logger(ProductQueueService.name);

  constructor(
    @InjectQueue('product-creation') private readonly productQueue: Queue,
  ) {}

  /**
   * Add a product creation job to the queue
   * @param productUploadData Product upload data
   * @returns Job ID and status
   */
  async addProductCreationJob(productUploadData: {
    productUploadId: number;
    stagedProductId: number;
    tiktokShopId: number;
    userId?: number; // Add userId parameter
    options?: {
      skipValidation?: boolean;
      forceUpload?: boolean;
    };
  }) {
    this.logger.log(
      `Adding product creation job for product upload ID: ${productUploadData.productUploadId}${productUploadData.userId ? ` for user: ${productUploadData.userId}` : ''}`,
    );

    const job = await this.productQueue.add(
      'create-product',
      productUploadData,
      {
        attempts: 3, // Number of retry attempts if job fails
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 seconds initial delay
        },
        removeOnComplete: false, // Keep completed jobs in the queue for monitoring
        removeOnFail: false, // Keep failed jobs in the queue for debugging
      },
    );

    this.logger.log(`Product creation job added with ID: ${job.id}`);

    return {
      jobId: job.id,
      status: 'queued',
    };
  }

  /**
   * Get the status of a job
   * @param jobId Job ID
   * @returns Job status information
   */
  async getJobStatus(jobId: string) {
    this.logger.log(`Getting status for job ID: ${jobId}`);

    const job = await this.productQueue.getJob(jobId);

    if (!job) {
      this.logger.warn(`Job with ID ${jobId} not found`);
      return {
        exists: false,
        message: 'Job not found',
      };
    }

    const state = await job.getState();
    // Get job progress
    let progress = 0;
    try {
      // In Bull, progress is a method that returns the current progress when called with no arguments
      progress = await job.progress();
    } catch (error) {
      this.logger.warn(`Error getting job progress: ${error.message}`);
    }

    return {
      exists: true,
      id: job.id,
      state,
      progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      timestamp: job.timestamp,
      finishedOn: job.finishedOn,
    };
  }
}
