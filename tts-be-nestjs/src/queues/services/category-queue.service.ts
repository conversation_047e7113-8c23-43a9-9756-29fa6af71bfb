import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

@Injectable()
export class CategoryQueueService {
  private readonly logger = new Logger(CategoryQueueService.name);

  constructor(
    @InjectQueue('category-sync') private readonly categoryQueue: Queue,
  ) {}

  /**
   * Add a category synchronization job to the queue
   * @param tiktokShopId TikTok Shop ID
   * @returns Job ID and status
   */
  async addCategorySyncJob(tiktokShopId: number) {
    this.logger.log(
      `Adding category synchronization job for TikTok Shop ID: ${tiktokShopId}`,
    );

    const job = await this.categoryQueue.add(
      'sync-categories',
      { tiktokShopId },
      {
        attempts: 3, // Number of retry attempts if job fails
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 seconds initial delay
        },
        removeOnComplete: false, // Keep completed jobs in the queue for monitoring
        removeOnFail: false, // Keep failed jobs in the queue for debugging
      },
    );

    this.logger.log(`Category synchronization job added with ID: ${job.id}`);

    return {
      jobId: job.id,
      status: 'queued',
    };
  }

  /**
   * Get the status of a category synchronization job
   * @param jobId Job ID
   * @returns Job status information
   */
  async getJobStatus(jobId: string) {
    this.logger.log(`Getting status for category sync job ID: ${jobId}`);

    const job = await this.categoryQueue.getJob(jobId);

    if (!job) {
      this.logger.warn(`Job with ID ${jobId} not found`);
      return {
        exists: false,
        message: 'Job not found',
      };
    }

    const state = await job.getState();
    // Get job progress
    let progress = 0;
    try {
      // In Bull, progress is a method that returns the current progress when called with no arguments
      progress = await job.progress();
    } catch (error) {
      this.logger.warn(`Error getting job progress: ${error.message}`);
    }

    return {
      exists: true,
      id: job.id,
      state,
      progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      timestamp: job.timestamp,
      finishedOn: job.finishedOn,
    };
  }
}
