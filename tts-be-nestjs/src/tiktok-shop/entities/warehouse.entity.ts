import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TikTokShop } from './tiktok-shop.entity';

@Entity('warehouses')
export class Warehouse {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: '1',
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description:
      'The warehouse ID from TikTok Shop, a unique and immutable primary key',
    example: '7000714532876273420',
    required: true,
  })
  @Column({ length: 25, unique: true })
  idTT: string;

  @ApiProperty({
    description: 'Warehouse name',
    example: 'Main Warehouse',
  })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({
    description: 'Warehouse effect status (ENABLED, DISABLED, RESTRICTED)',
    example: 'ENABLED',
  })
  @Column({ length: 20, nullable: true })
  effectStatus: string;

  @ApiProperty({
    description: 'Whether this is the default warehouse',
    example: true,
  })
  @Column({ default: false })
  isDefault: boolean;

  @ApiProperty({
    description:
      'Warehouse sub-type (DOMESTIC_WAREHOUSE, CB_OVERSEA_WAREHOUSE, CB_DIRECT_SHIPPING_WAREHOUSE)',
    example: 'DOMESTIC_WAREHOUSE',
  })
  @Column({ length: 50, nullable: true })
  subType: string;

  @ApiProperty({
    description: 'Warehouse type (SALES_WAREHOUSE, RETURN_WAREHOUSE)',
    example: 'SALES_WAREHOUSE',
  })
  @Column({ length: 50, nullable: true })
  type: string;

  @ApiProperty({
    description: 'Full address of the warehouse',
    example: '123 Main St, Anytown, CA 12345',
  })
  @Column({ type: 'text', nullable: true })
  fullAddress: string;

  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main St',
  })
  @Column({ length: 255, nullable: true })
  addressLine1: string;

  @ApiProperty({
    description: 'Address line 2',
    example: 'Suite 100',
  })
  @Column({ length: 255, nullable: true })
  addressLine2: string;

  @ApiProperty({
    description: 'City',
    example: 'Anytown',
  })
  @Column({ length: 100, nullable: true })
  city: string;

  @ApiProperty({
    description: 'State or province',
    example: 'CA',
  })
  @Column({ length: 100, nullable: true })
  state: string;

  @ApiProperty({
    description: 'Postal code',
    example: '12345',
  })
  @Column({ length: 20, nullable: true })
  postalCode: string;

  @ApiProperty({
    description: 'Region',
    example: 'North America',
  })
  @Column({ length: 100, nullable: true })
  region: string;

  @ApiProperty({
    description: 'Region code',
    example: 'NA',
  })
  @Column({ length: 20, nullable: true })
  regionCode: string;

  @ApiProperty({
    description: 'Contact person name',
    example: 'John Doe',
  })
  @Column({ length: 100, nullable: true })
  contactPerson: string;

  @ApiProperty({
    description: 'Phone number',
    example: '******-123-4567',
  })
  @Column({ length: 50, nullable: true })
  phoneNumber: string;

  @ApiProperty({
    description: 'Geolocation data (latitude and longitude)',
    example: {
      latitude: '37.7749',
      longitude: '-122.4194',
    },
  })
  @Column('jsonb', { nullable: true })
  geolocation: {
    latitude?: string;
    longitude?: string;
  };

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({ description: 'TikTok Shop ID' })
  @Column()
  tiktokShopId: number;
}
