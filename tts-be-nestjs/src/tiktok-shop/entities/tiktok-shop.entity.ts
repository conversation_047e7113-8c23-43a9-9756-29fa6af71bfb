import {
  <PERSON><PERSON>ty,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Product } from '../../products/entities/product.entity';
import { User } from '../../auth/entities/user.entity';
import { Order } from '../../orders/entities/order.entity';

@Entity('tiktok_shops')
export class TikTokShop {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: '1',
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'An internal identifier for the TikTok Shop',
    example: '7000714532876273420',
    required: true,
  })
  @Column({ length: 25, unique: true })
  idTT: string;

  @ApiProperty({
    description: 'The TikTok Shop name',
    example: 'Maomao beauty shop',
  })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({
    description: 'A user-friendly name for the TikTok Shop',
    example: 'My Main Beauty Store',
    required: false,
  })
  @Column({ length: 255, nullable: true })
  friendly_name: string;

  @ApiProperty({
    description: 'The region of the shop',
    example: 'GB',
  })
  @Column({ length: 2 })
  region: string;

  @ApiProperty({
    description:
      'The type of seller: CROSS_BORDER for cross border sellers with multiple shops in different countries, LOCAL for local sellers with only 1 shop',
    example: 'CROSS_BORDER',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  @Column({
    name: 'seller_type',
    type: 'enum',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  sellerType: string;

  @ApiProperty({
    description:
      'An encrypted token used to securely identify a shop in API requests. There is no need for decryption on the receiving end',
    example: 'GCP_XF90igAAAABh00qsWgtvOiGFNqyubMt3',
    required: false,
  })
  @Column({ length: 255 })
  cipher: string;

  @ApiProperty({
    description:
      'The TikTok Shop code, which is also displayed on Seller Center',
    example: 'CNGBCBA4LLU8',
  })
  @Column({ length: 50 })
  code: string;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The last time the access/refresh token was updated',
    example: '2025-04-10T12:34:56.789Z',
    required: false,
  })
  @Column({ type: 'timestamp', nullable: true })
  last_refreshed_at: Date;

  @ApiProperty({
    description:
      'TikTok Shop App application key that was provided from system.',
    example: 'app_key_example',
  })
  @Column({ length: 20 })
  app_key: string;

  @ApiProperty({
    description:
      'Temporary authorization code from TikTok after user grants permission',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @Column('text')
  auth_code: string;

  @ApiProperty({
    description: 'Access token to call TikTok Shop API',
    example: 'access_token_example',
  })
  @Column('text')
  access_token: string;

  @ApiProperty({
    description: 'Unix timestamp (in seconds) when the access token expires',
    example: 1712668800,
  })
  @Column({ type: 'bigint' })
  access_token_expire_in: number;

  @ApiProperty({
    description: 'Refresh token to generate a new access token',
    example: 'refresh_token_example',
  })
  @Column('text')
  refresh_token: string;

  @ApiProperty({
    description: 'Unix timestamp (in seconds) when the refresh token expires',
    example: 1715260800,
  })
  @Column({ type: 'bigint' })
  refresh_token_expire_in: number;

  @ApiProperty({ description: 'Products' })
  @OneToMany(() => Product, (product) => product.tiktokShop, {
    cascade: true,
    eager: false,
  })
  products: Product[];

  @ApiProperty({ description: 'Orders' })
  @OneToMany(() => Order, (order) => order.tiktokShop, {
    cascade: true,
    eager: false,
  })
  orders: Order[];

  @ApiProperty({
    description: 'The ID of the user who owns this TikTok Shop',
    example: 1,
  })
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
