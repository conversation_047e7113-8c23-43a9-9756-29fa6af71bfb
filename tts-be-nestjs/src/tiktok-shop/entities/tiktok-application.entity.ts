import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('tiktok_applications')
export class TikTokApplication {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: '1',
  })
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  idTT: string;

  @Column()
  email: string;

  @Column({ unique: true })
  app_key: string;

  @Column()
  app_secret: string;

  @ApiProperty({
    description: 'The type of application: Custom or Public',
    example: 'Custom',
    enum: ['Custom', 'Public'],
  })
  @Column({
    type: 'enum',
    enum: ['Custom', 'Public'],
  })
  app_type: string;

  @Column({ nullable: true })
  redirect_url: string;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
