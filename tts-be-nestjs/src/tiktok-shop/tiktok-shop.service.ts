import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThan, Repository } from 'typeorm';
import { TikTokShop } from './entities/tiktok-shop.entity';
import { CreateTikTokShopDto } from './dto/create-tiktok-shop.dto';
import { UpdateTikTokShopDto } from './dto/update-tiktok-shop.dto';
import { TikTokShopResponseDto, TikTokShopCleanResponseDto } from './dto/tiktok-shop-response.dto';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { FilterTiktokShopDto } from './dto/filter-tiktok-shop.dto';
import { PaginatedResult } from 'src/common/interfaces/paginated-result.interface';
import { plainToInstance } from 'class-transformer';
import {
  AccessTokenTool,
} from '../../tts_sdk';
// Removed unused ConfigService import
import { TikTokClientFactory } from './tiktok-client.factory';
import { TikTokApplication } from './entities/tiktok-application.entity';
import { Cron, CronExpression } from '@nestjs/schedule';

import { CreateTikTokApplicationDto } from './dto/create-tiktok-application.dto';
import { Warehouse } from './entities/warehouse.entity';
import { WarehouseResponseDto } from './dto/warehouse-response.dto';
import { getProperty } from 'src/common/utils/property-name.util';

@Injectable()
export class TikTokShopService {
  private readonly logger = new Logger(TikTokShopService.name);

  constructor(
    @InjectRepository(TikTokShop)
    private tikTokShopRepository: Repository<TikTokShop>,

    // Removed unused configService
    private readonly clientFactory: TikTokClientFactory,

    @InjectRepository(TikTokApplication)
    private readonly tiktokAppRepo: Repository<TikTokApplication>,

    @InjectRepository(Warehouse)
    private readonly warehouseRepository: Repository<Warehouse>,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async refreshTokens() {
    this.logger.log('Starting scheduled token refresh job');
    const now = Math.floor(Date.now() / 1000);
    const expiringSoonThreshold = now + 60 * 60; // expires in next 60 minutes
    const thresholdDate = new Date(expiringSoonThreshold * 1000); // Convert to milliseconds

    this.logger.log(
      `RefreshTokens cron - Threshold for expiring soon tokens at ${thresholdDate.toISOString()}`,
    );
    const shopsToRefresh = await this.tikTokShopRepository.find({
      where: {
        access_token_expire_in: LessThan(expiringSoonThreshold),
      },
    });

    this.logger.log(
      `Found ${shopsToRefresh.length} shops with tokens that need refreshing`,
    );

    for (const shop of shopsToRefresh) {
      try {
        const app = await this.tiktokAppRepo.findOneBy({
          app_key: shop.app_key,
        });
        const { body } = await AccessTokenTool.refreshToken(
          shop.refresh_token,
          shop.app_key,
          app?.app_secret,
        );
        const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
        this.logger.debug(
          `Parsed RefreshToken response: ${JSON.stringify(parsedBody, null, 2)}`,
        );

        shop.access_token = parsedBody?.data?.access_token;
        shop.access_token_expire_in = parsedBody?.data?.access_token_expire_in;
        shop.refresh_token = parsedBody?.data?.refresh_token;
        shop.refresh_token_expire_in =
          parsedBody?.data?.refresh_token_expire_in;
        shop.last_refreshed_at = new Date(); //Track refresh time
        await this.tikTokShopRepository.save(shop);
      } catch (error) {
        this.logger.error(
          `Failed to refresh token for shop ${shop.idTT}: ${error.message}`,
          error.stack,
        );
      }
    }

    this.logger.log('Completed scheduled token refresh job');
  }

  async create(
    createTikTokShopDto: CreateTikTokShopDto,
    userId: number,
  ): Promise<TikTokShopResponseDto> {
    this.logger.log(
      `Creating new TikTok shop with idTT: ${createTikTokShopDto.idTT} for user: ${userId}`,
    );
    const existing = await this.tikTokShopRepository.findOne({
      where: { idTT: createTikTokShopDto.idTT },
    });

    if (existing) {
      // handle duplicate: skip, update, throw, etc.
      this.logger.warn(
        `Attempted to create duplicate TikTok shop with idTT: ${createTikTokShopDto.idTT}`,
      );
      throw new ConflictException('TikTok Shop with this idTT already exists.');
    }

    const tikTokShop = this.tikTokShopRepository.create({
      idTT: createTikTokShopDto.idTT,
      name: createTikTokShopDto.name,
      friendly_name: createTikTokShopDto.friendly_name || createTikTokShopDto.name, // Default to name if not provided
      region: createTikTokShopDto.region,
      sellerType: createTikTokShopDto.sellerType,
      cipher: createTikTokShopDto.cipher,
      code: createTikTokShopDto.code,
      app_key: createTikTokShopDto.app_key,
      auth_code: createTikTokShopDto.auth_code,
      access_token: createTikTokShopDto.access_token,
      access_token_expire_in: createTikTokShopDto.access_token_expire_in,
      refresh_token: createTikTokShopDto.refresh_token,
      refresh_token_expire_in: createTikTokShopDto.refresh_token_expire_in,
      userId: userId, // Set the userId
    });

    const savedShop = await this.tikTokShopRepository.save(tikTokShop);
    return this.mapToResponseDto(savedShop);
  }

  async createApplication(
    dto: CreateTikTokApplicationDto,
  ): Promise<TikTokApplication> {
    this.logger.log(
      `Creating new TikTok application with app_key: ${dto.app_key}`,
    );
    const exists = await this.tiktokAppRepo.findOne({
      where: { app_key: dto.app_key },
    });
    if (exists) {
      this.logger.warn(
        `Attempted to create duplicate TikTok application with app_key: ${dto.app_key}`,
      );
      throw new ConflictException(
        'Application with this app_key already exists',
      );
    }

    const newApp = this.tiktokAppRepo.create(dto);
    return await this.tiktokAppRepo.save(newApp);
  }

  async findAllApplications(): Promise<TikTokApplication[]> {
    this.logger.log('Finding all TikTok applications');
    return this.tiktokAppRepo.find({
      order: { createdAt: 'DESC' },
    });
  }

  async handleAuthorizationFlow(
    appKey: string,
    authCode: string,
  ): Promise<TikTokShopCleanResponseDto[]> {
    this.logger.log(`Starting authorization flow for app_key: ${appKey}`);
    const app = await this.tiktokAppRepo.findOne({
      where: { app_key: appKey },
    });

    if (!app) {
      this.logger.warn(`Application with app_key=${appKey} not found`);
      throw new NotFoundException(
        `Application with app_key=${appKey} not found`,
      );
    }

    this.logger.log(
      `Found application: ${app.app_key}. Proceeding with authorization.`,
    );
    const appSecret = app.app_secret;

    // Validation: Check if authCode already exists in any TikTok shop record
    this.logger.log(`Checking if auth_code already exists: ${authCode}`);
    const existingShopWithAuthCode = await this.tikTokShopRepository.findOne({
      where: { auth_code: authCode },
    });

    if (existingShopWithAuthCode) {
      this.logger.log(
        `Found existing shop with auth_code: ${existingShopWithAuthCode.idTT} (${existingShopWithAuthCode.name})`,
      );

      if (existingShopWithAuthCode.userId !== null) {
        this.logger.warn(
          `TikTok shop with auth_code ${authCode} is already connected to user ${existingShopWithAuthCode.userId}`,
        );
        throw new BadRequestException(
          'This TikTok shop has already been successfully connected to another user. Each shop can only be connected to one user account.',
        );
      } else {
        this.logger.log(
          `TikTok shop with auth_code ${authCode} exists but is not connected to any user. Returning existing shop.`,
        );
        // Return the existing shop as a single-item array
        return [this.mapToCleanResponseDto(existingShopWithAuthCode)];
      }
    } else {
      this.logger.log(
        `No existing shop found with auth_code ${authCode}. Proceeding with new authorization.`,
      );
      // Step 1: Use SDK to get access_token
      const { body } = await AccessTokenTool.getAccessToken(
        authCode,
        appKey,
        appSecret,
      );
      const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
      this.logger.debug(
        `Parsed AccessToken response: ${JSON.stringify(parsedBody, null, 2)}`,
      );

      const accessToken = parsedBody?.data?.access_token;
      const accessTokenExpireIn = parsedBody?.data?.access_token_expire_in;
      const refreshToken = parsedBody?.data?.refresh_token;
      const refreshTokenExpireIn = parsedBody?.data?.refresh_token_expire_in;

      if (!accessToken) {
        this.logger.error('Failed to get access token from TikTok API');
        throw new InternalServerErrorException('Failed to get access token');
      }

      this.logger.log(
        `Successfully obtained access token for app_key: ${appKey}`,
      );

      // Step 2: Use SDK client to get shop list
      const client = await this.clientFactory.createClientByAppKey(appKey);

      const contentType = 'application/json';
      const { body: shopsGetBody } =
        await client.api.AuthorizationV202309Api.ShopsGet(
          accessToken,
          contentType,
        );
      this.logger.debug(
        `ShopsGet response data: ${JSON.stringify(shopsGetBody, null, 2)}`,
      );
      const shopList = shopsGetBody.data?.shops || [];
      if (shopList.length === 0) {
        this.logger.warn(`No authorized shops found for app_key: ${appKey}`);
        throw new BadRequestException('No authorized shops found.');
      }

      // Step 3: Wait for all saves in parallel
      const savedShops = await Promise.all(
        shopList.map((shop) =>
          this.saveOrUpdateTiktokShop({
            app_key: appKey,
            auth_code: authCode,
            access_token: accessToken,
            access_token_expire_in: accessTokenExpireIn,
            refresh_token: refreshToken,
            refresh_token_expire_in: refreshTokenExpireIn,
            idTT: shop.id,
            name: shop.name,
            region: shop.region,
            sellerType: shop.sellerType,
            cipher: shop.cipher,
            code: shop.code,
          }),
        ),
      );

      // Step 3.5: Trigger warehouse synchronization for each shop (fire-and-forget)
      this.logger.log(`Triggering warehouse synchronization for ${savedShops.length} shops`);
      savedShops.forEach((shop) => {
        // Fire-and-forget: Don't await this operation to avoid blocking the response
        this.synchronizeWarehousesInBackground(shop.id)
          .catch((error: Error) => {
            this.logger.error(
              `Background warehouse synchronization failed for shop ${shop.id} (${shop.name}): ${error.message}`,
              error.stack,
            );
          });
      });

      // Step 4: Map to response DTOs
      return savedShops.map((shop) => this.mapToCleanResponseDto(shop));
    }
  }

  async findAll(
    paginationQuery: PaginationQueryDto,
    filterDto: FilterTiktokShopDto,
    userId: number,
  ): Promise<PaginatedResult<TikTokShopCleanResponseDto>> {
    this.logger.log(
      `Finding all TikTok shops with filters: ${JSON.stringify(filterDto)} for user: ${userId}`,
    );

    const { limit = 20, page = 1 } = paginationQuery;
    const { name, code } = filterDto; // Note: idTT filtering is excluded for regular users

    // Calculate the skip value based on page and limit
    const skip = (page - 1) * limit;

    const queryBuilder =
      this.tikTokShopRepository.createQueryBuilder('tiktok-shop');

    // Add userId filter
    queryBuilder.andWhere('tiktok-shop.userId = :userId', { userId });

    if (name) {
      queryBuilder.andWhere('tiktok-shop.name ILIKE :name', {
        name: `%${name}%`,
      });
    }
    if (code) {
      queryBuilder.andWhere('tiktok-shop.code ILIKE :code', {
        code: `%${code}%`,
      });
    }
    queryBuilder.orderBy('tiktok-shop.createdAt', 'DESC');
    queryBuilder.take(limit);
    queryBuilder.skip(skip);

    const [data, total] = await queryBuilder.getManyAndCount();

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return {
      data: data.map((shop) => this.mapToCleanResponseDto(shop)),
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  async findOne(id: number, userId: number): Promise<TikTokShopCleanResponseDto> {
    this.logger.log(`Finding TikTok shop with ID: ${id} for user: ${userId}`);
    const shop = await this.tikTokShopRepository.findOne({
      where: { id, userId },
    });

    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${id} not found for user: ${userId}`,
      );
      throw new NotFoundException(`TikTok Shop with ID ${id} not found`);
    }

    return this.mapToCleanResponseDto(shop);
  }

  async findByTikTokId(
    idTT: string,
    userId: number,
  ): Promise<TikTokShopResponseDto> {
    this.logger.log(
      `Finding TikTok shop with idTT: ${idTT} for user: ${userId}`,
    );
    const shop = await this.tikTokShopRepository.findOne({
      where: { idTT, userId },
    });

    if (!shop) {
      this.logger.warn(
        `TikTok Shop with idTT ${idTT} not found for user: ${userId}`,
      );
      throw new NotFoundException(`TikTok Shop with idTT ${idTT} not found`);
    }

    return this.mapToResponseDto(shop);
  }

  async update(
    id: number,
    updateTikTokShopDto: UpdateTikTokShopDto,
    userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    this.logger.log(`Updating TikTok shop with ID: ${id} for user: ${userId}`);
    const shop = await this.tikTokShopRepository.findOne({
      where: { id, userId },
    });

    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${id} not found for user: ${userId} during update attempt`,
      );
      throw new NotFoundException(`TikTok Shop with ID ${id} not found`);
    }

    // Check if idTT is being changed and if the new one already exists
    if (updateTikTokShopDto.idTT && updateTikTokShopDto.idTT !== shop.idTT) {
      const existingShop = await this.tikTokShopRepository.findOne({
        where: { idTT: updateTikTokShopDto.idTT },
      });

      if (existingShop) {
        this.logger.warn(
          `Attempted to update shop ID ${id} with idTT "${updateTikTokShopDto.idTT}" which already exists`,
        );
        throw new BadRequestException(
          `idTT "${updateTikTokShopDto.idTT}" already exists.`,
        );
      }
    }

    this.tikTokShopRepository.merge(shop, updateTikTokShopDto);
    await this.tikTokShopRepository.save(shop);
    this.logger.log(
      `Successfully updated TikTok shop with ID: ${id} for user: ${userId}`,
    );
    return this.mapToCleanResponseDto(shop);
  }

  async updateFriendlyName(
    id: number,
    friendlyName: string,
    userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    this.logger.log(`Updating friendly name for TikTok shop with ID: ${id} for user: ${userId}`);
    const shop = await this.tikTokShopRepository.findOne({
      where: { id, userId },
    });

    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${id} not found for user: ${userId} during friendly name update attempt`,
      );
      throw new NotFoundException(`TikTok Shop with ID ${id} not found`);
    }

    shop.friendly_name = friendlyName;
    await this.tikTokShopRepository.save(shop);
    this.logger.log(
      `Successfully updated friendly name for TikTok shop with ID: ${id} for user: ${userId}`,
    );
    return this.mapToCleanResponseDto(shop);
  }

  async remove(id: number, userId: number): Promise<void> {
    this.logger.log(`Removing TikTok shop with ID: ${id} for user: ${userId}`);
    const result = await this.tikTokShopRepository.delete({ id, userId });

    if (result.affected === 0) {
      this.logger.warn(
        `TikTok Shop with ID ${id} not found for user: ${userId} during removal attempt`,
      );
      throw new NotFoundException(`TikTok Shop with ID ${id} not found`);
    }

    this.logger.log(
      `Successfully removed TikTok shop with ID: ${id} for user: ${userId}`,
    );
  }

  async saveOrUpdateTiktokShop(
    shopData: Partial<TikTokShop>,
  ): Promise<TikTokShop> {
    const existing = await this.tikTokShopRepository.findOne({
      where: { idTT: shopData.idTT },
    });

    if (existing) {
      // Update existing
      this.logger.log(
        `Updating existing TikTok shop: ${existing.idTT} (${existing.name})`,
      );
      this.tikTokShopRepository.merge(existing, shopData);
      return this.tikTokShopRepository.save(existing);
    }

    // Create new
    this.logger.log(
      `Creating new TikTok shop: ${shopData.idTT} (${shopData.name})`,
    );
    const newShop = this.tikTokShopRepository.create({
      ...shopData,
      friendly_name: shopData.friendly_name || shopData.name, // Default to name if friendly_name not provided
    });

    // If userId is not provided in shopData, we'll set it to null
    // This is for backward compatibility with existing code that doesn't provide userId
    if (!newShop.userId) {
      this.logger.warn(
        `Creating TikTok shop without userId: ${shopData.idTT} (${shopData.name})`,
      );
    }

    return this.tikTokShopRepository.save(newShop);
  }

  private mapToResponseDto(shop: TikTokShop): TikTokShopResponseDto {
    return plainToInstance(TikTokShopResponseDto, {
      id: shop.id,
      idTT: shop.idTT,
      name: shop.name,
      friendly_name: shop.friendly_name || shop.name, // Fallback to name if friendly_name is null
      region: shop.region,
      seller_type: shop.sellerType,
      cipher: shop.cipher,
      code: shop.code,
      createdAt: shop.createdAt,
      updatedAt: shop.updatedAt,
      app_key: shop.app_key,
      auth_code: shop.auth_code,
      access_token: shop.access_token,
      access_token_expire_in: shop.access_token_expire_in,
      refresh_token: shop.refresh_token,
      refresh_token_expire_in: shop.refresh_token_expire_in,
      last_refreshed_at: shop.last_refreshed_at,
    });
  }

  private mapToCleanResponseDto(shop: TikTokShop): TikTokShopCleanResponseDto {
    return plainToInstance(TikTokShopCleanResponseDto, {
      id: shop.id,
      name: shop.name,
      friendly_name: shop.friendly_name || shop.name, // Fallback to name if friendly_name is null
      region: shop.region,
      seller_type: shop.sellerType,
      code: shop.code,
      expired_in : shop.refresh_token_expire_in,
      createdAt: shop.createdAt,
      updatedAt: shop.updatedAt,
      last_refreshed_at: shop.last_refreshed_at,
    });
  }

  /**
   * Get warehouses for a TikTok Shop
   * @param tiktokShopId TikTok Shop ID
   * @param userId User ID
   * @returns List of warehouses
   */
  async getWarehouses(
    tiktokShopId: number,
    userId: number,
  ): Promise<WarehouseResponseDto[]> {
    this.logger.log(
      `Getting warehouses for TikTok Shop ID: ${tiktokShopId} for user: ${userId}`,
    );

    const shop = await this.tikTokShopRepository.findOne({
      where: { id: tiktokShopId, userId },
    });
    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${tiktokShopId} not found for user: ${userId}`,
      );
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }

    const warehouses = await this.warehouseRepository.find({
      where: { tiktokShopId },
      order: { createdAt: 'DESC' },
    });

    return warehouses.map((warehouse) =>
      this.mapToWarehouseResponseDto(warehouse),
    );
  }

  /**
   * Get the default warehouse for a TikTok Shop
   * @param tiktokShopId TikTok Shop ID
   * @returns Default warehouse
   */
  async getDefaultWarehouse(
    tiktokShopId: number,
  ): Promise<WarehouseResponseDto> {
    this.logger.log(
      `Getting default warehouse for TikTok Shop ID: ${tiktokShopId}`,
    );

    const defaultWarehouse = await this.warehouseRepository.findOne({
      where: { tiktokShopId, isDefault: true },
    });

    if (!defaultWarehouse) {
      this.logger.warn(
        `No default warehouse found for TikTok Shop ID: ${tiktokShopId}`,
      );
      throw new NotFoundException(
        `No default warehouse found for TikTok Shop with ID ${tiktokShopId}`,
      );
    }

    return this.mapToWarehouseResponseDto(defaultWarehouse);
  }

  /**
   * Synchronize warehouses from TikTok Shop API
   * @param tiktokShopId TikTok Shop ID
   * @param userId User ID
   * @returns List of synchronized warehouses
   */
  async synchronizeWarehouses(
    tiktokShopId: number,
    userId: number,
  ): Promise<WarehouseResponseDto[]> {
    this.logger.log(
      `Synchronizing warehouses for TikTok Shop ID: ${tiktokShopId} for user: ${userId}`,
    );

    const shop = await this.tikTokShopRepository.findOne({
      where: { id: tiktokShopId, userId },
    });
    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${tiktokShopId} not found for user: ${userId}`,
      );
      throw new NotFoundException(
        `TikTok Shop with ID ${tiktokShopId} not found`,
      );
    }

    try {
      await this.performWarehouseSynchronization(shop, '');

      // Get the updated list of warehouses after all operations
      const updatedWarehouses = await this.warehouseRepository.find({
        where: { tiktokShopId: shop.id },
        order: { createdAt: 'DESC' },
      });

      return updatedWarehouses.map((warehouse) =>
        this.mapToWarehouseResponseDto(warehouse),
      );
    } catch (error) {
      this.logger.error(
        `Error synchronizing warehouses: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to synchronize warehouses: ${error.message}`,
      );
    }
  }

  /**
   * Synchronize warehouses in background (fire-and-forget)
   * This method is designed for background execution without user ID validation
   * @param tiktokShopId TikTok Shop ID
   * @returns Promise that resolves when synchronization is complete
   */
  private async synchronizeWarehousesInBackground(
    tiktokShopId: number,
  ): Promise<void> {
    this.logger.log(
      `[Background] Starting warehouse synchronization for TikTok Shop ID: ${tiktokShopId}`,
    );

    try {
      const shop = await this.tikTokShopRepository.findOne({
        where: { id: tiktokShopId },
      });

      if (!shop) {
        this.logger.warn(
          `[Background] TikTok Shop with ID ${tiktokShopId} not found`,
        );
        return;
      }

      await this.performWarehouseSynchronization(shop, '[Background] ');

      this.logger.log(
        `[Background] Successfully synchronized warehouses for TikTok Shop ID: ${tiktokShopId}`,
      );
    } catch (error) {
      this.logger.error(
        `[Background] Error synchronizing warehouses for shop ${tiktokShopId}: ${error.message}`,
        error.stack,
      );
      // Don't throw the error since this is a background operation
    }
  }

  /**
   * Core warehouse synchronization logic shared between public and background methods
   * @param shop TikTok Shop entity
   * @param logPrefix Prefix for log messages (e.g., '[Background] ')
   * @returns Promise that resolves when synchronization is complete
   */
  private async performWarehouseSynchronization(
    shop: TikTokShop,
    logPrefix: string,
  ): Promise<void> {
    // Create TikTok Shop API client
    const client = await this.clientFactory.createClientByAppKey(shop.app_key);
    const contentType = 'application/json';

    // Call TikTok Shop API to get warehouses
    const { body } = await client.api.LogisticsV202309Api.WarehousesGet(
      shop.access_token,
      contentType,
      shop.cipher,
    );

    this.logger.debug(
      `${logPrefix}Warehouse API response${logPrefix ? ` for shop ${shop.id}` : ''}: ${JSON.stringify(body, null, 2)}`,
    );

    const warehouses = getProperty(
      getProperty(body, 'data'),
      'warehouses',
      [],
    );

    if (
      !warehouses ||
      !Array.isArray(warehouses) ||
      warehouses.length === 0
    ) {
      this.logger.warn(
        `${logPrefix}No warehouses found${logPrefix ? ` for shop ${shop.id}` : ''} or invalid response format`,
      );
      return;
    }

    // Get existing warehouses for this shop
    const existingWarehouses = await this.warehouseRepository.find({
      where: { tiktokShopId: shop.id },
    });

    // Create a set of warehouse IDs from the API response for quick lookup
    const apiWarehouseIds = new Set(
      warehouses.map((warehouse) => getProperty(warehouse, 'id')),
    );

    // Find warehouses in our database that don't exist in the API response
    const warehousesToRemove = existingWarehouses.filter(
      (warehouse) => !apiWarehouseIds.has(warehouse.idTT),
    );

    // Remove warehouses that don't exist in the API response
    if (warehousesToRemove.length > 0) {
      this.logger.log(
        `${logPrefix}Removing ${warehousesToRemove.length} warehouses${logPrefix ? ` for shop ${shop.id}` : ''} that don't exist in the API response`,
      );
      for (const warehouseToRemove of warehousesToRemove) {
        this.logger.log(
          `${logPrefix}Removing warehouse: ${warehouseToRemove.idTT} (${warehouseToRemove.name})`,
        );
        await this.warehouseRepository.remove(warehouseToRemove);
      }
    }

    // Process and save warehouses
    await Promise.all(
      warehouses.map(async (warehouseData) => {
        return this.saveOrUpdateWarehouse({
          idTT: getProperty(warehouseData, 'id'),
          name: getProperty(warehouseData, 'name'),
          effectStatus: getProperty(warehouseData, 'effectStatus'),
          isDefault: getProperty(warehouseData, 'isDefault', false),
          subType: getProperty(warehouseData, 'subType'),
          type: getProperty(warehouseData, 'type'),
          tiktokShopId: shop.id,
          // Handle address properties safely
          fullAddress: getProperty(
            getProperty(warehouseData, 'address'),
            'fullAddress',
          ),
          addressLine1: getProperty(
            getProperty(warehouseData, 'address'),
            'addressLine1',
          ),
          addressLine2: getProperty(
            getProperty(warehouseData, 'address'),
            'addressLine2',
          ),
          city: getProperty(getProperty(warehouseData, 'address'), 'city'),
          state: getProperty(getProperty(warehouseData, 'address'), 'state'),
          postalCode: getProperty(
            getProperty(warehouseData, 'address'),
            'postalCode',
          ),
          region: getProperty(
            getProperty(warehouseData, 'address'),
            'region',
          ),
          regionCode: getProperty(
            getProperty(warehouseData, 'address'),
            'regionCode',
          ),
          contactPerson: getProperty(
            getProperty(warehouseData, 'address'),
            'contactPerson',
          ),
          phoneNumber: getProperty(
            getProperty(warehouseData, 'address'),
            'phoneNumber',
          ),
          // Handle geolocation safely
          geolocation: getProperty(
            getProperty(warehouseData, 'address'),
            'geolocation',
          )
            ? {
                latitude: getProperty(
                  getProperty(
                    getProperty(warehouseData, 'address'),
                    'geolocation',
                  ),
                  'latitude',
                ),
                longitude: getProperty(
                  getProperty(
                    getProperty(warehouseData, 'address'),
                    'geolocation',
                  ),
                  'longitude',
                ),
              }
            : undefined,
        });
      }),
    );
  }

  /**
   * Save or update a warehouse
   * @param warehouseData Warehouse data
   * @returns Saved warehouse
   */
  private async saveOrUpdateWarehouse(
    warehouseData: Partial<Warehouse>,
  ): Promise<Warehouse> {
    const { idTT } = warehouseData;

    const existing = await this.warehouseRepository.findOne({
      where: { idTT },
    });

    if (existing) {
      // Update existing warehouse
      this.logger.log(
        `Updating existing warehouse: ${existing.idTT} (${existing.name})`,
      );
      this.warehouseRepository.merge(existing, warehouseData);
      return this.warehouseRepository.save(existing);
    }

    // Create new warehouse
    this.logger.log(`Creating new warehouse: ${idTT} (${warehouseData.name})`);
    const newWarehouse = this.warehouseRepository.create(warehouseData);
    return this.warehouseRepository.save(newWarehouse);
  }

  /**
   * Map warehouse entity to response DTO
   * @param warehouse Warehouse entity
   * @returns Warehouse response DTO
   */
  private mapToWarehouseResponseDto(
    warehouse: Warehouse,
  ): WarehouseResponseDto {
    return plainToInstance(WarehouseResponseDto, {
      id: warehouse.id,
      idTT: warehouse.idTT,
      name: warehouse.name,
      effectStatus: warehouse.effectStatus,
      isDefault: warehouse.isDefault,
      subType: warehouse.subType,
      type: warehouse.type,
      address: {
        fullAddress: warehouse.fullAddress,
        addressLine1: warehouse.addressLine1,
        addressLine2: warehouse.addressLine2,
        city: warehouse.city,
        state: warehouse.state,
        postalCode: warehouse.postalCode,
        region: warehouse.region,
        regionCode: warehouse.regionCode,
        contactPerson: warehouse.contactPerson,
        phoneNumber: warehouse.phoneNumber,
      },
      geolocation: warehouse.geolocation,
      tiktokShopId: warehouse.tiktokShopId,
      createdAt: warehouse.createdAt,
      updatedAt: warehouse.updatedAt,
    });
  }

  /**
   * Find a TikTokShop by its ID
   * @param id TikTokShop ID
   * @returns TikTokShop entity or null if not found
   */
  async findTikTokShopById(id: number): Promise<TikTokShop | null> {
    this.logger.log(`Finding TikTokShop by ID: ${id}`);
    return await this.tikTokShopRepository.findOne({ where: { id } });
  }

  /**
   * Find all TikTok shops without userId constraint (admin only)
   * @param paginationQuery Pagination parameters
   * @param filterDto Filter parameters
   * @returns Paginated list of TikTok shops
   */
  async findAllForAdmin(
    paginationQuery: PaginationQueryDto,
    filterDto: FilterTiktokShopDto,
  ): Promise<PaginatedResult<TikTokShopResponseDto>> {
    this.logger.log(
      `[ADMIN] Finding all TikTok shops with filters: ${JSON.stringify(filterDto)}`,
    );

    const { limit = 20, page = 1 } = paginationQuery;
    const { name, code, idTT } = filterDto;

    // Calculate the skip value based on page and limit
    const skip = (page - 1) * limit;

    const queryBuilder =
      this.tikTokShopRepository.createQueryBuilder('tiktok-shop');

    // Apply filters but without userId constraint
    if (name) {
      queryBuilder.andWhere('tiktok-shop.name ILIKE :name', {
        name: `%${name}%`,
      });
    }
    if (code) {
      queryBuilder.andWhere('tiktok-shop.code ILIKE :code', {
        code: `%${code}%`,
      });
    }
    if (idTT) {
      queryBuilder.andWhere('tiktok-shop.idTT ILIKE :idTT', {
        idTT: `%${idTT}%`,
      });
    }

    queryBuilder.orderBy('tiktok-shop.createdAt', 'DESC');
    queryBuilder.take(limit);
    queryBuilder.skip(skip);

    const [data, total] = await queryBuilder.getManyAndCount();

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return {
      data: data.map((shop) => this.mapToResponseDto(shop)),
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Generate authorization link for TikTok Shop
   * @param user Current authenticated user
   * @returns Authorization URL and instructions
   */
  generateAuthorizationLink(userId: number) {
    this.logger.log(`Generating TikTok Shop authorization link for user: ${userId}`);
    return {
      authorizationUrl: "https://services.us.tiktokshop.com/open/authorize?service_id=7530937812298499854",
      instructions: [
        "Open this link in the same browser where you are logged into TikTok Shop",
        "Review and approve the authorization request",
        "You will be redirected back to our application",
        "Copy your shop codes for manual connection"
      ]
    };
  }

  /**
   * Connect shop to user by shop code
   * @param shopCode Shop code to connect
   * @param userId User ID to connect shop to
   * @returns Connected shop
   */
  async connectShopToUser(shopCode: string, userId: number): Promise<TikTokShopResponseDto> {
    this.logger.log(`Connecting shop with code: ${shopCode} to user: ${userId}`);

    // First, find shop by code regardless of userId
    const shop = await this.tikTokShopRepository.findOne({
      where: { code: shopCode }
    });

    if (!shop) {
      this.logger.warn(`Shop with code ${shopCode} not found`);
      throw new NotFoundException('Shop not found');
    }

    // Check if shop is already connected
    if (shop.userId !== null) {
      if (shop.userId === userId) {
        // Shop already connected to the same user - return success
        this.logger.log(`Shop ${shopCode} already connected to user ${userId} - returning existing connection`);
        return this.mapToResponseDto(shop);
      } else {
        // Shop connected to different user - return error
        this.logger.warn(`Shop ${shopCode} already connected to different user ${shop.userId}`);
        throw new BadRequestException('Shop is already connected to another user');
      }
    }

    // Shop not connected yet - connect it
    shop.userId = userId;
    const savedShop = await this.tikTokShopRepository.save(shop);

    this.logger.log(`Successfully connected shop ${shopCode} to user ${userId}`);
    return this.mapToResponseDto(savedShop);
  }

  /**
   * Refresh TikTok Shop access token immediately for a specific shop
   * @param shopId TikTok Shop ID (internal database ID)
   * @param userId Current user ID for validation
   * @returns Updated shop with refreshed tokens
   */
  async refreshTokenTiktok(shopId: number, userId: number): Promise<TikTokShopCleanResponseDto> {
    this.logger.log(`Refreshing token for TikTok shop ID: ${shopId} for user: ${userId}`);

    // Find the shop and validate user ownership
    const shop = await this.tikTokShopRepository.findOne({
      where: { id: shopId, userId },
    });

    if (!shop) {
      this.logger.warn(
        `TikTok Shop with ID ${shopId} not found for user: ${userId} during token refresh attempt`,
      );
      throw new NotFoundException(`TikTok Shop with ID ${shopId} not found or access denied`);
    }

    try {
      // Get the TikTok application details
      const app = await this.tiktokAppRepo.findOneBy({
        app_key: shop.app_key,
      });

      if (!app) {
        this.logger.error(`TikTok application with app_key ${shop.app_key} not found`);
        throw new BadRequestException('TikTok application configuration not found');
      }

      this.logger.log(`Performing immediate token refresh for shop ${shop.idTT} (${shop.name})`);

      // Perform token refresh using TikTok SDK
      const { body } = await AccessTokenTool.refreshToken(
        shop.refresh_token,
        shop.app_key,
        app.app_secret,
      );
      const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;

      this.logger.debug(
        `Parsed RefreshToken response for shop ${shop.idTT}: ${JSON.stringify(parsedBody, null, 2)}`,
      );

      // Update shop with new token information
      shop.access_token = parsedBody?.data?.access_token;
      shop.access_token_expire_in = parsedBody?.data?.access_token_expire_in;
      shop.refresh_token = parsedBody?.data?.refresh_token;
      shop.refresh_token_expire_in = parsedBody?.data?.refresh_token_expire_in;
      shop.last_refreshed_at = new Date();

      const savedShop = await this.tikTokShopRepository.save(shop);

      this.logger.log(
        `Successfully refreshed token for shop ${shop.idTT} (${shop.name}) for user: ${userId}`,
      );

      return this.mapToCleanResponseDto(savedShop);
    } catch (error) {
      this.logger.error(
        `Failed to refresh token for shop ${shop.idTT} (${shop.name}) for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Token refresh failed: ${error.message || 'Unknown error occurred'}`,
      );
    }
  }
}
