import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TikTokShopController } from './tiktok-shop.controller';
import { TikTokShopService } from './tiktok-shop.service';
import { TikTokShop } from './entities/tiktok-shop.entity';
import { TikTokClientFactory } from './tiktok-client.factory';
import { TikTokApplication } from './entities/tiktok-application.entity';
import { Warehouse } from './entities/warehouse.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TikTokShop, TikTokApplication, Warehouse]),
  ],
  controllers: [TikTokShopController],
  providers: [TikTokShopService, TikTokClientFactory],
  exports: [TikTokShopService, TikTokClientFactory],
})
export class TiktokShopModule {}
