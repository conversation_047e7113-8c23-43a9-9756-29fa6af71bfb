import { IsEnum, IsString, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { CreateTikTokShopDto } from './create-tiktok-shop.dto';

export class UpdateTikTokShopDto extends PartialType(CreateTikTokShopDto) {
  @ApiPropertyOptional({
    description: 'An internal identifier for the TikTok Shop',
    example: '7000714532876273420',
  })
  @IsString()
  idTT?: string;

  @ApiPropertyOptional({
    description: 'The TikTok Shop name',
    example: 'Maomao beauty shop',
  })
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'A user-friendly name for the TikTok Shop',
    example: 'My Main Beauty Store',
  })
  @IsString()
  friendly_name?: string;

  @ApiPropertyOptional({
    description: 'The region of the shop',
    example: 'GB',
  })
  @IsString()
  @Length(2, 2)
  region?: string;

  @ApiPropertyOptional({
    description: 'The type of seller',
    example: 'CROSS_BORDER',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  @IsString()
  @IsEnum(['CROSS_BORDER', 'LOCAL'])
  sellerType?: string;

  @ApiPropertyOptional({
    description:
      'An encrypted token used to securely identify a shop in API requests',
    example: 'GCP_XF90igAAAABh00qsWgtvOiGFNqyubMt3',
  })
  @IsString()
  cipher?: string;

  @ApiPropertyOptional({
    description: 'The TikTok Shop code',
    example: 'CNGBCBA4LLU8',
  })
  @IsString()
  code?: string;

  @ApiProperty({
    description:
      'TikTok Shop App application key that was provided from system.',
    example: 'app_key_example',
  })
  app_key?: string;

  @ApiProperty({
    description: 'Temporary authorization code from TikTok',
    example: 'auth_code_example',
  })
  auth_code?: string;

  @ApiProperty({
    description: 'Access token to call TikTok Shop API',
    example: 'access_token_example',
  })
  access_token?: string;

  @ApiProperty({
    description: 'Access token expiry in Unix timestamp (seconds)',
    example: 1712668800,
  })
  access_token_expire_in?: number;

  @ApiProperty({
    description: 'Refresh token to generate a new access token',
    example: 'refresh_token_example',
  })
  refresh_token?: string;

  @ApiProperty({
    description: 'Refresh token expiry in Unix timestamp (seconds)',
    example: 1715260800,
  })
  refresh_token_expire_in?: number;
}
