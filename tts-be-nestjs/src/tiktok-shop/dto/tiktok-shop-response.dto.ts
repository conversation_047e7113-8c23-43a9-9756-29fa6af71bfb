import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class TikTokShopCleanResponseDto {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The TikTok Shop name',
    example: 'Maomao beauty shop',
  })
  name: string;

  @ApiProperty({
    description: 'A user-friendly name for the TikTok Shop',
    example: 'My Main Beauty Store',
    required: false,
  })
  friendly_name: string;

  @ApiProperty({
    description: 'The region of the shop',
    example: 'GB',
  })
  region: string;

  @ApiProperty({
    description: 'The type of seller',
    example: 'CROSS_BORDER',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  seller_type: string;

  @ApiProperty({
    description: 'The TikTok Shop code',
    example: 'CNGBCBA4LLU8',
  })
  code: string;

  expired_in: bigint;

  @ApiProperty({ description: 'Created Date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Last Refreshed At' })
  last_refreshed_at: Date;
}

export class TikTokShopResponseDto {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'An internal identifier for the TikTok Shop',
    example: '7000714532876273420',
  })
  idTT: string;

  @ApiProperty({
    description: 'The TikTok Shop name',
    example: 'Maomao beauty shop',
  })
  name: string;

  @ApiProperty({
    description: 'A user-friendly name for the TikTok Shop',
    example: 'My Main Beauty Store',
    required: false,
  })
  friendly_name: string;

  @ApiProperty({
    description: 'The region of the shop',
    example: 'GB',
  })
  region: string;

  @ApiProperty({
    description: 'The type of seller',
    example: 'CROSS_BORDER',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  seller_type: string;

  @ApiProperty({
    description:
      'An encrypted token used to securely identify a shop in API requests',
    example: 'GCP_XF90igAAAABh00qsWgtvOiGFNqyubMt3',
  })
  cipher: string;

  @ApiProperty({
    description: 'The TikTok Shop code',
    example: 'CNGBCBA4LLU8',
  })
  code: string;

  @ApiProperty({ description: 'Created Date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  updatedAt: Date;

  @ApiProperty({
    description:
      'TikTok Shop App application key that was provided from system.',
    example: 'app_key_example',
  })
  app_key: string;

  @ApiProperty({
    description: 'Temporary authorization code from TikTok',
    example: 'auth_code_example',
  })
  auth_code: string;

  @ApiProperty({
    description: 'Access token to call TikTok Shop API',
    example: 'access_token_example',
  })
  access_token: string;

  @ApiProperty({
    description: 'Unix timestamp (seconds) when the access token expires',
    example: 1712668800,
  })
  @Transform(({ value }) =>
    value ? new Date(value * 1000).toISOString() : null,
  )
  access_token_expire_in: string;

  @ApiProperty({
    description: 'Refresh token to generate a new access token',
    example: 'refresh_token_example',
  })
  refresh_token: string;

  @ApiProperty({
    description: 'Unix timestamp (seconds) when the refresh token expires',
    example: 1715260800,
  })
  @Transform(({ value }) =>
    value ? new Date(value * 1000).toISOString() : null,
  )
  refresh_token_expire_in: string;

  @ApiProperty({ description: 'Last Refreshed At' })
  last_refreshed_at: Date;
}
