import { IsEnum, IsString, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTikTokApplicationDto {
  @ApiProperty({
    description: 'Tiktok application name',
    example: 'Tiktok Agent',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Tiktok application id',
    example: '7488197576619738923',
  })
  @IsString()
  idTT: string;

  @ApiProperty({
    description: 'Tiktok application owner email',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;

  @ApiProperty({
    description: 'Tiktok app key',
    example: '6fn6********',
  })
  @IsString()
  app_key: string;

  @ApiProperty({
    description: 'Tiktok app secret',
    example: 'be2a624cf2*****',
  })
  @IsString()
  app_secret: string;

  @ApiProperty({
    description: 'The type of application',
    example: 'Custom',
    enum: ['Custom', 'Public'],
  })
  @IsString()
  @IsEnum(['Custom', 'Public'])
  app_type: string;

  @ApiProperty({
    description: 'Tiktok application redirect url',
    example: 'https://your-application.com/tiktok-authorize',
  })
  @IsString()
  redirect_url: string;
}
