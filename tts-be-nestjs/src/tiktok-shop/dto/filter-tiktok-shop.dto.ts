import { <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class FilterTiktokShopDto {
  @ApiPropertyOptional({ description: 'Name', example: 'Maomao beauty shop' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'idTT', example: '7000714532876273420' })
  @IsOptional()
  @IsString()
  idTT?: string;

  @ApiPropertyOptional({ description: 'code', example: 'CNGBCBA4LLU8' })
  @IsOptional()
  @IsString()
  code?: string;
}
