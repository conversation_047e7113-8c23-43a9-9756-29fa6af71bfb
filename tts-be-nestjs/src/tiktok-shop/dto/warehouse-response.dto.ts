import { ApiProperty } from '@nestjs/swagger';

export class WarehouseResponseDto {
  @ApiProperty({
    description: 'Internal warehouse ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'TikTok Shop warehouse ID',
    example: '7000714532876273420',
  })
  idTT: string;

  @ApiProperty({
    description: 'Warehouse name',
    example: 'Main Warehouse',
  })
  name: string;

  @ApiProperty({
    description: 'Warehouse effect status (ENABLED, DISABLED, RESTRICTED)',
    example: 'ENABLED',
  })
  effectStatus: string;

  @ApiProperty({
    description: 'Whether this is the default warehouse',
    example: true,
  })
  isDefault: boolean;

  @ApiProperty({
    description:
      'Warehouse sub-type (DOMESTIC_WAREHOUSE, CB_OVERSEA_WAREHOUSE, CB_DIRECT_SHIPPING_WAREHOUSE)',
    example: 'DOMESTIC_WAREHOUSE',
  })
  subType: string;

  @ApiProperty({
    description: 'Warehouse type (SALES_WAREHOUSE, RETURN_WAREHOUSE)',
    example: 'SALES_WAREHOUSE',
  })
  type: string;

  @ApiProperty({
    description: 'Address details',
    example: {
      addressLine1: '123 Main St',
      addressLine2: 'Suite 100',
      city: 'Anytown',
      state: 'CA',
      postalCode: '12345',
      region: 'North America',
      regionCode: 'NA',
      contactPerson: 'John Doe',
      phoneNumber: '******-123-4567',
    },
  })
  address: {
    fullAddress?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    region?: string;
    regionCode?: string;
    contactPerson?: string;
    phoneNumber?: string;
  };

  @ApiProperty({
    description: 'Geolocation data',
    example: {
      latitude: '37.7749',
      longitude: '-122.4194',
    },
  })
  geolocation?: {
    latitude?: string;
    longitude?: string;
  };

  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  tiktokShopId: number;

  @ApiProperty({
    description: 'Created date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
