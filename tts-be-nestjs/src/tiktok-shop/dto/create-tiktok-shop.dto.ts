import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  isString,
  IsString,
  Length,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';

export class CreateTikTokShopDto {
  @ApiProperty({
    description: 'An internal identifier for the TikTok Shop',
    example: '7000714532876273420',
  })
  @IsOptional()
  @IsString()
  idTT: string;

  @ApiProperty({
    description: 'The TikTok Shop name',
    example: 'Maomao beauty shop',
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'A user-friendly name for the TikTok Shop',
    example: 'My Main Beauty Store',
    required: false,
  })
  @IsOptional()
  @IsString()
  friendly_name?: string;

  @ApiProperty({
    description: 'The region of the shop',
    example: 'GB',
  })
  @IsOptional()
  @IsString()
  @Length(2, 2)
  region: string;

  @ApiProperty({
    description: 'The type of seller',
    example: 'CROSS_BORDER',
    enum: ['CROSS_BORDER', 'LOCAL'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['CROSS_BORDER', 'LOCAL'])
  sellerType: string;

  @ApiProperty({
    description:
      'An encrypted token used to securely identify a shop in API requests',
    example: 'GCP_XF90igAAAABh00qsWgtvOiGFNqyubMt3',
  })
  @IsOptional()
  @IsString()
  cipher: string;

  @ApiProperty({
    description: 'The TikTok Shop code',
    example: 'CNGBCBA4LLU8',
  })
  @IsOptional()
  @IsString()
  code: string;

  @ApiProperty({
    description:
      'TikTok Shop App application key that was provided from system.',
    example: 'app_key_example',
  })
  @IsString()
  app_key: string;

  @ApiProperty({
    description: 'Temporary authorization code from TikTok',
    example: 'auth_code_example',
  })
  @IsString()
  auth_code: string;

  @ApiProperty({
    description: 'Access token to call TikTok Shop API',
    example: 'access_token_example',
  })
  @IsOptional()
  @IsString()
  access_token: string;

  @ApiProperty({
    description: 'Access token expiry in Unix timestamp (seconds)',
    example: 1712668800,
  })
  @IsOptional()
  @IsNumber()
  access_token_expire_in: number;

  @ApiProperty({
    description: 'Refresh token to generate a new access token',
    example: 'refresh_token_example',
  })
  @IsOptional()
  @IsString()
  refresh_token: string;

  @ApiProperty({
    description: 'Refresh token expiry in Unix timestamp (seconds)',
    example: 1715260800,
  })
  @IsOptional()
  @IsNumber()
  refresh_token_expire_in?: number;
}
