import { Injectable, NotFoundException } from '@nestjs/common';
import { TikTokApplication } from './entities/tiktok-application.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  AccessTokenTool,
  TikTokShopNodeApiClient,
  ClientConfiguration,
} from '../../tts_sdk';

@Injectable()
export class TikTokClientFactory {
  constructor(
    @InjectRepository(TikTokApplication)
    private readonly tiktokAppRepo: Repository<TikTokApplication>,
  ) {}

  async createClientByAppKey(appKey: string): Promise<TikTokShopNodeApiClient> {
    const app = await this.tiktokAppRepo.findOne({
      where: { app_key: appKey },
    });

    if (!app) {
      throw new NotFoundException(
        `Application with app_key=${appKey} not found`,
      );
    }

    const config: ClientConfiguration = {
      app_key: app.app_key,
      app_secret: app.app_secret,
    };

    return new TikTokShopNodeApiClient({ config });
  }
}
