import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  BadRequestException,
  Query,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { TikTokShopService } from './tiktok-shop.service';
import { CreateTikTokShopDto } from './dto/create-tiktok-shop.dto';
import { UpdateTikTokShopDto } from './dto/update-tiktok-shop.dto';
import { UpdateFriendlyNameDto } from './dto/update-friendly-name.dto';
import { TikTokShopResponseDto, TikTokShopCleanResponseDto } from './dto/tiktok-shop-response.dto';
import {
  ApiTags,
  ApiOperation,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { PaginatedResult } from 'src/common/interfaces/paginated-result.interface';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { FilterTiktokShopDto } from './dto/filter-tiktok-shop.dto';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { TikTokApplication } from './entities/tiktok-application.entity';
import { CreateTikTokApplicationDto } from './dto/create-tiktok-application.dto';
import { WarehouseResponseDto } from './dto/warehouse-response.dto';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/auth/enums/user-role.enum';
import { Public } from 'src/auth/decorators/public.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';

@ApiTags('TikTok Shops')
@Controller('tiktok-shops')
export class TikTokShopController {
  private readonly logger = new Logger(TikTokShopController.name);
  constructor(private readonly tikTokShopService: TikTokShopService) {}

  /* Tiktok shop is created from the authorization flow, we don't need create tiktok shop manual.
  @Post()
  @ApiOperation({ summary: 'Create a new TikTok Shop' })
  @ApiCreatedResponse({
    description: 'The TikTok Shop has been successfully created.',
    type: TikTokShopResponseDto,
  })
  create(
    @Body() createTikTokShopDto: CreateTikTokShopDto,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopResponseDto> {
    return this.tikTokShopService.create(createTikTokShopDto, userId);
  }
  */

  @Post('applications')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new TikTok Application' })
  @ApiCreatedResponse({
    description: 'The TikTok Application has been successfully created.',
    type: TikTokApplication,
  })
  async createApplication(
    @Body() dto: CreateTikTokApplicationDto,
  ): Promise<TikTokApplication> {
    return this.tikTokShopService.createApplication(dto);
  }

  @Get('applications')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all TikTok Applications' })
  @ApiOkResponse({
    description: 'List of all TikTok Applications',
    type: [TikTokApplication],
  })
  async getApplications(): Promise<TikTokApplication[]> {
    return this.tikTokShopService.findAllApplications();
  }

  @Public()
  @Get('tiktok-authorize')
  @ApiOperation({ summary: 'Tiktok authorization' })
  @ApiCreatedResponse({
    description: 'The TikTok Shop has been successfully authorized.',
    type: [TikTokShopCleanResponseDto],
  })
  async tiktokAuthorize(
    @Query('app_key') appKey: string,
    @Query('code') authCode: string,
  ) {
    if (!appKey || !authCode) {
      throw new BadRequestException('app_key and code are required');
    }

    const shops = await this.tikTokShopService.handleAuthorizationFlow(
      appKey,
      authCode,
    );
    return {
      message: 'Tiktok Shops authorized and saved successfully',
      data: shops,
    };
  }

  @Get('admin/all')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all TikTok Shops (Admin only)' })
  @ApiOkResponse({
    description: 'List of all TikTok Shops without userId constraint',
    type: [TikTokShopResponseDto],
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Search by tiktok shop name',
  })
  @ApiQuery({
    name: 'code',
    required: false,
    type: String,
    description: 'Search by tiktok shop code',
  })
  @ApiQuery({
    name: 'idTT',
    required: false,
    type: String,
    description: 'Search by tiktok shop id',
  })
  findAllForAdmin(
    @Query() query: any,
  ): Promise<PaginatedResult<TikTokShopResponseDto>> {
    const paginationQuery = plainToInstance(PaginationQueryDto, query);
    const filterDto = plainToInstance(FilterTiktokShopDto, query);
    const errors1 = validateSync(paginationQuery);
    const errors2 = validateSync(filterDto);

    if (errors1.length || errors2.length) {
      throw new BadRequestException([...errors1, ...errors2]);
    }
    return this.tikTokShopService.findAllForAdmin(paginationQuery, filterDto);
  }

  @Get('authorization-link')
  @ApiOperation({ summary: 'Generate TikTok Shop authorization link' })
  @ApiOkResponse({
    description: 'Authorization link and instructions',
    schema: {
      type: 'object',
      properties: {
        authorizationUrl: {
          type: 'string',
          example: 'https://services.us.tiktokshop.com/open/authorize?service_id=7420413125780145962'
        },
        instructions: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'Open this link in the same browser where you are logged into TikTok Shop',
            'Review and approve the authorization request',
            'You will be redirected back to our application',
            'Copy your shop codes for manual connection'
          ]
        }
      }
    }
  })
  generateAuthorizationLink(
    @CurrentUser('id') userId: number,
  ) {
    this.logger.log(`generateAuthorizationLink for user: ${userId}`);
    return this.tikTokShopService.generateAuthorizationLink(userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all TikTok Shops' })
  @ApiOkResponse({
    description: 'List of all TikTok Shops',
    type: [TikTokShopCleanResponseDto],
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Search by tiktok shop name',
  })
  @ApiQuery({
    name: 'code',
    required: false,
    type: String,
    description: 'Search by tiktok shop code',
  })
  findAll(
    @Query() query: any,
    @CurrentUser('id') userId: number,
  ): Promise<PaginatedResult<TikTokShopCleanResponseDto>> {
    const paginationQuery = plainToInstance(PaginationQueryDto, query);
    const filterDto = plainToInstance(FilterTiktokShopDto, query);
    const errors1 = validateSync(paginationQuery);
    const errors2 = validateSync(filterDto);

    if (errors1.length || errors2.length) {
      throw new BadRequestException([...errors1, ...errors2]);
    }
    return this.tikTokShopService.findAll(paginationQuery, filterDto, userId);
  }

  @Get('tiktok/:idTT')
  @ApiOperation({ summary: 'Get a TikTok Shop by TikTok ID' })
  @ApiParam({ name: 'idTT', description: "TikTok's internal identifier" })
  @ApiOkResponse({
    description: 'The found TikTok Shop',
    type: TikTokShopResponseDto,
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  findByTikTokId(
    @Param('idTT') idTT: string,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopResponseDto> {
    return this.tikTokShopService.findByTikTokId(idTT, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a TikTok Shop by ID' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'The found TikTok Shop',
    type: TikTokShopCleanResponseDto,
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    return this.tikTokShopService.findOne(id, userId);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a TikTok Shop' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'The updated TikTok Shop',
    type: TikTokShopCleanResponseDto,
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTikTokShopDto: UpdateTikTokShopDto,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    return this.tikTokShopService.update(id, updateTikTokShopDto, userId);
  }

  @Patch(':id/friendly-name')
  @ApiOperation({ summary: 'Update TikTok Shop friendly name' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'The TikTok Shop with updated friendly name',
    type: TikTokShopCleanResponseDto,
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  updateFriendlyName(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateFriendlyNameDto: UpdateFriendlyNameDto,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    return this.tikTokShopService.updateFriendlyName(id, updateFriendlyNameDto.friendly_name, userId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a TikTok Shop' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiNoContentResponse({
    description: 'The TikTok Shop has been successfully deleted',
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<void> {
    return this.tikTokShopService.remove(id, userId);
  }

  @Get(':id/warehouses')
  @ApiOperation({ summary: 'Get warehouses for a TikTok Shop' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'List of warehouses for the TikTok Shop',
    type: [WarehouseResponseDto],
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  getWarehouses(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<WarehouseResponseDto[]> {
    return this.tikTokShopService.getWarehouses(id, userId);
  }

  @Get(':id/warehouses/sync')
  @ApiOperation({ summary: 'Synchronize warehouses from TikTok Shop API' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'List of synchronized warehouses',
    type: [WarehouseResponseDto],
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found' })
  synchronizeWarehouses(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<WarehouseResponseDto[]> {
    return this.tikTokShopService.synchronizeWarehouses(id, userId);
  }

  @Post('connect-shop')
  @ApiOperation({ summary: 'Connect shop by code to user' })
  @ApiCreatedResponse({
    description: 'Shop successfully connected to user',
    type: TikTokShopResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Shop not found or already connected' })
  connectShop(
    @Body('shopCode') shopCode: string,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopResponseDto> {
    return this.tikTokShopService.connectShopToUser(shopCode, userId);
  }

  @Post(':id/refresh-token')
  @ApiOperation({ summary: 'Refresh TikTok Shop access token immediately' })
  @ApiParam({ name: 'id', description: 'TikTok Shop ID (numeric)' })
  @ApiOkResponse({
    description: 'Token successfully refreshed',
    type: TikTokShopResponseDto,
  })
  @ApiNotFoundResponse({ description: 'TikTok Shop not found or access denied' })
  @ApiBadRequestResponse({ description: 'Token refresh failed' })
  refreshTokenTiktok(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<TikTokShopCleanResponseDto> {
    return this.tikTokShopService.refreshTokenTiktok(id, userId);
  }
}
