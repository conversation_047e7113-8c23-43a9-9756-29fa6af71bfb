# Swagger UI Configuration

## Overview

This document explains how Swagger UI is configured in the TikTok Shop NestJS backend to ensure it's only available in development mode and completely disabled in production for security reasons.

## Configuration Details

### Environment-Based Swagger Setup

The Swagger UI is conditionally enabled based on the `NODE_ENV` environment variable:

- **Development Mode** (`NODE_ENV=development`): Swagger UI is enabled and accessible
- **Production Mode** (`NODE_ENV=production`): Swagger UI is completely disabled

### Implementation

#### 1. Main Application Configuration (`src/main.ts`)

```typescript
// Configure Swagger only in development mode
if (process.env.NODE_ENV === 'development') {
  const config = new DocumentBuilder()
    .setTitle('TikTok Shop API')
    .setDescription('TikTok Shop API description')
    .setVersion('1.0')
    .addBearerAuth() // Add JWT authentication support
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);
  
  console.log('🚀 Swagger UI is available at: http://localhost:' + (process.env.PORT ?? 3000) + '/api');
} else {
  console.log('🔒 Swagger UI is disabled in production mode for security');
}
```

#### 2. Environment Variables

The configuration relies on the `NODE_ENV` environment variable:

```bash
# Development
NODE_ENV=development

# Production
NODE_ENV=production
```

#### 3. Nginx Configuration (Production)

For additional security in production, the Nginx configuration blocks access to Swagger endpoints:

```nginx
# Block Swagger API documentation in production for security
location /api {
    return 404;
}

# Block any Swagger-related endpoints
location ~* ^/(swagger|docs|api-docs) {
    return 404;
}
```

## Access Points

### Development Mode
- **Swagger UI**: `http://localhost:3000/api`
- **Swagger JSON**: `http://localhost:3000/api-json`

### Production Mode
- **Swagger UI**: Not accessible (404 error)
- **Swagger JSON**: Not accessible (404 error)

## Security Benefits

1. **Reduced Attack Surface**: Swagger UI is not exposed in production
2. **Information Disclosure Prevention**: API documentation doesn't reveal internal structure
3. **Performance**: No overhead from Swagger documentation generation in production
4. **Compliance**: Meets security best practices for production deployments

## Testing the Configuration

### Local Development Testing

1. **Test Development Mode**:
   ```bash
   # Set environment to development
   export NODE_ENV=development
   npm run start:dev
   
   # Access Swagger UI
   curl -I http://localhost:3000/api
   # Should return 200 OK
   ```

2. **Test Production Mode**:
   ```bash
   # Set environment to production
   export NODE_ENV=production
   npm run start:prod
   
   # Try to access Swagger UI
   curl -I http://localhost:3000/api
   # Should return 404 Not Found
   ```

### Production Deployment Testing

After deploying to AWS EC2:

```bash
# Test that Swagger is blocked
curl -I https://be.tiktokshop.expert/api
# Should return 404 Not Found

# Test that main API still works
curl -I https://be.tiktokshop.expert/health
# Should return 200 OK
```

## Deployment Considerations

### AWS EC2 Deployment

1. **Environment Variables**: Ensure `NODE_ENV=production` is set in the production environment
2. **Nginx Configuration**: The updated Nginx config blocks Swagger endpoints
3. **PM2 Configuration**: Production PM2 setup should use `NODE_ENV=production`

### Environment File Setup

**Development (.env)**:
```bash
NODE_ENV=development
PORT=3000
# ... other development configs
```

**Production (.env.production)**:
```bash
NODE_ENV=production
PORT=3000
# ... other production configs
```

## Troubleshooting

### Common Issues

1. **Swagger Still Accessible in Production**:
   - Check `NODE_ENV` environment variable
   - Verify Nginx configuration is applied
   - Restart the application and Nginx

2. **Swagger Not Working in Development**:
   - Ensure `NODE_ENV=development`
   - Check console logs for Swagger setup messages
   - Verify no Nginx blocking rules in development

3. **404 Errors for API Endpoints**:
   - Ensure you're not trying to access `/api` in production
   - Use actual API endpoints like `/products`, `/auth`, etc.

### Verification Commands

```bash
# Check current NODE_ENV
echo $NODE_ENV

# Check if Swagger is accessible
curl -I http://localhost:3000/api

# Check application logs
pm2 logs tiktokshop-backend
```

## Best Practices

1. **Always use environment variables** to control feature flags
2. **Test both development and production modes** before deployment
3. **Use Nginx as an additional security layer** in production
4. **Monitor logs** to ensure Swagger is properly disabled in production
5. **Document API endpoints** separately for production use

## Related Files

- `src/main.ts` - Main Swagger configuration
- `.env.example` - Environment variable documentation
- `backup/deploy/nginx-tiktokshop.conf` - Nginx production configuration
- `backup/deploy/user-data.sh` - EC2 deployment script
