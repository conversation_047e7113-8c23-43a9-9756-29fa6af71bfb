# User Data Access Control Implementation Plan

This document outlines the plan for implementing user data access control in the TikTok Shop application. The goal is to ensure that users can only access their own data by adding a `userId` column to relevant tables and enforcing access control at the API level.

## Overview

Currently, the application does not restrict data access based on the user who created it. This implementation will add user-based access control to ensure data security and proper multi-tenant functionality.

## Implementation Strategy

We will implement this feature incrementally, starting with a single entity (TikTok Shop) as a proof of concept before extending to other entities. This approach allows us to validate the solution and address any issues before wider implementation.

## Key Components

1. **Entity Updates**: Add `userId` column to relevant entities
2. **CurrentUser Decorator**: Create a custom decorator to extract user information from JWT tokens
3. **Controller Updates**: Use the decorator to get user information and pass it to services
4. **Service Updates**: Modify queries to filter data by user ID
5. **Testing**: Ensure the implementation works as expected

## Detailed Tasks

### Phase 1: TikTok Shop Entity (Proof of Concept)

- [x] Update TikTok Shop entity to include userId field
- [x] Create CurrentUser decorator
- [x] Update TikTok Shop controller to use CurrentUser decorator
- [x] Update TikTok Shop service to filter data by userId
- [x] Test the implementation

### Phase 2: Extend to Other Entities

- [x] Update Product entity to include userId field
- [x] Update StagedProduct entity to include userId field
- [ ] ~~Update ProductUpload entity to include userId field~~ (Decided not to add userId to ProductUpload)
- [x] Update Product controller to use CurrentUser decorator
- [x] Update Product service to filter data by userId
- [x] Update StagedProduct controller to use CurrentUser decorator
- [x] Update StagedProduct service to filter data by userId
- [x] Update ProductUpload controller to use CurrentUser decorator
- [x] Update ProductUpload service to verify access through StagedProduct
- [ ] Test the implementation

### Phase 3: Finalize and Document

- [ ] Review and refine implementation
- [ ] Update API documentation
- [ ] Create user guide for developers
- [ ] Final testing

## Technical Details

### CurrentUser Decorator

```typescript
// src/auth/decorators/current-user.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const CurrentUser = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    // If data is provided, return the specific property
    // Example: @CurrentUser('id') will return user.id
    return data ? user?.[data] : user;
  },
);
```

### Entity Update Example

```typescript
// Example for TikTok Shop entity
@Entity('tiktok_shops')
export class TikTokShop {
  // Existing fields...

  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
```

### Controller Usage Example

```typescript
@Get()
async findAll(@CurrentUser('id') userId: number) {
  return this.tikTokShopService.findAll(userId);
}
```

### Service Update Example

```typescript
async findAll(userId: number) {
  return this.tikTokShopRepository.find({
    where: { userId },
  });
}
```

## Implementation Notes

1. **Development Environment**: Since we're using `synchronize: true` in development, we'll skip creating migrations and directly update the entities.

2. **Target Entities**:
   - TikTok Shop (`tiktok_shops`) - ✅ Implemented
   - Product (`products`) - ✅ Implemented
   - Staged Product (`staged_products`) - ✅ Implemented
   - Product Upload (`product_upload`) - ❌ Not needed (access controlled via StagedProduct)

3. **Incremental Approach**: Started with TikTok Shop entity as a test case, then extended to Product and StagedProduct.

4. **Frontend Considerations**: The userId is handled entirely on the backend, keeping it hidden from the frontend.

5. **ProductUpload Access Control**: Instead of adding a userId column to ProductUpload, we verify access by checking if the associated StagedProduct belongs to the user. This approach:
   - Reduces data redundancy
   - Maintains a clear ownership hierarchy
   - Simplifies data synchronization

## Security Considerations

1. **Data Isolation**: Ensure complete isolation of data between users
2. **Error Handling**: Provide appropriate error messages without revealing sensitive information
3. **Edge Cases**: Handle cases where a user tries to access another user's data
4. **Testing**: Include security testing to verify data isolation

## Timeline

- **Week 1**: Implement Phase 1 (TikTok Shop entity)
- **Week 2**: Implement Phase 2 (Other entities)
- **Week 3**: Implement Phase 3 (Finalization and documentation)

## Success Criteria

1. Users can only access their own data
2. API endpoints properly filter data based on the authenticated user
3. No performance degradation from the added filtering
4. Clean and maintainable code implementation
5. Comprehensive test coverage

## Future Enhancements

1. **Role-Based Access Control**: Extend the system to support different access levels based on user roles
2. **Audit Logging**: Track data access and modifications for security purposes
3. **Shared Access**: Implement features to allow controlled sharing of data between users
