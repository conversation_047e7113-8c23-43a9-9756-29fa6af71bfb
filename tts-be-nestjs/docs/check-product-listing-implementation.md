# Check Product Listing Implementation Plan

## Overview

This document outlines the implementation plan for integrating TikTok Shop's Product Listing Check API (`ProductV202309Api.ProductsListingCheckPost`) into our product upload flow. This feature will validate product data before attempting to create products, reducing failed uploads and improving user experience.

## API Reference

- **API Endpoint**: `ProductV202309Api.ProductsListingCheckPost`
- **Documentation**: [TikTok Shop Product Listing Check](https://partner.tiktokshop.com/docv2/page/650a0ee8f1fd3102b91c6493?external_id=650a0ee8f1fd3102b91c6493)

## Implementation Tasks

### 1. Create DTOs for Validation Results

- [ ] Create `ProductValidationResultDto` to structure validation responses
- [ ] Create `ValidationErrorDto` for individual validation errors
- [ ] Create `ValidationWarningDto` for non-blocking warnings

### 2. Implement Image Processing with Retry Logic

- [ ] Add robust image processing method to `ProductUploadQueueProcessor`
- [ ] Implement retry logic for failed image uploads
- [ ] Update staged product with image URIs after successful uploads

### 3. Implement Validation Service Method

- [ ] Add `checkProductListing` method to `ProductUploadQueueProcessor`
- [ ] Format staged product data for the validation API
- [ ] Call TikTok Shop API and process results
- [ ] Return structured validation results

### 4. Update Product Upload Flow

- [ ] Keep `createProductUpload` simple - just create record and queue job
- [ ] Move image processing and validation to the background job
- [ ] Add options for skipping validation or forcing upload despite warnings
- [ ] Update product upload status with validation results

### 5. Create API Endpoint for Validation Status

- [ ] Add endpoint to check validation status of a product upload
- [ ] Return detailed validation errors and warnings

### 6. Update UI Integration

- [ ] Add validation feedback in the product creation UI
- [ ] Display validation errors and warnings clearly
- [ ] Allow users to force upload with warnings if desired

## Detailed Implementation Plan

### ProductUploadService Changes

```typescript
// Simplified createProductUpload method
async createProductUpload(
  createProductUploadDto: CreateProductUploadDto,
  options?: { skipValidation?: boolean, forceUpload?: boolean }
): Promise<ProductUploadResponseDto> {
  // 1. Create product upload record with PENDING status
  // 2. Queue background job for processing
  // 3. Return product upload record with job ID
}
```

### ProductUploadQueueProcessor Changes

```typescript
// Process images with retry logic
async processImages(stagedProduct, tiktokShop) {
  const MAX_RETRIES = 3;

  for (const image of stagedProduct.images) {
    if (!image.uri && (image.imageData || image.imageUrl)) {
      let retries = 0;
      let success = false;

      while (retries < MAX_RETRIES && !success) {
        try {
          // Upload image and store URI
          success = true;
        } catch (error) {
          retries++;
          // Wait before retrying
        }
      }

      if (!success) {
        throw new Error(`Failed to upload image after ${MAX_RETRIES} attempts`);
      }
    }
  }

  // Save updated staged product with image URIs
  return updatedStagedProduct;
}

// Validate product with TikTok API
async checkProductListing(stagedProduct, tiktokShop) {
  // 1. Format data for validation API
  // 2. Call ProductV202309Api.ProductsListingCheckPost
  // 3. Process and return validation results
}

// Main job handler
async handleCreateProduct(job) {
  // 1. Update status to IN_PROGRESS
  // 2. Process images with retry logic
  // 3. Validate product if not skipping validation
  // 4. Create product if validation passes or force upload
  // 5. Update product upload status with results
}
```

### Flow Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────────────────────────────┐
│  Create/Update  │     │ Create Upload   │     │           Background Job                │
│ Staged Product  │────▶│ Record & Queue  │────▶│                                         │
└─────────────────┘     └─────────────────┘     │  ┌─────────┐    ┌─────────┐    ┌─────┐  │
                                                 │  │ Process │    │Validate │    │Create│  │
                                                 │  │ Images  │───▶│ Product │───▶│Product│  │
                                                 │  │(w/retry)│    │         │    │      │  │
                                                 │  └─────────┘    └─────────┘    └─────┘  │
                                                 └─────────────────────────────────────────┘
                                                                     │
                                                                     ▼
                                                           ┌─────────────────────┐
                                                           │ Update Upload Status│
                                                           │ with Results       │
                                                           └─────────────────────┘
```

## Benefits

1. **Reduced Failed Uploads**: Catch validation errors before attempting to create products
2. **Improved User Experience**: Provide feedback on product data issues
3. **Efficiency**: Process everything in background jobs to avoid timeouts
4. **Reliability**: Implement retry logic for image uploads
5. **Scalability**: Handle large products with many images without blocking

## Next Steps

1. Implement the validation DTOs
2. Add robust image processing with retry logic to `ProductUploadQueueProcessor`
3. Add the `checkProductListing` method to `ProductUploadQueueProcessor`
4. Update the product upload flow to process everything in background
5. Create endpoints for checking validation status
6. Test with various product scenarios including image upload failures
