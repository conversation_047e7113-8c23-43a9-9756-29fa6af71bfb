# Temporary File Storage

This document describes the temporary file storage system used for handling image uploads in the TikTok Shop application.

## Overview

The application uses a temporary file storage system to handle image uploads efficiently. Instead of storing large base64 encoded image data in the database, the system stores images as temporary files on disk and only keeps references to these files in the database.

## Configuration

The temporary file storage system can be configured using environment variables:

```
# Temporary files directory (optional)
# If not provided, the system's temp directory will be used
TEMP_FILES_DIR=/path/to/temp/files
```

If `TEMP_FILES_DIR` is not provided, the system will use a subdirectory in the system's temporary directory (`os.tmpdir()`).

## Features

### 1. Configurable Temporary Directory

The temporary directory can be configured using the `TEMP_FILES_DIR` environment variable. This allows for flexibility in deployment environments.

### 2. File Type Detection and Validation

The system automatically detects and validates file types to ensure only image files are stored. The following image types are supported:

- JPEG/JPG
- PNG
- GIF
- WebP
- HEIC
- BMP
- TIFF

Files that are not recognized as images or are not in the supported formats will be rejected.

### 3. File Size Validation

The system enforces a maximum file size limit of 5MB for all uploaded images. Files exceeding this limit will be rejected with an appropriate error message. This helps prevent excessive disk usage and ensures optimal performance.

### 4. Automatic Cleanup

The system includes an automatic cleanup mechanism that runs daily at midnight to remove old temporary files. By default, files older than 24 hours are removed.

## Usage

The `TempFileStorageService` provides the following methods:

### `storeBase64Image(base64Data: string): Promise<string>`

Stores a base64-encoded image as a temporary file and returns the path to the file.

### `storeImageFromUrl(imageUrl: string): Promise<string>`

Downloads an image from a URL, stores it as a temporary file, and returns the path to the file.

### `readTempFile(filePath: string): Buffer`

Reads a temporary file and returns its contents as a Buffer.

### `createReadStream(filePath: string): fs.ReadStream`

Creates a read stream for a temporary file.

### `deleteTempFile(filePath: string): void`

Deletes a temporary file.

### `cleanupOldTempFiles(maxAgeHours = 24): void`

Cleans up temporary files older than the specified number of hours.

## Implementation Details

1. When a staged product is created or updated, any image data is stored as temporary files.
2. The paths to these temporary files are stored in the database instead of the actual image data.
3. When the product is uploaded to TikTok Shop, the temporary files are used for the upload.
4. After successful upload, the temporary files are automatically cleaned up.
5. A scheduled task runs daily to clean up any orphaned temporary files.

## Benefits

1. **Reduced Database Size**: By storing image data as temporary files instead of in the database, we significantly reduce the database size and improve performance.
2. **Improved Memory Usage**: By not keeping large base64 strings in memory, we reduce the memory footprint of the application.
3. **Efficient Resource Management**: Temporary files are automatically cleaned up after use and through a scheduled task, ensuring efficient resource management.
4. **File Type Validation**: Only valid image files are accepted, improving security and reliability.
5. **File Size Validation**: A 5MB size limit prevents excessive disk usage and ensures optimal performance.
