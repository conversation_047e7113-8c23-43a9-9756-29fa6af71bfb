# Backend Authentication Implementation Guide

This guide provides instructions for implementing the authentication system in your NestJS backend to support NextAuth.js in the frontend.

## Overview

The authentication system consists of:

1. User management (create, find users)
2. OAuth authentication (Google)
3. Magic link email authentication
4. JWT token generation and validation

## Implementation Steps

### 1. Install Required Dependencies

```bash
npm install --save @nestjs/jwt passport passport-jwt nodemailer uuid
npm install --save-dev @types/passport-jwt @types/nodemailer @types/uuid
```

### 2. Configure Environment Variables

Copy the environment variables from `.env.auth.example` to your `.env` file:

```
# JWT
JWT_SECRET=your-jwt-secret-key-here

# Email
EMAIL_SERVER_HOST=smtp.example.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
```

Generate a secure random string for `JWT_SECRET`:

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 3. Set Up Database Tables

Run the migration to create the necessary tables:

```bash
npm run migration:run
```

Or add the entities to your TypeORM configuration to auto-create the tables.

### 4. Import the Auth Module

Add the `AuthModule` to your `app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './auth/auth.module';
import { MailModule } from './mail/mail.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: configService.get<string>('DATABASE_URL'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: process.env.NODE_ENV !== 'production',
      }),
    }),
    AuthModule,
    MailModule,
  ],
})
export class AppModule {}
```

### 5. Set Up Email Service

Configure your email service provider. You can use:

- [Mailtrap](https://mailtrap.io/) for testing
- [SendGrid](https://sendgrid.com/)
- [Mailgun](https://www.mailgun.com/)
- Your own SMTP server

Update the `EMAIL_*` environment variables accordingly.

### 6. Implement JWT Strategy for API Protection

Create a JWT strategy to protect your API endpoints:

```typescript
// src/auth/strategies/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    const user = await this.usersRepository.findOne({
      where: { id: payload.sub },
    });

    if (!user) {
      throw new UnauthorizedException();
    }

    return user;
  }
}
```

Add the strategy to your `auth.module.ts`:

```typescript
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  // ...
  providers: [AuthService, JwtStrategy],
  // ...
})
export class AuthModule {}
```

### 7. Create a Guard for Protected Routes

```typescript
// src/auth/guards/jwt-auth.guard.ts
import { Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {}
```

### 8. Protect Your API Routes

Use the guard to protect your API routes:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('products')
export class ProductsController {
  @UseGuards(JwtAuthGuard)
  @Get()
  findAll() {
    // This route is protected
    return ['product1', 'product2'];
  }
}
```

### 9. Test the Authentication Flow

1. Start your backend server
2. Configure your frontend to use the backend API
3. Test Google OAuth login
4. Test magic link email login

## Security Considerations

1. Always use HTTPS in production
2. Set appropriate token expiration times
3. Implement rate limiting for the magic link endpoint to prevent abuse
4. Store tokens securely and never log them
5. Use a strong JWT secret and consider rotating it periodically
6. Validate all input data
7. Implement CORS to restrict access to your API

## Troubleshooting

### Email Not Sending

- Check your SMTP configuration
- Verify that your email provider allows sending from your server
- Check spam folders

### JWT Token Issues

- Ensure the JWT secret is the same in both frontend and backend
- Check token expiration times
- Verify that the token is being sent in the Authorization header

### Database Connection Issues

- Verify your database connection string
- Ensure the database user has the necessary permissions
- Check that the tables have been created correctly
