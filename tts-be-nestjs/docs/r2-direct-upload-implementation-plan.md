# Implementation Plan for Direct File Uploads to R2 Storage

## Current Implementation Analysis

After reviewing the codebase, I can see that:

1. There's already a `CloudStorageService` that handles R2 integration with methods for uploading files, base64 data, and generating signed URLs.
2. There's an `ImageUploadController` and `ImageUploadService` that handle uploading images to TikTok Shop.
3. The `TempFileStorageService` manages temporary files for processing.
4. The current implementation already supports R2 storage with the `r2Key` property in image objects.
5. The entities (`staged-product.entity.ts` and `template.entity.ts`) currently include `imageData` and `tempFilePath` properties which need to be removed.

The issue is that the frontend is still converting images to base64 and sending them in the request body, causing 413 (Payload Too Large) errors.

## Proposed Solution

Enhance the existing implementation to support direct file uploads to R2 storage:

1. Add new endpoints to the `ImageUploadController` for direct file uploads to R2
2. Modify the entities to remove `imageData` and `tempFilePath` properties

## Files to Modify

### Backend Changes

1. **Enhance ImageUploadController**:
   - Add new endpoints for direct file uploads to R2
   - Add endpoint for generating presigned URLs for client-side uploads

2. **Update DTOs**:
   - Create new DTOs for R2 file upload requests and responses

3. **Entity Modifications**:
   - Modify `staged-product.entity.ts` to remove `imageData` and `tempFilePath` properties from the `images` and `sizeChart` columns
   - Modify `template.entity.ts` to remove `imageData` and `tempFilePath` properties from the `images` and `sizeChart` columns

4. **CloudStorageService**:
   - Add method for generating presigned URLs for uploads

5. **TempFileStorageService**:
   - Make `validateImageBuffer` method public



## Detailed Implementation Plan

### 1. Backend: Enhance ImageUploadController

#### 1.1. Add New Endpoints to ImageUploadController

Add two new endpoints to the existing `ImageUploadController`:

1. `POST /products/images/upload-to-r2`: Upload a file directly to R2
2. `POST /products/images/presigned-url`: Generate a presigned URL for client-side uploads

```typescript
// Add these new endpoints to the existing ImageUploadController

@Post('upload-to-r2')
@ApiOperation({ summary: 'Upload a file directly to R2 storage' })
@ApiConsumes('multipart/form-data')
@UseInterceptors(FileInterceptor('file'))
async uploadFileToR2(
  @UploadedFile() file: Express.Multer.File,
  @Body('folder') folder: string = 'uploads',
): Promise<R2UploadResponseDto> {
  if (!file) {
    throw new BadRequestException('No file uploaded');
  }

  this.logger.log(`Uploading file ${file.originalname} to R2 in folder ${folder}`);

  // Validate file type and size
  await this.tempFileStorageService.validateImageBuffer(file.buffer);

  // Generate a key for the file
  const key = `${folder}/${Date.now()}-${uuid()}-${file.originalname}`;

  // Upload to R2
  const result = await this.cloudStorageService.uploadFile(
    file.buffer,
    key,
    file.mimetype
  );

  return {
    key: result.key,
    url: result.url,
    filename: file.originalname,
    size: file.size,
    mimetype: file.mimetype,
  };
}

@Post('presigned-url')
@ApiOperation({ summary: 'Generate a presigned URL for client-side uploads to R2' })
async getPresignedUrl(
  @Body() dto: R2UploadRequestDto,
): Promise<R2PresignedUrlResponseDto> {
  this.logger.log(`Generating presigned URL for ${dto.fileName} in folder ${dto.folder || 'uploads'}`);

  // Generate a key for the file
  const key = `${dto.folder || 'uploads'}/${Date.now()}-${uuid()}-${dto.fileName}`;

  // Generate a presigned URL for uploading
  const presignedUrl = await this.cloudStorageService.generatePresignedUploadUrl(
    key,
    dto.contentType,
    3600 // 1 hour expiration
  );

  return {
    key: key,
    presignedUrl: presignedUrl,
    publicUrl: `${this.cloudStorageService.getPublicUrl()}/${key}`,
    expiresIn: 3600,
  };
}
```

### 2. Add generatePresignedUploadUrl Method to CloudStorageService

```typescript
// Add this method to the existing CloudStorageService

/**
 * Generate a presigned URL for uploading a file to R2
 * @param key The key (filename) for the file
 * @param contentType The content type of the file
 * @param expirationSeconds How long the URL should be valid for
 * @returns A presigned URL for uploading
 */
async generatePresignedUploadUrl(
  key: string,
  contentType: string,
  expirationSeconds = 3600
): Promise<string> {
  try {
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      ContentType: contentType,
    });

    return await getSignedUrl(this.s3Client, command, { expiresIn: expirationSeconds });
  } catch (error) {
    this.logger.error(`Error generating presigned upload URL: ${error.message}`, error.stack);
    throw new Error(`Failed to generate presigned upload URL: ${error.message}`);
  }
}

/**
 * Get the public URL base for the R2 bucket
 * @returns The public URL base
 */
getPublicUrl(): string {
  return this.publicUrl;
}
```

### 3. Create New DTOs

```typescript
// Add these DTOs to a new file or extend the existing upload-image.dto.ts

export class R2UploadRequestDto {
  @ApiProperty({
    description: 'File name',
    example: 'image.jpg',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    description: 'Content type',
    example: 'image/jpeg',
  })
  @IsString()
  @IsNotEmpty()
  contentType: string;

  @ApiProperty({
    description: 'Folder path (optional)',
    example: 'product-images',
    required: false,
  })
  @IsString()
  @IsOptional()
  folder?: string;
}

export class R2PresignedUrlResponseDto {
  @ApiProperty({
    description: 'Object key in R2 storage',
    example: 'product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Presigned URL for uploading',
    example: 'https://example.r2.cloudflarestorage.com/bucket/key?X-Amz-Algorithm=...',
  })
  presignedUrl: string;

  @ApiProperty({
    description: 'Public URL for accessing the file after upload',
    example: 'https://pub-123456.r2.dev/product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  publicUrl: string;

  @ApiProperty({
    description: 'Expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;
}

export class R2UploadResponseDto {
  @ApiProperty({
    description: 'Object key in R2 storage',
    example: 'product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Public URL for accessing the file',
    example: 'https://pub-123456.r2.dev/product-images/1621234567890-123e4567-e89b-12d3-a456-426614174000-image.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'image.jpg',
  })
  filename: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
  })
  size: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
  })
  mimetype: string;
}
```

### 4. Make TempFileStorageService.validateImageBuffer Public

```typescript
// Change from private to public in TempFileStorageService
public async validateImageBuffer(buffer: Buffer): Promise<void> {
  // Existing implementation
}
```

### 5. Update Entity Files

#### 5.1. Update staged-product.entity.ts

```typescript
// Update the images and sizeChart properties in staged-product.entity.ts

@ApiProperty({ description: 'Product images' })
@Column('jsonb')
images: Array<{
  imageUrl?: string;
  r2Key?: string;
}>;

@ApiProperty({ description: 'Size chart information', required: false })
@Column('jsonb', { nullable: true })
sizeChart?: {
  imageUrl?: string;
  r2Key?: string;
};
```

#### 5.2. Update template.entity.ts

```typescript
// Update the images and sizeChart properties in template.entity.ts

@ApiProperty({ description: 'Product images' })
@Column('jsonb')
images: Array<{
  imageUrl?: string;
  r2Key?: string;
}>;

@ApiProperty({ description: 'Size chart information template', required: false })
@Column('jsonb', { nullable: true })
sizeChart?: {
  imageUrl?: string;
  r2Key?: string;
};
```

## Implementation Strategy

1. **Add New Endpoints**: Add the new endpoints to the existing `ImageUploadController`
2. **Add New Method**: Add the `generatePresignedUploadUrl` method to `CloudStorageService`
3. **Create New DTOs**: Create the new DTOs for R2 file uploads
4. **Make validateImageBuffer Public**: Update the `TempFileStorageService` to make the validation method public
5. **Update Entity Files**: Remove `imageData` and `tempFilePath` properties from the entity files
6. **Test the New Endpoints**: Ensure the new endpoints work correctly

## Migration Considerations

1. **Backward Compatibility**: Maintain backward compatibility during the transition by supporting both approaches temporarily
2. **Database Migration**: Consider creating a migration to update existing records if needed

## Conclusion

This implementation plan provides a comprehensive approach to changing how we handle image uploads in the backend for the staged-product and template creation flows. By implementing direct file uploads to R2 storage, we can avoid the 413 (Payload Too Large) errors caused by sending base64-encoded images in the request body.

The plan leverages existing code and adds new functionality for direct file uploads to R2 storage. By adding new endpoints to the existing `ImageUploadController` and updating the entity files, we can provide a backend API that supports direct file uploads to R2 storage.
