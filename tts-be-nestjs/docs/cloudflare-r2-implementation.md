# Cloudflare R2 Storage Implementation Guide

## Overview

This document outlines the implementation of Cloudflare R2 Storage for the TikTok Shop application. We'll be using R2 to store Staged-Product images instead of storing them on the backend server. This approach will improve scalability, reduce server load, and provide more reliable image storage.

**Key Benefits:**
- Offload file storage from backend server
- Improved scalability for image-heavy operations
- Better reliability and availability
- Simplified backup and disaster recovery
- Cost-effective storage solution

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Setting Up Cloudflare R2](#setting-up-cloudflare-r2)
3. [Implementation Plan](#implementation-plan)
4. [Progress Tracking](#progress-tracking)
5. [Usage Guidelines](#usage-guidelines)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

- Cloudflare account with R2 access
- AWS SDK for JavaScript/TypeScript
- NestJS backend application
- Node.js v14+ environment

## Setting Up Cloudflare R2

### 1. Create a Cloudflare Account (if you don't have one)

1. Go to [Cloudflare](https://www.cloudflare.com/) and sign up for an account
2. Verify your email address

### 2. Enable R2 Storage

1. Log in to your Cloudflare dashboard
2. Navigate to "R2" from the sidebar
3. If this is your first time using R2, you'll need to set up billing (R2 offers 10GB of storage and 10 million Class A operations for free)
4. Click "Create bucket"
5. Name your bucket (e.g., `product-images`)
6. Choose a region close to your primary user base
7. Click "Create bucket" to finalize

### 3. Create API Tokens

1. In the R2 dashboard, go to "Manage R2 API Tokens"
2. Click "Create API Token"
3. Name your token (e.g., "TikTok Shop Backend")
4. Set permissions to "Edit" for full access
5. Optionally, restrict access to specific buckets
6. Click "Create API Token"
7. Save the Access Key ID and Secret Access Key securely - **these will only be shown once**

### 4. Configure CORS (if needed)

1. Select your bucket from the R2 dashboard
2. Go to the "Settings" tab
3. Scroll down to "Cross-Origin Resource Sharing (CORS)"
4. Click "Add Rule"
5. Configure the rule to allow access from your domains
6. Example configuration:
   ```json
   {
     "AllowedOrigins": ["https://your-frontend-domain.com"],
     "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
     "AllowedHeaders": ["*"],
     "MaxAgeSeconds": 3000
   }
   ```
7. Click "Save"

## Implementation Plan

### Phase 1: Setup and Configuration

1. Install required dependencies:
   ```bash
   npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
   ```

2. Create a CloudStorageModule in the common directory:
   ```typescript
   // src/common/cloud-storage/cloud-storage.module.ts
   import { Module } from '@nestjs/common';
   import { CloudStorageService } from './cloud-storage.service';

   @Module({
     providers: [CloudStorageService],
     exports: [CloudStorageService],
   })
   export class CloudStorageModule {}
   ```

3. Add CloudStorageModule to CommonModule:
   ```typescript
   // src/common/common.module.ts
   import { Global, Module } from '@nestjs/common';
   import { TempFileStorageService } from './services/temp-file-storage.service';
   import { CloudStorageModule } from './cloud-storage/cloud-storage.module';

   @Global()
   @Module({
     imports: [CloudStorageModule],
     providers: [TempFileStorageService],
     exports: [TempFileStorageService, CloudStorageModule],
   })
   export class CommonModule {}
   ```

4. Configure environment variables for R2 credentials in `.env`:
   ```
   R2_ACCOUNT_ID=your_cloudflare_account_id
   R2_ACCESS_KEY_ID=your_access_key_id
   R2_SECRET_ACCESS_KEY=your_secret_access_key
   R2_BUCKET_NAME=your_bucket_name
   R2_PUBLIC_URL=https://<bucket-name>.<account-id>.r2.cloudflarestorage.com
   ```

### Phase 2: Core Implementation

1. Implement CloudStorageService with R2 connection
2. Modify StagedProductService to use CloudStorageService
3. Update image handling logic to store images in R2
4. Implement URL generation for stored images
5. Add cleanup functionality for R2 objects

### Phase 3: Migration and Testing

1. Create migration strategy for existing images
2. Implement fallback mechanism during transition
3. Write tests for the new storage functionality
4. Perform load testing to ensure performance

### Phase 4: Documentation and Deployment

1. Update API documentation
2. Create usage examples for the development team
3. Deploy to staging environment
4. Monitor performance and make adjustments

## Progress Tracking

| Task | Status | Notes |
|------|--------|-------|
| Create CloudStorageModule | Completed | Created module in src/common/cloud-storage/ |
| Implement CloudStorageService | Completed | Implemented with S3 client for R2 |
| Configure environment variables | Completed | Added R2 config to .env and .env.example |
| Modify StagedProductService | Completed | Updated to use CloudStorageService |
| Update image handling logic | Completed | Now uploads to R2 with fallback to temp files |
| Implement URL generation | Completed | Added methods for public and signed URLs |
| Add cleanup functionality | Completed | Added R2 object deletion |
| Create migration strategy | In Progress | Need to migrate existing images |
| Implement fallback mechanism | Completed | Falls back to TempFileStorage if R2 fails |
| Write tests | Not Started | |
| Perform load testing | Not Started | |
| Update API documentation | In Progress | Updated DTOs with R2 fields |
| Create usage examples | Not Started | |
| Deploy to staging | Not Started | |
| Monitor performance | Not Started | |

## Usage Guidelines

### Environment Variables

Add the following environment variables to your `.env` file:

```
R2_ACCOUNT_ID=your_cloudflare_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_PUBLIC_URL=https://<bucket-name>.<account-id>.r2.cloudflarestorage.com
R2_ENDPOINT=https://<account-id>.r2.cloudflarestorage.com
```

### CloudStorageService Implementation

Here's a sample implementation of the CloudStorageService:

```typescript
// src/common/cloud-storage/cloud-storage.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuid } from 'uuid';

@Injectable()
export class CloudStorageService {
  private readonly logger = new Logger(CloudStorageService.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly publicUrl: string;

  constructor(private configService: ConfigService) {
    const accountId = this.configService.get<string>('R2_ACCOUNT_ID');
    const accessKeyId = this.configService.get<string>('R2_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.get<string>('R2_SECRET_ACCESS_KEY');
    this.bucketName = this.configService.get<string>('R2_BUCKET_NAME');
    this.publicUrl = this.configService.get<string>('R2_PUBLIC_URL');

    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: `https://${accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    this.logger.log(`CloudStorageService initialized with bucket: ${this.bucketName}`);
  }

  /**
   * Upload a file to R2 storage
   * @param fileBuffer The file buffer to upload
   * @param key Optional key (filename) for the file, if not provided a UUID will be generated
   * @param contentType The content type of the file
   * @returns The URL of the uploaded file
   */
  async uploadFile(
    fileBuffer: Buffer,
    key?: string,
    contentType = 'application/octet-stream'
  ): Promise<string> {
    try {
      // Generate a key if not provided
      const fileKey = key || `uploads/${uuid()}`;

      // Upload the file to R2
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
        Body: fileBuffer,
        ContentType: contentType,
      });

      await this.s3Client.send(command);

      // Return the public URL
      return `${this.publicUrl}/${fileKey}`;
    } catch (error) {
      this.logger.error(`Error uploading file to R2: ${error.message}`, error.stack);
      throw new Error(`Failed to upload file to R2: ${error.message}`);
    }
  }

  /**
   * Get a signed URL for a file (for private buckets)
   * @param key The key (filename) of the file
   * @param expirationSeconds How long the URL should be valid for
   * @returns A signed URL for the file
   */
  async getSignedUrl(key: string, expirationSeconds = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn: expirationSeconds });
    } catch (error) {
      this.logger.error(`Error generating signed URL: ${error.message}`, error.stack);
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }

  /**
   * Delete a file from R2 storage
   * @param key The key (filename) of the file to delete
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.debug(`Deleted file from R2: ${key}`);
    } catch (error) {
      this.logger.error(`Error deleting file from R2: ${error.message}`, error.stack);
      throw new Error(`Failed to delete file from R2: ${error.message}`);
    }
  }
}
```

### Integration with StagedProductService

The StagedProductService will be updated to use CloudStorageService instead of TempFileStorageService:

```typescript
// Example of storing an image in R2
const imageBuffer = Buffer.from(image.imageData, 'base64');
const imageKey = `products/${stagedProduct.id}/${uuid()}.jpg`;
const imageUrl = await this.cloudStorageService.uploadFile(
  imageBuffer,
  imageKey,
  'image/jpeg'
);

// Store the URL and key in the database
stagedProduct.images.push({
  imageUrl,
  r2Key: imageKey // Store the key for future reference (deletion, etc.)
});
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify that your R2 credentials are correct
   - Check that your environment variables are properly set
   - Ensure the Access Key ID and Secret Access Key are still valid

2. **CORS Issues**
   - Configure CORS settings in your R2 bucket if accessing directly from frontend
   - Check that the allowed origins match your application domains

3. **Permission Errors**
   - Ensure your API token has the correct permissions for the bucket
   - Check that the bucket name is correct in your configuration

4. **File Size Limitations**
   - R2 has a maximum file size of 5TB
   - Implement chunked uploads for large files
   - For our application, we're limiting uploads to 5MB per file

5. **URL Generation Issues**
   - Ensure the R2_PUBLIC_URL is correctly formatted
   - For private buckets, use signed URLs with appropriate expiration times

### Logging and Monitoring

- Enable DEBUG level logging during development:
  ```typescript
  this.logger.debug(`Uploading file to R2: ${key}`);
  ```
- Monitor R2 usage and costs through Cloudflare dashboard
- Set up alerts for storage thresholds
- Implement periodic cleanup of unused files

### Testing R2 Connection

You can test your R2 connection with this simple script:

```typescript
// test-r2-connection.ts
import { CloudStorageService } from './cloud-storage.service';
import { ConfigService } from '@nestjs/config';

async function testConnection() {
  const configService = new ConfigService();
  const cloudStorage = new CloudStorageService(configService);

  try {
    // Create a test file
    const testBuffer = Buffer.from('Hello, R2!');
    const testKey = 'test-file.txt';

    // Upload the file
    console.log('Uploading test file...');
    const url = await cloudStorage.uploadFile(testBuffer, testKey, 'text/plain');
    console.log(`File uploaded successfully: ${url}`);

    // Get a signed URL
    console.log('Generating signed URL...');
    const signedUrl = await cloudStorage.getSignedUrl(testKey, 60);
    console.log(`Signed URL: ${signedUrl}`);

    // Delete the file
    console.log('Deleting test file...');
    await cloudStorage.deleteFile(testKey);
    console.log('File deleted successfully');

    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testConnection();
```

## Migration Strategy

To migrate from local file storage to R2:

1. Implement CloudStorageService alongside TempFileStorageService
2. Add a feature flag to control which storage service is used
3. Update StagedProductService to use CloudStorageService when the flag is enabled
4. Gradually migrate existing images to R2
5. Once all images are migrated, remove TempFileStorageService

## Next Steps

After implementation, consider:

1. Setting up Cloudflare Workers for image processing
2. Implementing image optimization at upload time
3. Adding image processing capabilities (resizing, cropping)
4. Creating a backup strategy for critical images
5. Implementing a CDN for faster image delivery

---

This document will be updated as the implementation progresses.
