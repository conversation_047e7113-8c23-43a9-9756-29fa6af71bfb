# Global Authentication Guide

This guide explains how authentication is implemented in the TikTok Shop backend application.

## Overview

The application uses a global authentication guard that protects all routes by default. This means that any request to any endpoint will require authentication unless the endpoint is explicitly marked as public.

## How It Works

1. The `GlobalAuthGuard` is registered as a global guard in the `AppModule`.
2. The guard checks if a route is marked as public using the `@Public()` decorator.
3. If the route is public, access is allowed without authentication.
4. If the route is not public, the JWT authentication guard is used to validate the request.

## Using the @Public() Decorator

To mark a route as public (not requiring authentication), use the `@Public()` decorator:

```typescript
import { Controller, Get } from '@nestjs/common';
import { Public } from './auth/decorators/public.decorator';

@Controller('example')
export class ExampleController {
  @Public()
  @Get()
  findAll() {
    // This route is public and does not require authentication
    return ['example1', 'example2'];
  }

  @Get('protected')
  findProtected() {
    // This route is protected and requires authentication
    return ['protected1', 'protected2'];
  }
}
```

You can also mark an entire controller as public:

```typescript
import { Controller, Get } from '@nestjs/common';
import { Public } from './auth/decorators/public.decorator';

@Public()
@Controller('public-api')
export class PublicApiController {
  @Get()
  findAll() {
    // This route is public
    return ['public1', 'public2'];
  }

  @Get('also-public')
  findOne() {
    // This route is also public
    return 'public';
  }
}
```

## Authentication Routes

The following routes are marked as public to allow authentication:

- `POST /auth/oauth-login`: OAuth login endpoint
- `POST /auth/request-magic-link`: Request a magic link for email authentication
- `POST /auth/verify-magic-link`: Verify a magic link token

## Testing Authentication

To test authentication, you can use the following curl commands:

1. Get a JWT token:

```bash
curl -X POST http://localhost:3001/auth/oauth-login -H "Content-Type: application/json" -d '{"provider": "google", "providerId": "123456789", "email": "<EMAIL>", "name": "Test User", "image": "https://example.com/image.jpg"}'
```

2. Use the token to access a protected route:

```bash
curl -X GET http://localhost:3001/auth/profile -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

3. Try to access a protected route without a token (should return 401 Unauthorized):

```bash
curl -X GET http://localhost:3001/auth/profile
```

## Security Considerations

1. Always use HTTPS in production to protect JWT tokens in transit.
2. Set appropriate token expiration times.
3. Implement token refresh mechanisms for long-lived sessions.
4. Be careful about which routes you mark as public.
5. Consider implementing rate limiting for authentication endpoints to prevent brute force attacks.
