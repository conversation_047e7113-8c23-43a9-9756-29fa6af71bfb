# Staged Product from Template Endpoint

This document describes the new endpoint for creating a StagedProduct from a Template.

## Endpoint

**POST** `/staged-products/from-template`

## Description

Creates a new staged product using data from an existing template. This endpoint allows users to quickly create products by leveraging pre-configured templates with predefined attributes, SKUs, dimensions, and other product data.

## Authentication

This endpoint requires JWT authentication. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Request Body

```typescript
{
  templateId: number;     // ID of the template to use
  title: string;          // Product title
  images: Array<{         // Product images
    imageUrl?: string;    // URL of the image (optional)
    r2Key?: string;       // R2 storage key (optional)
  }>;
}
```

### Example Request

```json
{
  "templateId": 1,
  "title": "Premium Cotton T-Shirt - Red",
  "images": [
    {
      "imageUrl": "https://example.com/product-image-1.jpg",
      "r2Key": "products/123/image-1"
    },
    {
      "imageUrl": "https://example.com/product-image-2.jpg",
      "r2Key": "products/123/image-2"
    }
  ]
}
```

## Response

Returns a `StagedProductResponseDto` with the created staged product data.

### Success Response (201 Created)

```json
{
  "id": 123,
  "title": "Premium Cotton T-Shirt - Red",
  "description": "High-quality cotton t-shirt with a comfortable fit.",
  "categoryId": "853000",
  "brandId": "7082427311584347905",
  "categoryVersion": "v2",
  "images": [
    {
      "imageUrl": "https://example.com/product-image-1.jpg",
      "r2Key": "products/123/image-1"
    }
  ],
  "productAttributes": [
    {
      "id": "7082427311584347905",
      "values": [
        {
          "id": "7082427311584347906"
        }
      ]
    }
  ],
  "skus": [
    {
      "salesAttributes": [
        {
          "name": "Color",
          "valueName": "Red"
        }
      ],
      "inventory": [
        {
          "quantity": 100,
          "warehouseId": "0"
        }
      ],
      "price": {
        "amount": 19.99,
        "currency": "USD"
      }
    }
  ],
  "packageDimensions": {
    "length": "20",
    "width": "15",
    "height": "5",
    "unit": "CENTIMETER"
  },
  "packageWeight": {
    "value": "500",
    "unit": "GRAM"
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

## Error Responses

### 400 Bad Request
Invalid request body or validation errors.

```json
{
  "statusCode": 400,
  "message": [
    "templateId must be a number",
    "title should not be empty",
    "images must be an array"
  ],
  "error": "Bad Request"
}
```

### 401 Unauthorized
Missing or invalid authentication token.

```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

### 404 Not Found
Template not found or user doesn't have access to it.

```json
{
  "statusCode": 404,
  "message": "Template with ID 123 not found"
}
```

## Template Data Copied

The following data is copied from the template to the new staged product:

- `description`
- `categoryId`
- `brandId`
- `productAttributes`
- `skus` (with inventory converted to array format)
- `sizeChart` (if present in template)
- `packageDimensions`
- `packageWeight`

## User Access Control

- Users can only access templates they own or public templates (`isPublic: true`)
- The created staged product will be associated with the authenticated user
- Templates are validated for existence and user access before creating the staged product

## Implementation Details

1. The endpoint validates the request body using the `CreateStagedProductFromTemplateDto`
2. It uses the `TemplateService.createStagedProductFromTemplate()` method to handle the business logic
3. Template data is transformed to match the staged product format (e.g., inventory array conversion)
4. The response is mapped using the existing `StagedProductResponseDto`

## Usage Example with cURL

```bash
curl -X POST \
  http://localhost:3000/staged-products/from-template \
  -H 'Authorization: Bearer your_access_token' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": 1,
    "title": "Premium Cotton T-Shirt - Red",
    "images": [
      {
        "imageUrl": "https://example.com/product-image.jpg",
        "r2Key": "products/123/image-1"
      }
    ]
  }'
```
