# Check Product Listing Technical Specification

## Overview

This document provides a detailed technical specification for implementing the TikTok Shop Product Listing Check functionality in our application. This feature will validate product data against TikTok's requirements before attempting to create products.

## Data Models

### Validation Result DTOs

```typescript
export class ValidationErrorDto {
  field: string;
  message: string;
  code: string;
  level: 'ERROR' | 'WARNING';
}

export class ProductValidationResultDto {
  isValid: boolean;
  errors: ValidationErrorDto[];
  warnings: ValidationErrorDto[];
  rawResponse?: any; // Original TikTok API response
}
```

### Controller Endpoint

```typescript
@Post('validate')
@ApiOperation({ summary: 'Validate a staged product without creating it' })
@ApiResponse({
  status: 200,
  description: 'Validation results',
  type: ProductValidationResultDto,
})
async validateStagedProduct(
  @Body() validateProductDto: {
    stagedProductId: number;
    tiktokShopId: number;
  },
): Promise<ProductValidationResultDto> {
  return this.productUploadService.checkProductListing(
    validateProductDto.stagedProductId,
    validateProductDto.tiktokShopId,
  );
}
```

### Service Methods

```typescript
// In ProductUploadService
async createProductUpload(
  createProductUploadDto: CreateProductUploadDto,
  options?: { skipValidation?: boolean, forceUpload?: boolean },
): Promise<ProductUploadResponseDto> {
  // Implementation details below
}

// In ProductUploadQueueProcessor
async processImages(
  stagedProduct: StagedProduct,
  tiktokShop: TikTokShop,
): Promise<StagedProduct> {
  // Implementation details below
}

async checkProductListing(
  stagedProduct: StagedProduct,
  tiktokShop: TikTokShop,
): Promise<ProductValidationResultDto> {
  // Implementation details below
}
```

## Implementation Details

### 1. Image Processing with Retry Logic

Implement robust image processing with retry logic in the background job:

```typescript
async processImages(stagedProduct: StagedProduct, tiktokShop: TikTokShop): Promise<StagedProduct> {
  const MAX_RETRIES = 3;
  const processedImages = [];

  // Process main images
  for (const image of stagedProduct.images) {
    // If image already has a URI, use it
    if (image.uri) {
      processedImages.push({
        uri: image.uri,
        url: image.url || '',
        width: image.width || 0,
        height: image.height || 0,
        useCase: image.useCase,
      });
      continue;
    }

    // If image has imageData or imageUrl, upload it with retry logic
    if (image.imageData || image.imageUrl) {
      let retries = 0;
      let success = false;
      let uploadResult;

      while (retries < MAX_RETRIES && !success) {
        try {
          uploadResult = await this.imageUploadService.uploadImage(tiktokShop.id, {
            imageData: image.imageData,
            imageUrl: image.imageUrl,
            useCase: image.useCase as ImageUseCase,
          });

          success = true;
        } catch (error) {
          retries++;
          this.logger.warn(`Image upload failed, retry ${retries}/${MAX_RETRIES}: ${error.message}`);

          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries - 1)));
        }
      }

      if (!success) {
        throw new Error(`Failed to upload image after ${MAX_RETRIES} attempts`);
      }

      processedImages.push({
        uri: uploadResult.uri,
        url: uploadResult.url,
        width: uploadResult.width,
        height: uploadResult.height,
        useCase: uploadResult.useCase,
      });
    }
  }

  // Process size chart similarly with retry logic
  // ...

  // Update staged product with processed images
  stagedProduct.images = processedImages;
  return await this.stagedProductRepository.save(stagedProduct);
}
```

### 2. Product Validation

Implement product validation in the background job after image processing:

```typescript
async checkProductListing(stagedProduct: StagedProduct, tiktokShop: TikTokShop): Promise<ProductValidationResultDto> {
  // Format data for TikTok API
  const tikTokProductData = await this.prepareTikTokProductData(stagedProduct);

  // Create TikTok client
  const client = await this.tikTokClientFactory.createClientByAppKey(tiktokShop.app_key);

  // Call validation API
  const result = await client.api.ProductV202309Api.ProductsListingCheckPost(
    tiktokShop.access_token,
    'application/json',
    tiktokShop.cipher,
    tikTokProductData,
    { headers: {} }
  );

  // Initialize result object
  const validationResult: ProductValidationResultDto = {
    isValid: false,
    errors: [],
    warnings: [],
    rawResponse: result.body,
  };

  // Check if the request was successful
  if (result.body?.code === 0) {
    // Process validation results
    const checkResult = result.body?.data?.check_result;

    if (checkResult) {
      validationResult.isValid = checkResult.is_passed || false;

      // Process errors and warnings
      if (checkResult.errors && Array.isArray(checkResult.errors)) {
        checkResult.errors.forEach(error => {
          if (error.level === 'ERROR') {
            validationResult.errors.push({
              field: error.field_name || '',
              message: error.message || '',
              code: error.error_code || '',
              level: 'ERROR',
            });
          } else {
            validationResult.warnings.push({
              field: error.field_name || '',
              message: error.message || '',
              code: error.error_code || '',
              level: 'WARNING',
            });
          }
        });
      }
    }
  }

  return validationResult;
}
```

### 3. Background Job Processing

Implement the main job handler to process everything in the background:

```typescript
async handleCreateProduct(job: Job) {
  try {
    const { productUploadId, stagedProductId, tiktokShopId, options } = job.data;
    const skipValidation = options?.skipValidation || false;
    const forceUpload = options?.forceUpload || false;

    // Get required entities
    const productUpload = await this.productUploadRepository.findOne({ where: { id: productUploadId } });
    const stagedProduct = await this.stagedProductRepository.findOne({ where: { id: stagedProductId } });
    const tiktokShop = await this.tikTokShopRepository.findOne({ where: { id: tiktokShopId } });

    if (!productUpload || !stagedProduct || !tiktokShop) {
      throw new NotFoundException('Required entities not found');
    }

    // Update status to IN_PROGRESS
    productUpload.status = ProductUploadStatus.IN_PROGRESS;
    await this.productUploadRepository.save(productUpload);

    // Update progress
    await job.progress(10);

    // Step 1: Process images with retry logic
    const processedProduct = await this.processImages(stagedProduct, tiktokShop);
    await job.progress(40);

    // Step 2: Validate product if not skipping validation
    let validationResult = null;
    if (!skipValidation) {
      validationResult = await this.checkProductListing(processedProduct, tiktokShop);

      // If validation fails and not forcing upload, update status and return
      if (!validationResult.isValid && !forceUpload) {
        productUpload.status = ProductUploadStatus.FAILED;
        productUpload.errorMessage = 'Product validation failed';
        productUpload.progress = 100;
        await this.productUploadRepository.save(productUpload);

        return {
          success: false,
          message: 'Product validation failed',
          validationErrors: validationResult.errors,
          validationWarnings: validationResult.warnings,
        };
      }
    }

    await job.progress(70);

    // Step 3: Create product in TikTok Shop
    // ... existing product creation code ...

    // Step 4: Update product upload status
    productUpload.status = ProductUploadStatus.COMPLETED;
    productUpload.tiktokProductId = createdProductId;
    productUpload.progress = 100;
    productUpload.completedAt = new Date();
    await this.productUploadRepository.save(productUpload);

    return {
      success: true,
      message: 'Product created successfully',
      productId: createdProductId,
    };
  } catch (error) {
    // Handle errors and update product upload status
    // ...
  }
}
```

### 4. Simplified Product Upload Service

Simplify the product upload service to just create a record and queue a job:

```typescript
async createProductUpload(
  createProductUploadDto: CreateProductUploadDto,
  options?: { skipValidation?: boolean; forceUpload?: boolean },
): Promise<ProductUploadResponseDto> {
  // Check if staged product and TikTok shop exist
  const stagedProduct = await this.stagedProductRepository.findOne({
    where: { id: createProductUploadDto.stagedProductId }
  });
  const tiktokShop = await this.tikTokShopRepository.findOne({
    where: { id: createProductUploadDto.tiktokShopId }
  });

  if (!stagedProduct || !tiktokShop) {
    throw new NotFoundException('Staged product or TikTok shop not found');
  }

  // Create product upload record
  const productUpload = new ProductUpload();
  productUpload.stagedProductId = createProductUploadDto.stagedProductId;
  productUpload.tiktokShopId = createProductUploadDto.tiktokShopId;
  productUpload.status = ProductUploadStatus.PENDING;
  productUpload.progress = 0;

  // Save product upload
  const savedProductUpload = await this.productUploadRepository.save(productUpload);

  // Add job to queue with options
  const jobResult = await this.productQueueService.addProductCreationJob({
    productUploadId: savedProductUpload.id,
    stagedProductId: createProductUploadDto.stagedProductId,
    tiktokShopId: createProductUploadDto.tiktokShopId,
    options: {
      skipValidation: options?.skipValidation || false,
      forceUpload: options?.forceUpload || false,
    },
  });

  // Update product upload with job ID
  savedProductUpload.jobId = jobResult.jobId.toString();
  await this.productUploadRepository.save(savedProductUpload);

  return this.mapToResponseDto(savedProductUpload);
}
```

## Error Handling

### Image Upload Errors

Implement retry logic for image uploads:

```typescript
let retries = 0;
const MAX_RETRIES = 3;

while (retries < MAX_RETRIES) {
  try {
    // Upload image
    break;
  } catch (error) {
    retries++;
    if (retries >= MAX_RETRIES) {
      throw new Error(`Failed to upload image after ${MAX_RETRIES} attempts: ${error.message}`);
    }
    // Wait with exponential backoff
    await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries - 1)));
  }
}
```

### Validation Errors

When validation fails in the background job, update the product upload status:

```typescript
if (!validationResult.isValid && !forceUpload) {
  productUpload.status = ProductUploadStatus.FAILED;
  productUpload.errorMessage = 'Product validation failed';
  productUpload.progress = 100;
  await this.productUploadRepository.save(productUpload);

  return {
    success: false,
    message: 'Product validation failed',
    validationErrors: validationResult.errors,
    validationWarnings: validationResult.warnings,
  };
}
```

### API Errors

Handle TikTok API errors:

```typescript
if (result.body?.code !== 0) {
  throw new BadRequestException(
    `Failed to validate product: ${result.body?.message || 'Unknown error'}`,
  );
}
```

### Job Errors

Handle errors in the background job:

```typescript
try {
  // Job processing
} catch (error) {
  // Update product upload status
  productUpload.status = ProductUploadStatus.FAILED;
  productUpload.errorMessage = error.message;
  productUpload.progress = 100;
  await this.productUploadRepository.save(productUpload);

  // Log error
  this.logger.error(`Error processing job ${job.id}: ${error.message}`, error.stack);

  // Return error information
  return {
    success: false,
    message: error.message,
    error: error.stack,
  };
}
```

## Testing Strategy

1. **Unit Tests**:
   - Test image processing with retry logic
   - Test data formatting for the TikTok API
   - Test validation result processing
   - Test error handling

2. **Integration Tests**:
   - Test the full background job flow with mock TikTok API responses
   - Test image processing integration with retries
   - Test validation integration

3. **End-to-End Tests**:
   - Test the complete flow with real TikTok API (in staging environment)
   - Test various product scenarios (valid products, products with errors, etc.)
   - Test image upload failures and retry logic

## Performance Considerations

1. **Background Processing**: Process everything in background jobs to avoid timeouts
2. **Retry Logic**: Implement exponential backoff for retries to avoid overwhelming the API
3. **Progress Tracking**: Update job progress regularly to provide feedback to users
4. **Batch Processing**: Consider batching API calls when possible
5. **Image Optimization**: Optimize images before uploading to reduce upload time

## Security Considerations

1. **API Credentials**: Ensure TikTok API credentials are securely stored
2. **Input Validation**: Validate all input data before sending to TikTok API
3. **Error Messages**: Ensure error messages don't expose sensitive information
4. **Job Data**: Ensure sensitive data in job payloads is properly handled
5. **Status Updates**: Validate status update requests to prevent unauthorized updates
