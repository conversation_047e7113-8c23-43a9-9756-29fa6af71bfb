/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309UncombinePackagesRequestBody {
    /**
    * TikTok Shop order ID. Indicate the orders that need to be removed from the package. Please make sure the orders belong to the package.
    */
    'orderIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orderIds",
            "baseName": "order_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309UncombinePackagesRequestBody.attributeTypeMap;
    }
}

