/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309SplitOrdersRequestBodySplittableGroups {
    /**
    * A unique identifier designated by the developer. This identifier will represent a group of items that will be split into a new package. Once split is confirmed, the platform will be assigned a new package ID for this group of items.  For example, if you input `123` as request, the response will return `123` as your unique identification. The seller uses this field to label each group of items that have been split, and the platform will assign new package IDs to them. 
    */
    'id'?: string;
    /**
    * The order line item IDs that need to be split into this group.
    */
    'orderLineItemIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SplitOrdersRequestBodySplittableGroups.attributeTypeMap;
    }
}

