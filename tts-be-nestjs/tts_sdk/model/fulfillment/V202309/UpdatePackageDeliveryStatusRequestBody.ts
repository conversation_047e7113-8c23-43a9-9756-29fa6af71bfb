/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309UpdatePackageDeliveryStatusRequestBodyPackages } from './UpdatePackageDeliveryStatusRequestBodyPackages';

export class Fulfillment202309UpdatePackageDeliveryStatusRequestBody {
    /**
    * The return list of packages.
    */
    'packages'?: Array<Fulfillment202309UpdatePackageDeliveryStatusRequestBodyPackages>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "packages",
            "baseName": "packages",
            "type": "Array<Fulfillment202309UpdatePackageDeliveryStatusRequestBodyPackages>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309UpdatePackageDeliveryStatusRequestBody.attributeTypeMap;
    }
}

