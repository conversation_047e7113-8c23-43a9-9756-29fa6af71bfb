/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309UpdatePackageDeliveryStatusResponseDataErrors } from './UpdatePackageDeliveryStatusResponseDataErrors';

export class Fulfillment202309UpdatePackageDeliveryStatusResponseData {
    /**
    * Specific return information (returns multiple errors and reasons).
    */
    'errors'?: Array<Fulfillment202309UpdatePackageDeliveryStatusResponseDataErrors>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Fulfillment202309UpdatePackageDeliveryStatusResponseDataErrors>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309UpdatePackageDeliveryStatusResponseData.attributeTypeMap;
    }
}

