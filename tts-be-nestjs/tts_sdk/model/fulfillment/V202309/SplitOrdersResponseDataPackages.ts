/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309SplitOrdersResponseDataPackages {
    /**
    * Package ID after success split. Use this new package ID to call [Ship Package API](https://partner.tiktokshop.com/docv2/page/650aa4f1defece02be6e7cb1?external_id=650aa4f1defece02be6e7cb1) to ship the package.
    */
    'id'?: string;
    /**
    * The ID of split group in request body.
    */
    'splittableGroupId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "splittableGroupId",
            "baseName": "splittable_group_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SplitOrdersResponseDataPackages.attributeTypeMap;
    }
}

