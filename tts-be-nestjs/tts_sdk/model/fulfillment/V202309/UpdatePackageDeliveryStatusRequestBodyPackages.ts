/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309UpdatePackageDeliveryStatusRequestBodyPackages {
    /**
    * Delivery status of the package. Possible values: - `DELIVERY_SUCCESS`: Package has been successfully delivered. - `DELIVERY_FAILED`: Package delivery has been unsuccessful. - `UPDATE_POD`: For packages that have been delivered, but you would like to update an attachment.
    */
    'deliveryType'?: string;
    /**
    * Delivery failure reasons.  When `delivery_type = DELIVERY_FAILED`, this field is required. For other delivery types, this field is not required.  Possible values: - `INVALID_ADDRESS`: The buyer shipping address is invalid. - `UNABLE_RECEIVE`: The buyer is currently unable to receive the delivery. - `UNABLE_CONTACT_BUYER`: Unable to contact the buyer. - `BUYER_REFUSED`: The buyer has refused to receive the product. - `DELAY_DELIVERY`: Delay in delivery. - `PACKAGE_LOST`: The package is lost. - `PACKAGE_DAMAGE`: The package is damaged. - `FORCE_MAJEURE`: An unforeseeable event of force majeure has occurred. - `OTHER`: Other reason. 
    */
    'failDeliveryReason'?: string;
    /**
    * Attachment type: - `IMG` - `PDF`
    */
    'fileType'?: string;
    /**
    * Attachment URL. The seller can use the [Upload Delivery File](https://partner.tiktokshop.com/docv2/page/650aa6e04a0bb702c06dcd34?external_id=650aa6e04a0bb702c06dcd34#Back%20To%20Top) and [Upload Delivery Image](https://partner.tiktokshop.com/docv2/page/650aa70d0fcef602bf32772f?external_id=650aa70d0fcef602bf32772f) APIs to generate the URL. The attachment will be used by TikTok Shop to verify the package delivery.
    */
    'fileUrl'?: string;
    /**
    * The package ID.
    */
    'id'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "deliveryType",
            "baseName": "delivery_type",
            "type": "string"
        },
        {
            "name": "failDeliveryReason",
            "baseName": "fail_delivery_reason",
            "type": "string"
        },
        {
            "name": "fileType",
            "baseName": "file_type",
            "type": "string"
        },
        {
            "name": "fileUrl",
            "baseName": "file_url",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309UpdatePackageDeliveryStatusRequestBodyPackages.attributeTypeMap;
    }
}

