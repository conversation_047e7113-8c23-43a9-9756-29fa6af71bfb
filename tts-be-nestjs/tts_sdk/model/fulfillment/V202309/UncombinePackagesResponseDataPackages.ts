/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309UncombinePackagesResponseDataPackages {
    /**
    * The newly generated package ID(s) after being uncombined.
    */
    'id'?: string;
    /**
    * List of order ID(s) corresponding to the uncombined package ID(s).
    */
    'orderIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "orderIds",
            "baseName": "order_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309UncombinePackagesResponseDataPackages.attributeTypeMap;
    }
}

