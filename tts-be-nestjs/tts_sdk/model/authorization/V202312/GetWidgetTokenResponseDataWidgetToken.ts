/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Authorization202312GetWidgetTokenResponseDataWidgetToken {
    /**
    * widget token expire timestamp, usually 5 minutes
    */
    'expireAt'?: number;
    /**
    * token used to pass widget api
    */
    'token'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "expireAt",
            "baseName": "expire_at",
            "type": "number"
        },
        {
            "name": "token",
            "baseName": "token",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Authorization202312GetWidgetTokenResponseDataWidgetToken.attributeTypeMap;
    }
}

