/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Authorization202309GetAuthorizedShopsResponseDataShops } from './GetAuthorizedShopsResponseDataShops';

export class Authorization202309GetAuthorizedShopsResponseData {
    /**
    * The list of shops that a seller has authorized for the app.
    */
    'shops'?: Array<Authorization202309GetAuthorizedShopsResponseDataShops>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "shops",
            "baseName": "shops",
            "type": "Array<Authorization202309GetAuthorizedShopsResponseDataShops>"
        }    ];

    static getAttributeTypeMap() {
        return Authorization202309GetAuthorizedShopsResponseData.attributeTypeMap;
    }
}

