/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Authorization202309GetAuthorizedShopsResponseDataShops {
    /**
    * An encrypted token used to securely identify a shop in API requests. There is no need for decryption on the receiving end.
    */
    'cipher'?: string;
    /**
    * The TikTok Shop code, which is also displayed on Seller Center.
    */
    'code'?: string;
    /**
    * An internal identifier for the TikTok Shop.
    */
    'id'?: string;
    /**
    * The TikTok Shop name.
    */
    'name'?: string;
    /**
    * The region of the shop.
    */
    'region'?: string;
    /**
    * The type of seller. Possible values: - CROSS_BORDER: Cross border sellers with multiple shops in different countries. - LOCAL: Local sellers with only 1 shop.
    */
    'sellerType'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "cipher",
            "baseName": "cipher",
            "type": "string"
        },
        {
            "name": "code",
            "baseName": "code",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "region",
            "baseName": "region",
            "type": "string"
        },
        {
            "name": "sellerType",
            "baseName": "seller_type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Authorization202309GetAuthorizedShopsResponseDataShops.attributeTypeMap;
    }
}

