/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309UpdateGlobalInventoryRequestBodyGlobalSkusInventory {
    /**
    * The ID of the global warehouse where the SKU is stored. Retrieve this value from [Get Global Product](6509e2b0bace3e02b7490c96) or [Get Global Seller Warehouse](650aa3f0defece02be6e5ffb).
    */
    'globalWarehouseId'?: string;
    /**
    * The updated SKU quantity.
    */
    'quantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "globalWarehouseId",
            "baseName": "global_warehouse_id",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateGlobalInventoryRequestBodyGlobalSkusInventory.attributeTypeMap;
    }
}

