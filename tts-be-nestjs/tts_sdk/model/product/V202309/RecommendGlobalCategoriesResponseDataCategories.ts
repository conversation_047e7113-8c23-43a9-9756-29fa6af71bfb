/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309RecommendGlobalCategoriesResponseDataCategories {
    /**
    * The category ID.
    */
    'id'?: string;
    /**
    * A flag to indicate if the category is a leaf category.  **Note**: You can only create or edit products that belong to a leaf category.
    */
    'isLeaf'?: boolean;
    /**
    * The category level.
    */
    'level'?: number;
    /**
    * The category name.
    */
    'name'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isLeaf",
            "baseName": "is_leaf",
            "type": "boolean"
        },
        {
            "name": "level",
            "baseName": "level",
            "type": "number"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecommendGlobalCategoriesResponseDataCategories.attributeTypeMap;
    }
}

