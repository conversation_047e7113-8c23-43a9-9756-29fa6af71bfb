/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309SearchGlobalProductsRequestBody {
    /**
    * The fields \"create_time_ge\" and \"create_time_le\" together constitute the filter condition for the creation time of the global product.  - If you only fill in the \"create_time_le\", and the \"create_time_ge\" is empty , then we will set the earliest time of the shop to the field \"create_time_ge\" by default.  - If you only fill in the \"create_time_ge\", and the \"create_time_le\" is empty , then we will set the current time to the field \"create_time_le\" by default.  The time search condition uses Unix timestamp in GMT (UTC+00:00). 
    */
    'createTimeGe'?: number;
    /**
    * Refer to the description of \"create_time_ge\".
    */
    'createTimeLe'?: number;
    /**
    * Seller SKUs, a filtering condition used for global product search. This field allows you to search for all global products that contain these Seller SKUs.
    */
    'sellerSkus'?: Array<string>;
    /**
    * Global Product status, used as a filtering criterion for global product search. including  PUBLISHED,UNPUBLISHED,DRAFT,DELETED
    */
    'status'?: string;
    /**
    * The fields \"update_time_ge\" and \"update_time_le\" together constitute the filter condition for the update time of the global product.  -  If you only fill in the \"update_time_le\", and the \"update_time_ge\" is empty , then we will set the earliest time of the shop to the field \"update_time_ge\" by default.  - If you only fill in the \"update_time_ge\", and the \"update_time_le\" is empty , then we will set the current time to the field \"update_time_le\" by default.
    */
    'updateTimeGe'?: number;
    /**
    * Refer to the description of \"update_time_ge\".
    */
    'updateTimeLe'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTimeGe",
            "baseName": "create_time_ge",
            "type": "number"
        },
        {
            "name": "createTimeLe",
            "baseName": "create_time_le",
            "type": "number"
        },
        {
            "name": "sellerSkus",
            "baseName": "seller_skus",
            "type": "Array<string>"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "updateTimeGe",
            "baseName": "update_time_ge",
            "type": "number"
        },
        {
            "name": "updateTimeLe",
            "baseName": "update_time_le",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchGlobalProductsRequestBody.attributeTypeMap;
    }
}

