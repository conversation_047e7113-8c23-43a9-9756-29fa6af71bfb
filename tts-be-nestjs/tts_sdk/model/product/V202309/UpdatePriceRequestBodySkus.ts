/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdatePriceRequestBodySkusExternalListPrices } from './UpdatePriceRequestBodySkusExternalListPrices';
import { Product202309UpdatePriceRequestBodySkusListPrice } from './UpdatePriceRequestBodySkusListPrice';
import { Product202309UpdatePriceRequestBodySkusPrice } from './UpdatePriceRequestBodySkusPrice';

export class Product202309UpdatePriceRequestBodySkus {
    /**
    * The SKU list price (e.g. MSRP, RRP) or original price information on external ecommerce platforms. Applicable only for selected sellers in the US market.  **Note**: This value may appear as the strikethrough price on the product page. However, whether the strikethrough price is shown and the amount shown are subject to the audit team\'s review and decision based on various pricing information.
    */
    'externalListPrices'?: Array<Product202309UpdatePriceRequestBodySkusExternalListPrices>;
    /**
    * The SKU ID generated by TikTok Shop. One product can contain multiple SKU IDs.  **Note**:  - The SKU ID must belong to a product with the `ACTIVATE` status. - If you are updating multiple SKUs, all the SKU IDs must belong to the same product.
    */
    'id'?: string;
    'listPrice'?: Product202309UpdatePriceRequestBodySkusListPrice;
    'price'?: Product202309UpdatePriceRequestBodySkusPrice;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "externalListPrices",
            "baseName": "external_list_prices",
            "type": "Array<Product202309UpdatePriceRequestBodySkusExternalListPrices>"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "listPrice",
            "baseName": "list_price",
            "type": "Product202309UpdatePriceRequestBodySkusListPrice"
        },
        {
            "name": "price",
            "baseName": "price",
            "type": "Product202309UpdatePriceRequestBodySkusPrice"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdatePriceRequestBodySkus.attributeTypeMap;
    }
}

