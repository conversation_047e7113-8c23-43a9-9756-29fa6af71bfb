/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309RecommendCategoryResponseDataCategories } from './RecommendCategoryResponseDataCategories';

export class Product202309RecommendCategoryResponseData {
    /**
    * Recommended category information.
    */
    'categories'?: Array<Product202309RecommendCategoryResponseDataCategories>;
    /**
    * The recommended category ID. This is always a leaf category.
    */
    'leafCategoryId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "categories",
            "baseName": "categories",
            "type": "Array<Product202309RecommendCategoryResponseDataCategories>"
        },
        {
            "name": "leafCategoryId",
            "baseName": "leaf_category_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecommendCategoryResponseData.attributeTypeMap;
    }
}

