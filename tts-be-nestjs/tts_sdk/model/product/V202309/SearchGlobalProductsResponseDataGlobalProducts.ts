/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309SearchGlobalProductsResponseDataGlobalProductsSkus } from './SearchGlobalProductsResponseDataGlobalProductsSkus';

export class Product202309SearchGlobalProductsResponseDataGlobalProducts {
    /**
    * The time when the global product is created. Unix timestamp GMT (UTC+00:00). This timestamp is used across all API requests. Developers can use this convert to local time.
    */
    'createTime'?: number;
    /**
    * The global product ID. 
    */
    'id'?: string;
    /**
    * global product SKU information.
    */
    'skus'?: Array<Product202309SearchGlobalProductsResponseDataGlobalProductsSkus>;
    /**
    * The status of the global product. including PUBLISHED,UNPUBLISHED,DRAFT,DETELTED
    */
    'status'?: string;
    /**
    * The global product name.
    */
    'title'?: string;
    /**
    * The time when the global product status is updated. Unix timestamp GMT (UTC+00:00). This timestamp is used across all API requests. Developers can use this convert to local time.
    */
    'updateTime'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309SearchGlobalProductsResponseDataGlobalProductsSkus>"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "title",
            "baseName": "title",
            "type": "string"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchGlobalProductsResponseDataGlobalProducts.attributeTypeMap;
    }
}

