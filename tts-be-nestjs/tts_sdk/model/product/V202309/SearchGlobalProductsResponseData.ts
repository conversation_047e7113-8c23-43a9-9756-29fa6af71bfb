/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309SearchGlobalProductsResponseDataGlobalProducts } from './SearchGlobalProductsResponseDataGlobalProducts';

export class Product202309SearchGlobalProductsResponseData {
    /**
    * The searched global product list.
    */
    'globalProducts'?: Array<Product202309SearchGlobalProductsResponseDataGlobalProducts>;
    /**
    * The pagination token is a cursor used for pagination. The token is returned in the previous pagination query to determine the current position. 
    */
    'nextPageToken'?: string;
    /**
    * The total number of global products searched.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "globalProducts",
            "baseName": "global_products",
            "type": "Array<Product202309SearchGlobalProductsResponseDataGlobalProducts>"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchGlobalProductsResponseData.attributeTypeMap;
    }
}

