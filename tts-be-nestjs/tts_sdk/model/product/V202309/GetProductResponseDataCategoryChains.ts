/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataCategoryChains {
    /**
    * The ID of the category of this product.
    */
    'id'?: string;
    /**
    * A flag to indicate if the category is a leaf category.   **Note**: You can only create or edit products that belong to a leaf category.
    */
    'isLeaf'?: boolean;
    /**
    * The name of the category in the country where the shop operates.
    */
    'localName'?: string;
    /**
    * The parent category ID.  For the root category, the parent ID is `0`.
    */
    'parentId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isLeaf",
            "baseName": "is_leaf",
            "type": "boolean"
        },
        {
            "name": "localName",
            "baseName": "local_name",
            "type": "string"
        },
        {
            "name": "parentId",
            "baseName": "parent_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataCategoryChains.attributeTypeMap;
    }
}

