/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataSkusPrice {
    /**
    * The currency of the SKU price. Possible values based on the region: - BRL:  Brazil - EUR: France, Germany, Ireland, Italy, Spain - GBP: United Kingdom - IDR: Indonesia - JPY: Japan - MXN: Mexico - MYR: Malaysia - PHP: Philippines - SGD: Singapore - THB: Thailand - USD: United States - VND: Vietnam
    */
    'currency'?: string;
    /**
    * The SKU\'s selling price, inclusive of tax. Applicable only for cross-border sellers from China.
    */
    'salePrice'?: string;
    /**
    * The SKU\'s selling price, exclusive of tax.
    */
    'taxExclusivePrice'?: string;
    /**
    * The unit price of the SKU.  You can display the unit price to facilitate easier price comparisons across different products and packaging sizes. Applicable only for the EU market.  **Note**:  - This value is available only if you have defined the elements used to calculate this price when creating the product. - Unit price = Selling price/(SKU unit count/base unit count)
    */
    'unitPrice'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "salePrice",
            "baseName": "sale_price",
            "type": "string"
        },
        {
            "name": "taxExclusivePrice",
            "baseName": "tax_exclusive_price",
            "type": "string"
        },
        {
            "name": "unitPrice",
            "baseName": "unit_price",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusPrice.attributeTypeMap;
    }
}

