/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataSkusCombinedSkus {
    /**
    * The ID of the source product included in the combined listing.
    */
    'productId'?: string;
    /**
    * The quantity of the source SKU included in the combined listing.
    */
    'skuCount'?: number;
    /**
    * The ID of the source SKU included in the combined listing.
    */
    'skuId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "skuCount",
            "baseName": "sku_count",
            "type": "number"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusCombinedSkus.attributeTypeMap;
    }
}

