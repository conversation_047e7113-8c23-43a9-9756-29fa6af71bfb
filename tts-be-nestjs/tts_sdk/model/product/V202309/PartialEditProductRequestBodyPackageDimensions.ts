/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309PartialEditProductRequestBodyPackageDimensions {
    /**
    * The package height. A positive whole number.
    */
    'height'?: string;
    /**
    * The package length. A positive whole number.
    */
    'length'?: string;
    /**
    * The unit for the package dimensions. Possible values based on region: - US: CENTIMETER, INCH - Other regions: CENTIMETER    **Note**: You must use the same system of measurement (metric system or imperial system) for `package_weight` and `package_dimensions`. In other words, if you are using KILOGRAM for the weight, you must use CENTIMETER for the dimensions.
    */
    'unit'?: string;
    /**
    * The package width. A positive whole number.
    */
    'width'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "string"
        },
        {
            "name": "length",
            "baseName": "length",
            "type": "string"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductRequestBodyPackageDimensions.attributeTypeMap;
    }
}

