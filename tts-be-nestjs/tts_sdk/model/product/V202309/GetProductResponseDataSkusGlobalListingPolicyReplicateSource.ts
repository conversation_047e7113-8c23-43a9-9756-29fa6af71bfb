/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataSkusGlobalListingPolicyReplicateSource {
    /**
    * The ID of the source product.
    */
    'productId'?: string;
    /**
    * The shop ID of the source product.
    */
    'shopId'?: string;
    /**
    * The SKU ID of the source product.
    */
    'skuId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "shopId",
            "baseName": "shop_id",
            "type": "string"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusGlobalListingPolicyReplicateSource.attributeTypeMap;
    }
}

