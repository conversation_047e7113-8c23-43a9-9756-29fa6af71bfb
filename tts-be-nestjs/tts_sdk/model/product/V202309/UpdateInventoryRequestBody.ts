/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdateInventoryRequestBodySkus } from './UpdateInventoryRequestBodySkus';

export class Product202309UpdateInventoryRequestBody {
    /**
    * A list of Stock Keeping Units (SKUs) used to identify distinct variants of the product.
    */
    'skus'?: Array<Product202309UpdateInventoryRequestBodySkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309UpdateInventoryRequestBodySkus>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateInventoryRequestBody.attributeTypeMap;
    }
}

