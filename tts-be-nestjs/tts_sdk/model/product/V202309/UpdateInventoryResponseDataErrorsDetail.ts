/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdateInventoryResponseDataErrorsDetailExtraErrors } from './UpdateInventoryResponseDataErrorsDetailExtraErrors';

export class Product202309UpdateInventoryResponseDataErrorsDetail {
    /**
    * A list of secondary errors or issues related to the main error.
    */
    'extraErrors'?: Array<Product202309UpdateInventoryResponseDataErrorsDetailExtraErrors>;
    /**
    * The ID of the SKU where the error occurred.
    */
    'skuId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "extraErrors",
            "baseName": "extra_errors",
            "type": "Array<Product202309UpdateInventoryResponseDataErrorsDetailExtraErrors>"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateInventoryResponseDataErrorsDetail.attributeTypeMap;
    }
}

