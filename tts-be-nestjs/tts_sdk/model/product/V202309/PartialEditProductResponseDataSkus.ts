/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PartialEditProductResponseDataSkusSalesAttributes } from './PartialEditProductResponseDataSkusSalesAttributes';

export class Product202309PartialEditProductResponseDataSkus {
    /**
    * An external identifier used in an external ecommerce platform. This is used to associate the SKU between TikTok Shop and the external ecommerce platform. 
    */
    'externalSkuId'?: string;
    /**
    * The SKU ID generated by TikTok Shop. One product can contain multiple SKU IDs.
    */
    'id'?: string;
    /**
    * A list of attributes  (e.g. size, color, length) that define each variant of a product.
    */
    'salesAttributes'?: Array<Product202309PartialEditProductResponseDataSkusSalesAttributes>;
    /**
    * An internal code/name for managing SKUs, not visible to buyers.  
    */
    'sellerSku'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "externalSkuId",
            "baseName": "external_sku_id",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "salesAttributes",
            "baseName": "sales_attributes",
            "type": "Array<Product202309PartialEditProductResponseDataSkusSalesAttributes>"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductResponseDataSkus.attributeTypeMap;
    }
}

