/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309GetProductResponseDataSkusSalesAttributesSkuImg } from './GetProductResponseDataSkusSalesAttributesSkuImg';
import { Product202309GetProductResponseDataSkusSalesAttributesSupplementarySkuImages } from './GetProductResponseDataSkusSalesAttributesSupplementarySkuImages';

export class Product202309GetProductResponseDataSkusSalesAttributes {
    /**
    * The sales attribute ID.
    */
    'id'?: string;
    /**
    * The sales attribute name.
    */
    'name'?: string;
    'skuImg'?: Product202309GetProductResponseDataSkusSalesAttributesSkuImg;
    /**
    * A list of supplementary images for each value (e.g. red) of the primary sales attribute (e.g. color) to provide multiple views or details of the product for that attribute value. These appear in the product options gallery on TikTok Shop.  Applicable only for the US market.
    */
    'supplementarySkuImages'?: Array<Product202309GetProductResponseDataSkusSalesAttributesSupplementarySkuImages>;
    /**
    * The sales attribute value ID.
    */
    'valueId'?: string;
    /**
    * The sales attribute value name.
    */
    'valueName'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "skuImg",
            "baseName": "sku_img",
            "type": "Product202309GetProductResponseDataSkusSalesAttributesSkuImg"
        },
        {
            "name": "supplementarySkuImages",
            "baseName": "supplementary_sku_images",
            "type": "Array<Product202309GetProductResponseDataSkusSalesAttributesSupplementarySkuImages>"
        },
        {
            "name": "valueId",
            "baseName": "value_id",
            "type": "string"
        },
        {
            "name": "valueName",
            "baseName": "value_name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusSalesAttributes.attributeTypeMap;
    }
}

