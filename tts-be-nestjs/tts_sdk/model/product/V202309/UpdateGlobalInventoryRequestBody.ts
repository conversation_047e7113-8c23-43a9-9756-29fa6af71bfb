/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdateGlobalInventoryRequestBodyGlobalSkus } from './UpdateGlobalInventoryRequestBodyGlobalSkus';

export class Product202309UpdateGlobalInventoryRequestBody {
    /**
    * The list of global SKUs that need to be updated.
    */
    'globalSkus'?: Array<Product202309UpdateGlobalInventoryRequestBodyGlobalSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "globalSkus",
            "baseName": "global_skus",
            "type": "Array<Product202309UpdateGlobalInventoryRequestBodyGlobalSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateGlobalInventoryRequestBody.attributeTypeMap;
    }
}

