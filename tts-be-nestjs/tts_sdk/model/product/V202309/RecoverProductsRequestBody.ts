/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309RecoverProductsRequestBody {
    /**
    * The product IDs to recover. Max number of IDs: 20.
    */
    'productIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "productIds",
            "baseName": "product_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecoverProductsRequestBody.attributeTypeMap;
    }
}

