/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309InventorySearchResponseDataInventorySkus } from './InventorySearchResponseDataInventorySkus';

export class Product202309InventorySearchResponseDataInventory {
    /**
    * The ID of the requested product.
    */
    'productId'?: string;
    /**
    * The list of requested SKUs.
    */
    'skus'?: Array<Product202309InventorySearchResponseDataInventorySkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309InventorySearchResponseDataInventorySkus>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309InventorySearchResponseDataInventory.attributeTypeMap;
    }
}

