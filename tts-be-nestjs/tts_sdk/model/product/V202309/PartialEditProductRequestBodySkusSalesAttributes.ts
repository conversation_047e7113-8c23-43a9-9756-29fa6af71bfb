/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PartialEditProductRequestBodySkusSalesAttributesSkuImg } from './PartialEditProductRequestBodySkusSalesAttributesSkuImg';
import { Product202309PartialEditProductRequestBodySkusSalesAttributesSupplementarySkuImages } from './PartialEditProductRequestBodySkusSalesAttributesSupplementarySkuImages';

export class Product202309PartialEditProductRequestBodySkusSalesAttributes {
    /**
    * The ID of the sales attribute. This is either a built-in sales attribute ID from [Get Attributes API](https://partner.tiktokshop.com/docv2/page/6509c5784a0bb702c0561cc8) or a custom attribute ID returned after calling [Create Product API](https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a).
    */
    'id'?: string;
    /**
    * A self-defined custom sales attribute name if the existing attributes do not satisfy your needs. The system will auto-generate an ID after editing.  **Note**:  - Do not include sensitive characters. - Max length: 20 characters 
    */
    'name'?: string;
    'skuImg'?: Product202309PartialEditProductRequestBodySkusSalesAttributesSkuImg;
    /**
    * A list of supplementary images for each value (e.g. red) of the primary sales attribute (e.g. color) to provide multiple views or details of the product for that attribute value. These appear in the product options gallery on TikTok Shop.  **Note**: - Max number of image URIs: 8. - Arrange your image URIs in the sequence that they should appear on TikTok Shop. - Applicable only for the US market.
    */
    'supplementarySkuImages'?: Array<Product202309PartialEditProductRequestBodySkusSalesAttributesSupplementarySkuImages>;
    /**
    * The ID of the sales attribute value. This is either a built-in sales attribute value ID from [Get Attributes API](https://partner.tiktokshop.com/docv2/page/6509c5784a0bb702c0561cc8) or a custom sales attribute value ID returned after calling [Create Product API](https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a).
    */
    'valueId'?: string;
    /**
    * A self-defined custom sales attribute value if the existing values do not satisfy your needs. The system will auto-generate an ID after editing.  **Note**: - No duplicates allowed under the same attribute. - Max length: 50 characters. 
    */
    'valueName'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "skuImg",
            "baseName": "sku_img",
            "type": "Product202309PartialEditProductRequestBodySkusSalesAttributesSkuImg"
        },
        {
            "name": "supplementarySkuImages",
            "baseName": "supplementary_sku_images",
            "type": "Array<Product202309PartialEditProductRequestBodySkusSalesAttributesSupplementarySkuImages>"
        },
        {
            "name": "valueId",
            "baseName": "value_id",
            "type": "string"
        },
        {
            "name": "valueName",
            "baseName": "value_name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductRequestBodySkusSalesAttributes.attributeTypeMap;
    }
}

