/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309SearchProductsResponseDataProductsSkusInventory } from './SearchProductsResponseDataProductsSkusInventory';
import { Product202309SearchProductsResponseDataProductsSkusPrice } from './SearchProductsResponseDataProductsSkusPrice';

export class Product202309SearchProductsResponseDataProductsSkus {
    /**
    * The SKU ID generated by TikTok Shop.
    */
    'id'?: string;
    /**
    * SKU inventory information.
    */
    'inventory'?: Array<Product202309SearchProductsResponseDataProductsSkusInventory>;
    'price'?: Product202309SearchProductsResponseDataProductsSkusPrice;
    /**
    * An internal code/name for managing SKUs, not visible to buyers. 
    */
    'sellerSku'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "inventory",
            "baseName": "inventory",
            "type": "Array<Product202309SearchProductsResponseDataProductsSkusInventory>"
        },
        {
            "name": "price",
            "baseName": "price",
            "type": "Product202309SearchProductsResponseDataProductsSkusPrice"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchProductsResponseDataProductsSkus.attributeTypeMap;
    }
}

