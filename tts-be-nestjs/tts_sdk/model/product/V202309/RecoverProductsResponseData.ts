/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309RecoverProductsResponseDataErrors } from './RecoverProductsResponseDataErrors';

export class Product202309RecoverProductsResponseData {
    /**
    * The list of errors that occurred.
    */
    'errors'?: Array<Product202309RecoverProductsResponseDataErrors>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Product202309RecoverProductsResponseDataErrors>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecoverProductsResponseData.attributeTypeMap;
    }
}

