/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309RecommendCategoryRequestBodyImages } from './RecommendCategoryRequestBodyImages';

export class Product202309RecommendCategoryRequestBody {
    /**
    * The category tree version to use for this product. Possible values based on region: - US: `v2`, represents the 7-level category tree.   **Important**: For US shops, you must pass `v2` when using this API. - Other regions: `v1`, represents the 3-level category tree. Default: `v1`
    */
    'categoryVersion'?: string;
    /**
    * The product description in HTML format.  **Note**: - The content must conform to the [HTML syntax](https://html.spec.whatwg.org/). All HTML tags are accepted but to optimize display on the TikTok Shop product detail page, the system will automatically convert certain tags into alternative formats, such as rendering `<table>` tags as images. - Max length: 10,000 characters. - Images must use TikTok Shop image URLs, not exceed 4000px, and include `src`, `width`, and `height` attributes.  **Recommendations**:  - If you are syncing a pre-existing description from another platform, include the full HTML source description here. - Provide a detailed description, ideally over 300 characters. - Include 3-5 key selling points, each under 250 characters, with supporting images. - Use 1600x1600 px for the image dimensions.
    */
    'description'?: string;
    /**
    * Product images, including gallery images, images that appear in the description, product variant images.
    */
    'images'?: Array<Product202309RecommendCategoryRequestBodyImages>;
    /**
    * Recommend categories that belong to the specified platform. Possible values: - TIKTOK_SHOP - TOKOPEDIA Default: TIKTOK_SHOP  Applicable only for sellers that migrated from Tokopedia.
    */
    'listingPlatform'?: string;
    /**
    * The product title.  Title length: - DE, ES, FR, IE, IT, JP, UK, US: [1, 255]  - BR, MX: [1, 300] - Other regions: [25, 255]
    */
    'productTitle'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "categoryVersion",
            "baseName": "category_version",
            "type": "string"
        },
        {
            "name": "description",
            "baseName": "description",
            "type": "string"
        },
        {
            "name": "images",
            "baseName": "images",
            "type": "Array<Product202309RecommendCategoryRequestBodyImages>"
        },
        {
            "name": "listingPlatform",
            "baseName": "listing_platform",
            "type": "string"
        },
        {
            "name": "productTitle",
            "baseName": "product_title",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecommendCategoryRequestBody.attributeTypeMap;
    }
}

