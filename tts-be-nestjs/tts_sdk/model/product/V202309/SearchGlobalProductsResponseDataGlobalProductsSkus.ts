/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309SearchGlobalProductsResponseDataGlobalProductsSkus {
    /**
    * The global SKU ID. 
    */
    'id'?: string;
    /**
    * The seller SKU entered when creating or editing the global product.
    */
    'sellerSku'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchGlobalProductsResponseDataGlobalProductsSkus.attributeTypeMap;
    }
}

