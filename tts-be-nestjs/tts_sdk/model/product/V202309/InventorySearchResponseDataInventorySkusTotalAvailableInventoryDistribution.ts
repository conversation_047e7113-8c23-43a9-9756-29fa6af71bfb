/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCampaignInventory } from './InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCampaignInventory';
import { Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory } from './InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory';
import { Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionInShopInventory } from './InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionInShopInventory';

export class Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistribution {
    /**
    * Inventory that is allocated to a specific campaign.
    */
    'campaignInventory'?: Array<Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCampaignInventory>;
    /**
    * Inventory that is allocated to TikTok creators.
    */
    'creatorInventory'?: Array<Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory>;
    'inShopInventory'?: Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionInShopInventory;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "campaignInventory",
            "baseName": "campaign_inventory",
            "type": "Array<Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCampaignInventory>"
        },
        {
            "name": "creatorInventory",
            "baseName": "creator_inventory",
            "type": "Array<Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory>"
        },
        {
            "name": "inShopInventory",
            "baseName": "in_shop_inventory",
            "type": "Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionInShopInventory"
        }    ];

    static getAttributeTypeMap() {
        return Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistribution.attributeTypeMap;
    }
}

