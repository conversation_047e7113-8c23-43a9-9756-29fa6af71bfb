/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309PartialEditProductRequestBodySkusIdentifierCode {
    /**
    * The identifier code.  **Format**: - GTIN: 14 digits  - EAN: 8, 13, or 14 digits  - UPC: 12 digits  - ISBN: 13 digits (supports \'X\' in uppercase as the last digit)  **Note**: The identifier code must be unique for each SKU, with no repetition allowed.
    */
    'code'?: string;
    /**
    * The type of identifier code. Possible values: - GTIN - EAN - UPC - ISBN
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductRequestBodySkusIdentifierCode.attributeTypeMap;
    }
}

