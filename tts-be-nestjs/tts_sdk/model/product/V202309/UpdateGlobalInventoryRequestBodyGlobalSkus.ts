/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdateGlobalInventoryRequestBodyGlobalSkusInventory } from './UpdateGlobalInventoryRequestBodyGlobalSkusInventory';

export class Product202309UpdateGlobalInventoryRequestBodyGlobalSkus {
    /**
    * The global SKU ID in TikTok Shop.
    */
    'id'?: string;
    /**
    * Global SKU inventory information.
    */
    'inventory'?: Array<Product202309UpdateGlobalInventoryRequestBodyGlobalSkusInventory>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "inventory",
            "baseName": "inventory",
            "type": "Array<Product202309UpdateGlobalInventoryRequestBodyGlobalSkusInventory>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateGlobalInventoryRequestBodyGlobalSkus.attributeTypeMap;
    }
}

