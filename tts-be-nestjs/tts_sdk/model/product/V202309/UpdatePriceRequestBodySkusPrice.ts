/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309UpdatePriceRequestBodySkusPrice {
    /**
    * **Local shops** The SKU\'s selling price displayed on the product page before any discounts. Minimum/maximum prices: - BR: [0.5, 10000] - DE: [0.01, 6,300] - ES: [0.01, 6,300] - FR: [0.01, 6,300] - ID: [100, 100,000,000] (A discounted price less than 2000 IDR may lead to a negative balance) - IE: [0.01, 6,300] - IT: [0.01, 6,300] - JP: [1, 500,000]  - MX: [1, 50,000] - MY: [0.01, 320,000] - PH: [0.01, 400,000] - SG: [0.01, 10,000] - TH: [30, 260,000] - UK: [0.01, 5,600] - US: [0.01, 7,600] - VN: [1, 180,000,000]  **Cross-border shops** The SKU\'s selling price, exclusive of tax. The final price displayed on the product page will automatically include applicable charges such as tax. Minimum/maximum prices: - DE: [0.01, 150] - ES: [0.01, 150] - FR: [0.01, 150] - IE: [0.01, 150] - IT: [0.01, 150] - MY: [0.01, 320,000] - PH: [0.01, 400,000] - SG: [0.01, 400] - TH: [0.01, 260,000] - UK: [0.01, 134.5] - US: [0.01, 800] - VN: [1, 1,000,000]
    */
    'amount'?: string;
    /**
    * The currency of the SKU price. Possible values based on the region: - BRL: Brazil - EUR: France, Germany, Ireland, Italy, Spain - GBP: United Kingdom - IDR: Indonesia - JPY: Japan - MXN: Mexico - MYR: Malaysia - PHP: Philippines - SGD: Singapore - THB: Thailand - USD: United States - VND: Vietnam
    */
    'currency'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "amount",
            "baseName": "amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdatePriceRequestBodySkusPrice.attributeTypeMap;
    }
}

