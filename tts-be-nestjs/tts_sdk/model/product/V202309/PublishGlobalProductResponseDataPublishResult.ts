/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PublishGlobalProductResponseDataPublishResultFailReasons } from './PublishGlobalProductResponseDataPublishResultFailReasons';

export class Product202309PublishGlobalProductResponseDataPublishResult {
    /**
    * The list of errors that occurred.
    */
    'failReasons'?: Array<Product202309PublishGlobalProductResponseDataPublishResultFailReasons>;
    /**
    * The market where the product is published. Possible values: - DE: Germany - ES: Spain - FR: France - GB: United Kingdom - ID: Indonesia - IE: Ireland - IT: Italy - MY: Malaysia - PH: Philippines - SG: Singapore - TH: Thailand - US: United States - VN: Vietnam
    */
    'region'?: string;
    /**
    * The status of publishing the product to the market. Possible values: - SUCCESS: The global product was successfully published to the local shop, submitted for listing, and is now under review. - DRAFT: The global product was saved as a draft local product due to validation errors. - FAILED: Synchronization of the global product to the local shop was unsuccessful.
    */
    'status'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "failReasons",
            "baseName": "fail_reasons",
            "type": "Array<Product202309PublishGlobalProductResponseDataPublishResultFailReasons>"
        },
        {
            "name": "region",
            "baseName": "region",
            "type": "string"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PublishGlobalProductResponseDataPublishResult.attributeTypeMap;
    }
}

