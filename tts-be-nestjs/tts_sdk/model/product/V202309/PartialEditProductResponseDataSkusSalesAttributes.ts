/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309PartialEditProductResponseDataSkusSalesAttributes {
    /**
    * The sales attribute ID.  If you included the custom sales attribute name in the request, this is a newly generated ID.
    */
    'id'?: string;
    /**
    * The sales attribute value ID.  If you included the custom sales attribute value name in the request, this is a newly generated ID.
    */
    'valueId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "valueId",
            "baseName": "value_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductResponseDataSkusSalesAttributes.attributeTypeMap;
    }
}

