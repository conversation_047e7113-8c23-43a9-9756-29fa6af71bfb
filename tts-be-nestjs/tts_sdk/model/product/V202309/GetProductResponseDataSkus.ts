/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309GetProductResponseDataSkusCombinedSkus } from './GetProductResponseDataSkusCombinedSkus';
import { Product202309GetProductResponseDataSkusExternalListPrices } from './GetProductResponseDataSkusExternalListPrices';
import { Product202309GetProductResponseDataSkusGlobalListingPolicy } from './GetProductResponseDataSkusGlobalListingPolicy';
import { Product202309GetProductResponseDataSkusIdentifierCode } from './GetProductResponseDataSkusIdentifierCode';
import { Product202309GetProductResponseDataSkusInventory } from './GetProductResponseDataSkusInventory';
import { Product202309GetProductResponseDataSkusListPrice } from './GetProductResponseDataSkusListPrice';
import { Product202309GetProductResponseDataSkusPreSale } from './GetProductResponseDataSkusPreSale';
import { Product202309GetProductResponseDataSkusPrice } from './GetProductResponseDataSkusPrice';
import { Product202309GetProductResponseDataSkusSalesAttributes } from './GetProductResponseDataSkusSalesAttributes';

export class Product202309GetProductResponseDataSkus {
    /**
    * If this SKU is a combined listing, this object contains the list of individual SKUs that form a product bundle (e.g. gift basket, starter pack).
    */
    'combinedSkus'?: Array<Product202309GetProductResponseDataSkusCombinedSkus>;
    /**
    * The SKU list price (e.g. MSRP, RRP) or original price information on external ecommerce platforms. Applicable only for selected sellers in the US market.  **Note**: This value may appear as the strikethrough price on the product page. However, whether the strikethrough price is shown and the amount shown are subject to the audit team\'s review and decision based on various pricing information.
    */
    'externalListPrices'?: Array<Product202309GetProductResponseDataSkusExternalListPrices>;
    /**
    * An external identifier used in an external ecommerce platform. This is used to associate the SKU between TikTok Shop and the external ecommerce platform.
    */
    'externalSkuId'?: string;
    /**
    * A list of URLs for third-party product listing pages where consumers can place orders.
    */
    'externalUrls'?: Array<string>;
    /**
    * A list of up to 10 additional identifier codes if the SKU is a combined listing (in other words, a product bundle) containing multiple individual SKUs.  **Format**: GTIN: 14 digits  EAN: 8, 13, or 14 digits  UPC: 12 digits  ISBN: 13 digits (supports \'X\' in uppercase as the last digit)  **Note**:  - Applicable only for the EU market.  - The identifier code must be unique for each SKU, with no repetition allowed.
    */
    'extraIdentifierCodes'?: Array<string>;
    'globalListingPolicy'?: Product202309GetProductResponseDataSkusGlobalListingPolicy;
    /**
    * The SKU ID generated by TikTok Shop.
    */
    'id'?: string;
    'identifierCode'?: Product202309GetProductResponseDataSkusIdentifierCode;
    /**
    * SKU inventory details.
    */
    'inventory'?: Array<Product202309GetProductResponseDataSkusInventory>;
    'listPrice'?: Product202309GetProductResponseDataSkusListPrice;
    'preSale'?: Product202309GetProductResponseDataSkusPreSale;
    'price'?: Product202309GetProductResponseDataSkusPrice;
    /**
    * A list of attributes  (e.g. size, color, length) that define each variant of a product.
    */
    'salesAttributes'?: Array<Product202309GetProductResponseDataSkusSalesAttributes>;
    /**
    * An internal code/name for managing SKUs, not visible to buyers. 
    */
    'sellerSku'?: string;
    /**
    * The total quantity/volume of the product represented by the SKU. For example, if the SKU represents 500ml of water, this value would be 500 if the unit type is defined as ml. Applicable only for the EU market.  **Note**:  - This is mainly used to calculate the unit price of the SKU. - Unit price = Selling price/(SKU unit count/base unit count).
    */
    'skuUnitCount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "combinedSkus",
            "baseName": "combined_skus",
            "type": "Array<Product202309GetProductResponseDataSkusCombinedSkus>"
        },
        {
            "name": "externalListPrices",
            "baseName": "external_list_prices",
            "type": "Array<Product202309GetProductResponseDataSkusExternalListPrices>"
        },
        {
            "name": "externalSkuId",
            "baseName": "external_sku_id",
            "type": "string"
        },
        {
            "name": "externalUrls",
            "baseName": "external_urls",
            "type": "Array<string>"
        },
        {
            "name": "extraIdentifierCodes",
            "baseName": "extra_identifier_codes",
            "type": "Array<string>"
        },
        {
            "name": "globalListingPolicy",
            "baseName": "global_listing_policy",
            "type": "Product202309GetProductResponseDataSkusGlobalListingPolicy"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "identifierCode",
            "baseName": "identifier_code",
            "type": "Product202309GetProductResponseDataSkusIdentifierCode"
        },
        {
            "name": "inventory",
            "baseName": "inventory",
            "type": "Array<Product202309GetProductResponseDataSkusInventory>"
        },
        {
            "name": "listPrice",
            "baseName": "list_price",
            "type": "Product202309GetProductResponseDataSkusListPrice"
        },
        {
            "name": "preSale",
            "baseName": "pre_sale",
            "type": "Product202309GetProductResponseDataSkusPreSale"
        },
        {
            "name": "price",
            "baseName": "price",
            "type": "Product202309GetProductResponseDataSkusPrice"
        },
        {
            "name": "salesAttributes",
            "baseName": "sales_attributes",
            "type": "Array<Product202309GetProductResponseDataSkusSalesAttributes>"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        },
        {
            "name": "skuUnitCount",
            "baseName": "sku_unit_count",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkus.attributeTypeMap;
    }
}

