/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataSkusSalesAttributesSupplementarySkuImages {
    /**
    * The image height. Unit: px
    */
    'height'?: number;
    /**
    * The URLs to view the image thumbnails.
    */
    'thumbUrls'?: Array<string>;
    /**
    * The URI of the image.
    */
    'uri'?: string;
    /**
    * The URLs to view the images.
    */
    'urls'?: Array<string>;
    /**
    * The image width. Unit: px
    */
    'width'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "number"
        },
        {
            "name": "thumbUrls",
            "baseName": "thumb_urls",
            "type": "Array<string>"
        },
        {
            "name": "uri",
            "baseName": "uri",
            "type": "string"
        },
        {
            "name": "urls",
            "baseName": "urls",
            "type": "Array<string>"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusSalesAttributesSupplementarySkuImages.attributeTypeMap;
    }
}

