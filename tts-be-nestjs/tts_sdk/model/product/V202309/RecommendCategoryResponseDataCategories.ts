/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309RecommendCategoryResponseDataCategories {
    /**
    * The category ID.
    */
    'id'?: string;
    /**
    * A flag to indicate if the category is a leaf category.   **Note**: You can only create or edit products that belong to a leaf category.
    */
    'isLeaf'?: boolean;
    /**
    * The category level.
    */
    'level'?: number;
    /**
    * The category name.
    */
    'name'?: string;
    /**
    * The shop\'s permission status for this category. Possible values: - AVAILABLE: You have the permission to create products in this category. - INVITE_ONLY: This is a restricted category and you do not have permission to use it. Submit an application through the Qualification Center on TikTok Shop Seller Center to gain access. In Seller Center, `INVITE_ONLY` is also known as \"restricted\".
    */
    'permissionStatuses'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isLeaf",
            "baseName": "is_leaf",
            "type": "boolean"
        },
        {
            "name": "level",
            "baseName": "level",
            "type": "number"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "permissionStatuses",
            "baseName": "permission_statuses",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecommendCategoryResponseDataCategories.attributeTypeMap;
    }
}

