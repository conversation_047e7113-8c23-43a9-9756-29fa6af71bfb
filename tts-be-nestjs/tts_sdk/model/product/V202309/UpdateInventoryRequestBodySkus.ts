/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309UpdateInventoryRequestBodySkusInventory } from './UpdateInventoryRequestBodySkusInventory';

export class Product202309UpdateInventoryRequestBodySkus {
    /**
    * The SKU ID generated by TikTok Shop. One product can contain multiple SKU IDs.  **Note**:  - The SKU ID must belong to a product with the `ACTIVATE` status. - If you are updating multiple SKUs, all the SKU IDs must belong to the same product.
    */
    'id'?: string;
    /**
    * SKU inventory information.
    */
    'inventory'?: Array<Product202309UpdateInventoryRequestBodySkusInventory>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "inventory",
            "baseName": "inventory",
            "type": "Array<Product202309UpdateInventoryRequestBodySkusInventory>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateInventoryRequestBodySkus.attributeTypeMap;
    }
}

