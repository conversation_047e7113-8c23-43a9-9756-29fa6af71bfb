/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309SearchProductsResponseDataProducts } from './SearchProductsResponseDataProducts';

export class Product202309SearchProductsResponseData {
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The list of products that meet the query conditions.
    */
    'products'?: Array<Product202309SearchProductsResponseDataProducts>;
    /**
    * The total number of products that meet the query conditions.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "products",
            "baseName": "products",
            "type": "Array<Product202309SearchProductsResponseDataProducts>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchProductsResponseData.attributeTypeMap;
    }
}

