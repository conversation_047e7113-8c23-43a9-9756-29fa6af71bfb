/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309UpdateInventoryResponseDataErrorsDetailExtraErrors {
    /**
    * The secondary error code.
    */
    'code'?: number;
    /**
    * The secondary error message.
    */
    'message'?: string;
    /**
    * The ID of the warehouse  where the error occurred.
    */
    'warehouseId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "number"
        },
        {
            "name": "message",
            "baseName": "message",
            "type": "string"
        },
        {
            "name": "warehouseId",
            "baseName": "warehouse_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateInventoryResponseDataErrorsDetailExtraErrors.attributeTypeMap;
    }
}

