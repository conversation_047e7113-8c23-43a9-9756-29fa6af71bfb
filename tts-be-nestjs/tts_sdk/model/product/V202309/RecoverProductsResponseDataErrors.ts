/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309RecoverProductsResponseDataErrorsDetail } from './RecoverProductsResponseDataErrorsDetail';

export class Product202309RecoverProductsResponseDataErrors {
    /**
    * The main error code.
    */
    'code'?: number;
    'detail'?: Product202309RecoverProductsResponseDataErrorsDetail;
    /**
    * The main error message.
    */
    'message'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "number"
        },
        {
            "name": "detail",
            "baseName": "detail",
            "type": "Product202309RecoverProductsResponseDataErrorsDetail"
        },
        {
            "name": "message",
            "baseName": "message",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309RecoverProductsResponseDataErrors.attributeTypeMap;
    }
}

