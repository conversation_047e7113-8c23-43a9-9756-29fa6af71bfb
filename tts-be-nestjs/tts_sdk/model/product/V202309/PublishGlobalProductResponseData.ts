/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PublishGlobalProductResponseDataProducts } from './PublishGlobalProductResponseDataProducts';
import { Product202309PublishGlobalProductResponseDataPublishResult } from './PublishGlobalProductResponseDataPublishResult';

export class Product202309PublishGlobalProductResponseData {
    /**
    * The local products converted from the global product.
    */
    'products'?: Array<Product202309PublishGlobalProductResponseDataProducts>;
    /**
    * Results of publishing the global product.
    */
    'publishResult'?: Array<Product202309PublishGlobalProductResponseDataPublishResult>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "products",
            "baseName": "products",
            "type": "Array<Product202309PublishGlobalProductResponseDataProducts>"
        },
        {
            "name": "publishResult",
            "baseName": "publish_result",
            "type": "Array<Product202309PublishGlobalProductResponseDataPublishResult>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PublishGlobalProductResponseData.attributeTypeMap;
    }
}

