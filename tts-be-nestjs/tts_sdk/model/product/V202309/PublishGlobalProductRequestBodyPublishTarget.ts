/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PublishGlobalProductRequestBodyPublishTargetSkus } from './PublishGlobalProductRequestBodyPublishTargetSkus';

export class Product202309PublishGlobalProductRequestBodyPublishTarget {
    /**
    * A comma-delimited list of manufacturer IDs. Retrieve the IDs from the [Search Manufacturers API](67066a580dcee902fa03ccf9). Default: The IDs provided when the global product was created. **Note**: Required for the EU market.
    */
    'manufacturerIds'?: Array<string>;
    /**
    * The new market where you want to publish the global product. Possible values: - DE: Germany - ES: Spain - FR: France - GB: United Kingdom - ID: Indonesia - IE: Ireland - IT: Italy - MY: Malaysia - PH: Philippines - SG: Singapore - TH: Thailand - US: United States - VN: Vietnam  **Note**: You can only publish in each market once.
    */
    'region'?: string;
    /**
    * A comma-delimited list of responsible person IDs. Retrieve the IDs from the [Search Responsible Persons API](67066a55f17b7d02f95d2fb1). Default: The IDs provided when the global product was created. **Note**: Required and applicable only for the EU market.
    */
    'responsiblePersonIds'?: Array<string>;
    /**
    * The SKUs to be published in the specified market. Max count: 300 for US; 100 for other regions.
    */
    'skus'?: Array<Product202309PublishGlobalProductRequestBodyPublishTargetSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "manufacturerIds",
            "baseName": "manufacturer_ids",
            "type": "Array<string>"
        },
        {
            "name": "region",
            "baseName": "region",
            "type": "string"
        },
        {
            "name": "responsiblePersonIds",
            "baseName": "responsible_person_ids",
            "type": "Array<string>"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309PublishGlobalProductRequestBodyPublishTargetSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PublishGlobalProductRequestBodyPublishTarget.attributeTypeMap;
    }
}

