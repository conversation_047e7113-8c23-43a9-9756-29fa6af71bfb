/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PartialEditProductRequestBodySizeChartImage } from './PartialEditProductRequestBodySizeChartImage';
import { Product202309PartialEditProductRequestBodySizeChartTemplate } from './PartialEditProductRequestBodySizeChartTemplate';

export class Product202309PartialEditProductRequestBodySizeChart {
    'image'?: Product202309PartialEditProductRequestBodySizeChartImage;
    'template'?: Product202309PartialEditProductRequestBodySizeChartTemplate;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "image",
            "baseName": "image",
            "type": "Product202309PartialEditProductRequestBodySizeChartImage"
        },
        {
            "name": "template",
            "baseName": "template",
            "type": "Product202309PartialEditProductRequestBodySizeChartTemplate"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductRequestBodySizeChart.attributeTypeMap;
    }
}

