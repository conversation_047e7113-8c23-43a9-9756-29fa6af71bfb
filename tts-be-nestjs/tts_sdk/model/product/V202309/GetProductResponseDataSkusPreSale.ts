/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309GetProductResponseDataSkusPreSaleFulfillmentType } from './GetProductResponseDataSkusPreSaleFulfillmentType';

export class Product202309GetProductResponseDataSkusPreSale {
    'fulfillmentType'?: Product202309GetProductResponseDataSkusPreSaleFulfillmentType;
    /**
    * The type of pre-sale. Possible values: - PRE_ORDER: The product is not yet available or released. Fulfillment will take a longer time.
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "fulfillmentType",
            "baseName": "fulfillment_type",
            "type": "Product202309GetProductResponseDataSkusPreSaleFulfillmentType"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusPreSale.attributeTypeMap;
    }
}

