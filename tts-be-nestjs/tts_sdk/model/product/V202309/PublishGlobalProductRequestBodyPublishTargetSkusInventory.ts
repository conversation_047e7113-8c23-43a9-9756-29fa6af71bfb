/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309PublishGlobalProductRequestBodyPublishTargetSkusInventory {
    /**
    * The SKU quantity available in the warehouse. Valid range: [1, 99,999]  **Note**: If the local SKU inventory quantity exceeds the global SKU quantity, the global SKU quantity will be updated to be the sum of all local inventories.
    */
    'quantity'?: number;
    /**
    * The warehouse ID. Retrieve this value from the [Get Warehouse List API](https://partner.tiktokshop.com/docv2/page/650aa418defece02be6e66b6).  Default: - Sellers without multiple warehouses: The available warehouses will be used, prioritizing local warehouses in the specified market over warehouses in the seller\'s base country. - Sellers with multiple warehouses: The global warehouse ID
    */
    'warehouseId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        },
        {
            "name": "warehouseId",
            "baseName": "warehouse_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PublishGlobalProductRequestBodyPublishTargetSkusInventory.attributeTypeMap;
    }
}

