/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PublishGlobalProductRequestBodyPublishTarget } from './PublishGlobalProductRequestBodyPublishTarget';

export class Product202309PublishGlobalProductRequestBody {
    /**
    * Target markets for publishing global products. 
    */
    'publishTarget'?: Array<Product202309PublishGlobalProductRequestBodyPublishTarget>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "publishTarget",
            "baseName": "publish_target",
            "type": "Array<Product202309PublishGlobalProductRequestBodyPublishTarget>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PublishGlobalProductRequestBody.attributeTypeMap;
    }
}

