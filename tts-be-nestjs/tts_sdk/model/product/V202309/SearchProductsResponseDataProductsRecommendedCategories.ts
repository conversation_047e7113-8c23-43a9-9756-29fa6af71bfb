/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309SearchProductsResponseDataProductsRecommendedCategories {
    /**
    * The ID of the recommended category.
    */
    'id'?: string;
    /**
    * The name of the category in the country where the shop operates.
    */
    'localName'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "localName",
            "baseName": "local_name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchProductsResponseDataProductsRecommendedCategories.attributeTypeMap;
    }
}

