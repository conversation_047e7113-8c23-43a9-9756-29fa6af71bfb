/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309PartialEditProductResponseDataAudit } from './PartialEditProductResponseDataAudit';
import { Product202309PartialEditProductResponseDataSkus } from './PartialEditProductResponseDataSkus';

export class Product202309PartialEditProductResponseData {
    'audit'?: Product202309PartialEditProductResponseDataAudit;
    /**
    * The product ID generated by TikTok Shop.
    */
    'productId'?: string;
    /**
    * A list of Stock Keeping Units (SKUs) used to identify distinct variants of the product.
    */
    'skus'?: Array<Product202309PartialEditProductResponseDataSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "audit",
            "baseName": "audit",
            "type": "Product202309PartialEditProductResponseDataAudit"
        },
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309PartialEditProductResponseDataSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Product202309PartialEditProductResponseData.attributeTypeMap;
    }
}

