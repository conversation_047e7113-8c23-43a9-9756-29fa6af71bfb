/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309SearchProductsRequestBody {
    /**
    * Filter products to show only those that are created on or after the specified date and time. Unix timestamp. Note: The \"create_time_ge\" and \"create_time_le\" together constitute the creation time filter condition. - If \"create_time_ge\" is filled but \"create_time_le\" is empty, \"create_time_le\" will default to the current time. - If \"create_time_le\" is filled but \"create_time_ge\" is empty, \"create_time_ge\" will default to the earliest shop time.
    */
    'createTimeGe'?: number;
    /**
    * Filter products to show only those that are created on or before the specified date and time. Unix timestamp. Refer to notes in \"create_time_ge\" for more usage information.
    */
    'createTimeLe'?: number;
    /**
    * Filter products by these seller SKU codes.
    */
    'sellerSkus'?: Array<string>;
    /**
    * Filter products by their status. Default: ALL Possible values:  - ALL - DRAFT - PENDING - FAILED - ACTIVATE - SELLER_DEACTIVATED - PLATFORM_DEACTIVATED - FREEZE - DELETED
    */
    'status'?: string;
    /**
    * Filter products to show only those that are updated on or after the specified date and time. Unix timestamp. Note: The fields \"update_time_ge\" and \"update_time_le\" together define the update time filter condition. - If \"update_time_ge\" is filled but \"update_time_le\" is empty, \"update_time_le\" will default to the current time. - If \"update_time_le\" is filled but \"update_time_ge\" is empty, \"update_time_ge\" will default to the earliest shop time.
    */
    'updateTimeGe'?: number;
    /**
    * Filter products to show only those that are updated on or before the specified date and time. Unix timestamp. Refer to notes in `update_time_ge` for more usage information.
    */
    'updateTimeLe'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTimeGe",
            "baseName": "create_time_ge",
            "type": "number"
        },
        {
            "name": "createTimeLe",
            "baseName": "create_time_le",
            "type": "number"
        },
        {
            "name": "sellerSkus",
            "baseName": "seller_skus",
            "type": "Array<string>"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "updateTimeGe",
            "baseName": "update_time_ge",
            "type": "number"
        },
        {
            "name": "updateTimeLe",
            "baseName": "update_time_le",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchProductsRequestBody.attributeTypeMap;
    }
}

