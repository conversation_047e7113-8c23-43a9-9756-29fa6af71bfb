/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory {
    /**
    * The name of the associated TikTok creator.
    */
    'creatorName'?: string;
    /**
    * The number of units allocated.
    */
    'quantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "creatorName",
            "baseName": "creator_name",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309InventorySearchResponseDataInventorySkusTotalAvailableInventoryDistributionCreatorInventory.attributeTypeMap;
    }
}

