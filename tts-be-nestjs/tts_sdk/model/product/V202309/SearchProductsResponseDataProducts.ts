/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Product202309SearchProductsResponseDataProductsRecommendedCategories } from './SearchProductsResponseDataProductsRecommendedCategories';
import { Product202309SearchProductsResponseDataProductsSkus } from './SearchProductsResponseDataProductsSkus';

export class Product202309SearchProductsResponseDataProducts {
    /**
    * The time when the product is created. Unix timestamp.
    */
    'createTime'?: number;
    /**
    * The product ID generated by TikTok Shop.
    */
    'id'?: string;
    /**
    * The reasons why synchronizing of global product information to local products failed.  Applicable only for cross-border sellers.
    */
    'productSyncFailReasons'?: Array<string>;
    /**
    * Recommended categories for the product based on the product title, description, and images.
    */
    'recommendedCategories'?: Array<Product202309SearchProductsResponseDataProductsRecommendedCategories>;
    /**
    * The regions where the product is sold. Possible values: - DE: Germany - ES: Spain - FR: France - GB: United Kingdom - ID: Indonesia - IE: Ireland - IT: Italy - JP: Japan - MX: Mexico - MY: Malaysia - PH: Philippines - SG: Singapore - TH: Thailand - US: United States - VN: Vietnam
    */
    'salesRegions'?: Array<string>;
    /**
    * A list of Stock Keeping Units (SKUs) used to identify distinct variants of the product.
    */
    'skus'?: Array<Product202309SearchProductsResponseDataProductsSkus>;
    /**
    * The status of the product. Possible values:  - DRAFT - PENDING - FAILED - ACTIVATE - SELLER_DEACTIVATED - PLATFORM_DEACTIVATED - FREEZE - DELETED
    */
    'status'?: string;
    /**
    * The product title.
    */
    'title'?: string;
    /**
    * The time when the product is last updated. Unix timestamp.
    */
    'updateTime'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "productSyncFailReasons",
            "baseName": "product_sync_fail_reasons",
            "type": "Array<string>"
        },
        {
            "name": "recommendedCategories",
            "baseName": "recommended_categories",
            "type": "Array<Product202309SearchProductsResponseDataProductsRecommendedCategories>"
        },
        {
            "name": "salesRegions",
            "baseName": "sales_regions",
            "type": "Array<string>"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Product202309SearchProductsResponseDataProductsSkus>"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "title",
            "baseName": "title",
            "type": "string"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309SearchProductsResponseDataProducts.attributeTypeMap;
    }
}

