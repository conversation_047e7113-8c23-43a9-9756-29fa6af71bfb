/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309UpdateInventoryRequestBodySkusInventory {
    /**
    * The updated SKU quantity. Valid range: [0, 99,999]
    */
    'quantity'?: number;
    /**
    * The ID of the warehouse where the SKU is stored. Retrieve this value from the [Get Product API](https://partner.tiktokshop.com/docv2/page/6509d85b4a0bb702c057fdda).  **Note**: Optional if there is only 1 warehouse. Otherwise, please provide this ID.
    */
    'warehouseId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        },
        {
            "name": "warehouseId",
            "baseName": "warehouse_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UpdateInventoryRequestBodySkusInventory.attributeTypeMap;
    }
}

