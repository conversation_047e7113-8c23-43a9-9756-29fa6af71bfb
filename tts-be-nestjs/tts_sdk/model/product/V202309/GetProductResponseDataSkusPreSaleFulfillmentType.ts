/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309GetProductResponseDataSkusPreSaleFulfillmentType {
    /**
    * The desired duration (in calendar days) for handling a pre-sale order and handing it over to a shipping carrier. Valid range: [3, 30]
    */
    'handlingDurationDays'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "handlingDurationDays",
            "baseName": "handling_duration_days",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309GetProductResponseDataSkusPreSaleFulfillmentType.attributeTypeMap;
    }
}

