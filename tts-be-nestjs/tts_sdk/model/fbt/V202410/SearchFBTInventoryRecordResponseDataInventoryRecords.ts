/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsGoods } from './SearchFBTInventoryRecordResponseDataInventoryRecordsGoods';
import { Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsOrder } from './SearchFBTInventoryRecordResponseDataInventoryRecordsOrder';

export class Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecords {
    /**
    * The change in the number of units is calculated as the `final_on_hand_quantity` - `initial_on_hand_quantity`.
    */
    'changedQuantity'?: number;
    /**
    * The time when the inventory record is created, Unix timestamp in seconds.
    */
    'createTime'?: number;
    /**
    * The identifier for warehouse generated by Fulfilled by TikTok  system.
    */
    'fbtWarehouseId'?: string;
    /**
    * The number of goods units physically in the Fulfilled by TikTok warehouse after current inventory change, excluding those in transit.
    */
    'finalOnHandQuantity'?: number;
    'goods'?: Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsGoods;
    /**
    * The number of goods units physically in the Fulfilled by TikTok warehouse before current inventory change, excluding those in transit.
    */
    'initialOnHandQuantity'?: number;
    /**
    * The type of goods in current inventory record. Possible values:  - NORMAL  - DEFECTIVE
    */
    'inventoryGoodsType'?: string;
    'order'?: Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsOrder;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "changedQuantity",
            "baseName": "changed_quantity",
            "type": "number"
        },
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "fbtWarehouseId",
            "baseName": "fbt_warehouse_id",
            "type": "string"
        },
        {
            "name": "finalOnHandQuantity",
            "baseName": "final_on_hand_quantity",
            "type": "number"
        },
        {
            "name": "goods",
            "baseName": "goods",
            "type": "Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsGoods"
        },
        {
            "name": "initialOnHandQuantity",
            "baseName": "initial_on_hand_quantity",
            "type": "number"
        },
        {
            "name": "inventoryGoodsType",
            "baseName": "inventory_goods_type",
            "type": "string"
        },
        {
            "name": "order",
            "baseName": "order",
            "type": "Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsOrder"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecords.attributeTypeMap;
    }
}

