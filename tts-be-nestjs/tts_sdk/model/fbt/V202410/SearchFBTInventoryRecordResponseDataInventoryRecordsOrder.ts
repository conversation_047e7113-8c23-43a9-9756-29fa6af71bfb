/**
 * tik<PERSON> shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsOrder {
    /**
    * The identifier for order generated by Fulfilled by TikTok system.
    */
    'id'?: string;
    /**
    * The type of order. Possible values: - INBOUND_ORDER - RETURN_TO_SUPPLIER_ORDER - DISPOSAL - INVENTORY_STATUS_ADJUSTMENT_ORDER - CONSIGN_ORDER - INVENTORY_COUNT_ORDER - FAILED_DELIVERY - CUSTOMER_RETURN - INVENTORY_QUANTITY_ADJUSTMENT_ORDER - OUTBOUND_TRANSFER_ORDER - FROZEN_ORDER
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecordsOrder.attributeTypeMap;
    }
}

