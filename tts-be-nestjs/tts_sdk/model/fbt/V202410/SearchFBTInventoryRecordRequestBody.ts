/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202410SearchFBTInventoryRecordRequestBody {
    /**
    * Filter inventory record creation time to show only those that are created on or after the specified date and time. Unix timestamp in seconds. `create_time_ge` is 0 by default. 
    */
    'createTimeGe'?: number;
    /**
    * Filter inventory record creation time to show only those that are created on or before the specified date and time. Unix timestamp in seconds. `create_time_le` is the current time by default. 
    */
    'createTimeLe'?: number;
    /**
    * The identifiers for warehouses generated by Fulfilled by TikTok system. If specificed, it will only show inventory records belonging to those warehouses, otherwise, all warehouses subscribed by merchant will be returned.
    */
    'fbtWarehouseIds'?: Array<string>;
    /**
    * The identifiers for goods generated by Fulfilled by TikTok system, maximum length 100. If specificed, it will only show inventory records belonging to those goods, otherwise, all goods with non-zero inventory will be returned.
    */
    'goodsIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTimeGe",
            "baseName": "create_time_ge",
            "type": "number"
        },
        {
            "name": "createTimeLe",
            "baseName": "create_time_le",
            "type": "number"
        },
        {
            "name": "fbtWarehouseIds",
            "baseName": "fbt_warehouse_ids",
            "type": "Array<string>"
        },
        {
            "name": "goodsIds",
            "baseName": "goods_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202410SearchFBTInventoryRecordRequestBody.attributeTypeMap;
    }
}

