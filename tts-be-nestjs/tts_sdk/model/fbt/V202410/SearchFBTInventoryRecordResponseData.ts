/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecords } from './SearchFBTInventoryRecordResponseDataInventoryRecords';

export class Fbt202410SearchFBTInventoryRecordResponseData {
    /**
    * A list of inventory records information.
    */
    'inventoryRecords'?: Array<Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecords>;
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The total number of the search results.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "inventoryRecords",
            "baseName": "inventory_records",
            "type": "Array<Fbt202410SearchFBTInventoryRecordResponseDataInventoryRecords>"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202410SearchFBTInventoryRecordResponseData.attributeTypeMap;
    }
}

