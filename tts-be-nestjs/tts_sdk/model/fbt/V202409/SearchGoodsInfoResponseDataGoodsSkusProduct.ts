/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409SearchGoodsInfoResponseDataGoodsSkusProduct {
    /**
    * The identifier for product generated by TikTok Shop system.
    */
    'id'?: string;
    /**
    * The Tiktok Shop product image url.
    */
    'imageUrl'?: string;
    /**
    * The TikTok Shop product name.
    */
    'name'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "imageUrl",
            "baseName": "image_url",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsSkusProduct.attributeTypeMap;
    }
}

