/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension } from './SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension';
import { Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoWeight } from './SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoWeight';

export class Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo {
    'dimension'?: Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension;
    'weight'?: Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo.attributeTypeMap;
    }
}

