/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409SearchGoodsInfoResponseDataGoodsBarcodes {
    /**
    * The printable barcode for the goods.
    */
    'code'?: string;
    /**
    * The barcode type. Possible values:  - UPC  - EAN  - GTIN - ASIN OR FNSKU  - MERCHANT SKU ID  - FBT SPECIFIC BARCODE
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsBarcodes.attributeTypeMap;
    }
}

