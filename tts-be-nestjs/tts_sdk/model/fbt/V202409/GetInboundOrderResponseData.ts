/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409GetInboundOrderResponseDataInboundOrders } from './GetInboundOrderResponseDataInboundOrders';

export class Fbt202409GetInboundOrderResponseData {
    /**
    * A list of inbound order detail information.
    */
    'inboundOrders'?: Array<Fbt202409GetInboundOrderResponseDataInboundOrders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "inboundOrders",
            "baseName": "inbound_orders",
            "type": "Array<Fbt202409GetInboundOrderResponseDataInboundOrders>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseData.attributeTypeMap;
    }
}

