/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions {
    /**
    * The country or region codes follow the two-character ISO 3166-1 alpha-2 format.
    */
    'regionCode'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions.attributeTypeMap;
    }
}

