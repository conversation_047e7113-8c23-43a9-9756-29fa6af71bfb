/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409SearchGoodsInfoResponseDataGoodsSkusProduct } from './SearchGoodsInfoResponseDataGoodsSkusProduct';

export class Fbt202409SearchGoodsInfoResponseDataGoodsSkus {
    /**
    * The identifier for SKU generated by TikTok Shop system.
    */
    'id'?: string;
    /**
    * The TikTok Shop SKU image url.
    */
    'imageUrl'?: string;
    /**
    * A flag indicating whether the Fulfilled by TikTok goods is matched with the TikTok Shop SKU. True: Matched. False: Not matched.
    */
    'matched'?: boolean;
    /**
    * The SKU name.
    */
    'name'?: string;
    'product'?: Fbt202409SearchGoodsInfoResponseDataGoodsSkusProduct;
    /**
    * The regions where the SKU is being sold, represented by region codes in the two-character ISO 3166-1 alpha-2 format.
    */
    'regions'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "imageUrl",
            "baseName": "image_url",
            "type": "string"
        },
        {
            "name": "matched",
            "baseName": "matched",
            "type": "boolean"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "product",
            "baseName": "product",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsSkusProduct"
        },
        {
            "name": "regions",
            "baseName": "regions",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsSkus.attributeTypeMap;
    }
}

