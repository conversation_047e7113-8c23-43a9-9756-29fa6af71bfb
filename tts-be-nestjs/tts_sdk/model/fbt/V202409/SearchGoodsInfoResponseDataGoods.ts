/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409SearchGoodsInfoResponseDataGoodsBarcodes } from './SearchGoodsInfoResponseDataGoodsBarcodes';
import { Fbt202409SearchGoodsInfoResponseDataGoodsLotExpirationInfo } from './SearchGoodsInfoResponseDataGoodsLotExpirationInfo';
import { Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo } from './SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo';
import { Fbt202409SearchGoodsInfoResponseDataGoodsSkus } from './SearchGoodsInfoResponseDataGoodsSkus';
import { Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo } from './SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo';

export class Fbt202409SearchGoodsInfoResponseDataGoods {
    /**
    * A list of detailed information for goods barcodes.
    */
    'barcodes'?: Array<Fbt202409SearchGoodsInfoResponseDataGoodsBarcodes>;
    /**
    * The identifier for goods generated by Fulfilled by TikTok system.
    */
    'id'?: string;
    /**
    * The goods image url.
    */
    'imageUrl'?: string;
    'lotExpirationInfo'?: Fbt202409SearchGoodsInfoResponseDataGoodsLotExpirationInfo;
    'merchantDeclarationInfo'?: Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo;
    /**
    * The goods name.
    */
    'name'?: string;
    /**
    * The identifier for goods defined by merchant.
    */
    'referenceCode'?: string;
    /**
    * A list of Stock Keeping Units (SKUs) infomation matched with current FBT goods.
    */
    'skus'?: Array<Fbt202409SearchGoodsInfoResponseDataGoodsSkus>;
    'warehouseConfirmationInfo'?: Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "barcodes",
            "baseName": "barcodes",
            "type": "Array<Fbt202409SearchGoodsInfoResponseDataGoodsBarcodes>"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "imageUrl",
            "baseName": "image_url",
            "type": "string"
        },
        {
            "name": "lotExpirationInfo",
            "baseName": "lot_expiration_info",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsLotExpirationInfo"
        },
        {
            "name": "merchantDeclarationInfo",
            "baseName": "merchant_declaration_info",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "referenceCode",
            "baseName": "reference_code",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Fbt202409SearchGoodsInfoResponseDataGoodsSkus>"
        },
        {
            "name": "warehouseConfirmationInfo",
            "baseName": "warehouse_confirmation_info",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfo"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoods.attributeTypeMap;
    }
}

