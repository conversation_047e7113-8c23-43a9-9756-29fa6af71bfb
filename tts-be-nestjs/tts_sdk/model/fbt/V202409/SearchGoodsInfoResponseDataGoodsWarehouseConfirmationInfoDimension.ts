/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension {
    /**
    * Height value verified by warehouse, up to three decimal places.
    */
    'height'?: string;
    /**
    * Length value verified by warehouse, up to three decimal places.
    */
    'length'?: string;
    /**
    * Dimension unit.  Possible values: - MILLIMETER  - CENTIMETER - METER  - FOOT  - INCH - MICRO
    */
    'unit'?: string;
    /**
    * Width value verified by warehouse, up to three decimal places.
    */
    'width'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "string"
        },
        {
            "name": "length",
            "baseName": "length",
            "type": "string"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsWarehouseConfirmationInfoDimension.attributeTypeMap;
    }
}

