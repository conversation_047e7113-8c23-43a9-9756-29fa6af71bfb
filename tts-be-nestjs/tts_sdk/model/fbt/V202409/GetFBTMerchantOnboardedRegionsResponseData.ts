/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions } from './GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions';

export class Fbt202409GetFBTMerchantOnboardedRegionsResponseData {
    /**
    * A list of countries or regions where the merchant is onboarded and can conduct business in the Fulfilled by TikTok system. If seller is not onboarded, `data` will be empty.
    */
    'onboardedRegions'?: Array<Fbt202409GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "onboardedRegions",
            "baseName": "onboarded_regions",
            "type": "Array<Fbt202409GetFBTMerchantOnboardedRegionsResponseDataOnboardedRegions>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetFBTMerchantOnboardedRegionsResponseData.attributeTypeMap;
    }
}

