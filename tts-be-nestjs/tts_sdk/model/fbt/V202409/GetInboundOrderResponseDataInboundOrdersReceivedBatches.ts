/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetInboundOrderResponseDataInboundOrdersReceivedBatches {
    /**
    * The quantity of defective goods in the inbound order batch.
    */
    'defectiveQuantity'?: number;
    /**
    * The identifier for goods generated by Fulfilled by TikTok system.
    */
    'goodsId'?: string;
    /**
    * The identifier for inbound put away batch.
    */
    'id'?: string;
    /**
    * The quantity of normal goods in the inbound order batch.
    */
    'normalQuantity'?: number;
    /**
    * A list of product identifiers generated by TikTok Shop, which will be empty if the current goods do not match any product.
    */
    'productIds'?: Array<string>;
    /**
    * A list of SKU (Stock Keeping Unit) identifiers generated by TikTok Shop, which will be empty if the current goods do not match any SKU.
    */
    'skuIds'?: Array<string>;
    /**
    * The total number of units received by the Fulfilled by TikTok warehouse in the inbound order batch, which is the sum of `normal_quantity` and `defective_quantity`.
    */
    'totalQuantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "defectiveQuantity",
            "baseName": "defective_quantity",
            "type": "number"
        },
        {
            "name": "goodsId",
            "baseName": "goods_id",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "normalQuantity",
            "baseName": "normal_quantity",
            "type": "number"
        },
        {
            "name": "productIds",
            "baseName": "product_ids",
            "type": "Array<string>"
        },
        {
            "name": "skuIds",
            "baseName": "sku_ids",
            "type": "Array<string>"
        },
        {
            "name": "totalQuantity",
            "baseName": "total_quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrdersReceivedBatches.attributeTypeMap;
    }
}

