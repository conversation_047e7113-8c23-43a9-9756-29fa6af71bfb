/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409SearchGoodsInfoResponseDataGoods } from './SearchGoodsInfoResponseDataGoods';

export class Fbt202409SearchGoodsInfoResponseData {
    /**
    * A list of detailed information for Fulfilled by TikTok goods.
    */
    'goods'?: Array<Fbt202409SearchGoodsInfoResponseDataGoods>;
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the page_token parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The total number of goods that meet the query conditions.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "goods",
            "baseName": "goods",
            "type": "Array<Fbt202409SearchGoodsInfoResponseDataGoods>"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseData.attributeTypeMap;
    }
}

