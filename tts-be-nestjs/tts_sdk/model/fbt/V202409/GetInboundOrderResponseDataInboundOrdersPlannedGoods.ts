/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetInboundOrderResponseDataInboundOrdersPlannedGoods {
    /**
    * The identifier for goods generated by Fulfilled by TikTok system.
    */
    'id'?: string;
    /**
    * The Fulfilled by TikTok goods name.
    */
    'name'?: string;
    /**
    * The planned goods quantity.
    */
    'quantity'?: number;
    /**
    * The identifier for goods defined by merchant.
    */
    'referenceCode'?: string;
    /**
    * The identifier for Stock Keeping Unit (SKU) generated by Fulfilled by TikTok system.
    */
    'skuIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        },
        {
            "name": "referenceCode",
            "baseName": "reference_code",
            "type": "string"
        },
        {
            "name": "skuIds",
            "baseName": "sku_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrdersPlannedGoods.attributeTypeMap;
    }
}

