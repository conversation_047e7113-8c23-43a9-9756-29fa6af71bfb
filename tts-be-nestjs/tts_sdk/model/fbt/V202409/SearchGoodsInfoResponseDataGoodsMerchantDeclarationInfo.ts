/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoDimension } from './SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoDimension';
import { Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoWeight } from './SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoWeight';

export class Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo {
    'dimension'?: Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoDimension;
    'weight'?: Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoDimension"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfoWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoResponseDataGoodsMerchantDeclarationInfo.attributeTypeMap;
    }
}

