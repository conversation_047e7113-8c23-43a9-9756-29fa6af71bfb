/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersCarriers } from './GetInboundOrderResponseDataInboundOrdersCarriers';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersMerchant } from './GetInboundOrderResponseDataInboundOrdersMerchant';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersOrderOperationLogs } from './GetInboundOrderResponseDataInboundOrdersOrderOperationLogs';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersPlannedGoods } from './GetInboundOrderResponseDataInboundOrdersPlannedGoods';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersReceivedBatches } from './GetInboundOrderResponseDataInboundOrdersReceivedBatches';
import { Fbt202409GetInboundOrderResponseDataInboundOrdersWarehouse } from './GetInboundOrderResponseDataInboundOrdersWarehouse';

export class Fbt202409GetInboundOrderResponseDataInboundOrders {
    /**
    * The actual arrival time. Unix timestamp in seconds.
    */
    'actualArriveTime'?: number;
    /**
    * A list of carrier information.
    */
    'carriers'?: Array<Fbt202409GetInboundOrderResponseDataInboundOrdersCarriers>;
    /**
    * The time when the inbound order created. Unix timestamp in seconds.
    */
    'createTime'?: number;
    /**
    * The expected arrival time provided by merchant. Unix timestamp in seconds.
    */
    'expectArriveTime'?: number;
    /**
    * The identifier for inbound order generated in Fillfilled by TikTok system.
    */
    'id'?: string;
    'merchant'?: Fbt202409GetInboundOrderResponseDataInboundOrdersMerchant;
    /**
    * A list of inbound order operation history.
    */
    'orderOperationLogs'?: Array<Fbt202409GetInboundOrderResponseDataInboundOrdersOrderOperationLogs>;
    /**
    * A list of planned goods details in the inbound order.
    */
    'plannedGoods'?: Array<Fbt202409GetInboundOrderResponseDataInboundOrdersPlannedGoods>;
    /**
    * A list of actual received inbound order batch details.
    */
    'receivedBatches'?: Array<Fbt202409GetInboundOrderResponseDataInboundOrdersReceivedBatches>;
    /**
    * The time when the inbound order shipped. Unix timestamp in seconds.
    */
    'shipTime'?: number;
    /**
    * The inbound order type. Possible values: - REPLENISHMENT
    */
    'type'?: string;
    'warehouse'?: Fbt202409GetInboundOrderResponseDataInboundOrdersWarehouse;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "actualArriveTime",
            "baseName": "actual_arrive_time",
            "type": "number"
        },
        {
            "name": "carriers",
            "baseName": "carriers",
            "type": "Array<Fbt202409GetInboundOrderResponseDataInboundOrdersCarriers>"
        },
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "expectArriveTime",
            "baseName": "expect_arrive_time",
            "type": "number"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "merchant",
            "baseName": "merchant",
            "type": "Fbt202409GetInboundOrderResponseDataInboundOrdersMerchant"
        },
        {
            "name": "orderOperationLogs",
            "baseName": "order_operation_logs",
            "type": "Array<Fbt202409GetInboundOrderResponseDataInboundOrdersOrderOperationLogs>"
        },
        {
            "name": "plannedGoods",
            "baseName": "planned_goods",
            "type": "Array<Fbt202409GetInboundOrderResponseDataInboundOrdersPlannedGoods>"
        },
        {
            "name": "receivedBatches",
            "baseName": "received_batches",
            "type": "Array<Fbt202409GetInboundOrderResponseDataInboundOrdersReceivedBatches>"
        },
        {
            "name": "shipTime",
            "baseName": "ship_time",
            "type": "number"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        },
        {
            "name": "warehouse",
            "baseName": "warehouse",
            "type": "Fbt202409GetInboundOrderResponseDataInboundOrdersWarehouse"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrders.attributeTypeMap;
    }
}

