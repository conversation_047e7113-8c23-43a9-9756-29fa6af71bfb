/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409SearchGoodsInfoRequestBody {
    /**
    * Identifier for goods generated by Fulfilled by TikTok system, maximum length 100. Filter the goods information to display only those with specific goods IDs. Note: When `goods_ids`, `reference_codes`, `sku_ids`, or `product_ids` include two or more values, the result will return the intersection of the goods information. For example, if the input includes both `goods_ids` and `sku_ids`, it will return only the goods that meet both the `goods_ids` and `sku_ids` criteria. If requested with all criteria empty, it will return a `page_size` number of goods defined by the merchant, sorted by creation time, with the latest first.
    */
    'goodsIds'?: Array<string>;
    /**
    * Identifier for product generated by TikTok Shop system, maximum length 100. Filter the goods information to display only those with specific product IDs.
    */
    'productIds'?: Array<string>;
    /**
    * Identifier for goods defined by merchant, maximum length 100. Filter the goods information to display only those with specific reference codes.
    */
    'referenceCodes'?: Array<string>;
    /**
    * Identifier for Stock Keeping Unit (SKU) generated by TikTok Shop system, maximum length 100. Filter the goods information to display only those with specific SKU IDs.
    */
    'skuIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "goodsIds",
            "baseName": "goods_ids",
            "type": "Array<string>"
        },
        {
            "name": "productIds",
            "baseName": "product_ids",
            "type": "Array<string>"
        },
        {
            "name": "referenceCodes",
            "baseName": "reference_codes",
            "type": "Array<string>"
        },
        {
            "name": "skuIds",
            "baseName": "sku_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409SearchGoodsInfoRequestBody.attributeTypeMap;
    }
}

