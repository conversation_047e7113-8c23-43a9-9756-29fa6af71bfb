/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetInboundOrderResponseDataInboundOrdersWarehouse {
    /**
    * The Identifier for warehouse generated by Fulfilled by TikTok system.
    */
    'fbtWarehouseId'?: string;
    /**
    * The Fulfilled by TikTok warehouse name.
    */
    'name'?: string;
    /**
    * The Fullfilled by TikTok  warehouse type.   Possible values: - PLATFORM_WAREHOUSE
    */
    'type'?: string;
    /**
    * The identifier for warehouse generated by TikTok Shop system, will have the relationship with `fbt_warehouse_id`.
    */
    'warehouseIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "fbtWarehouseId",
            "baseName": "fbt_warehouse_id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        },
        {
            "name": "warehouseIds",
            "baseName": "warehouse_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrdersWarehouse.attributeTypeMap;
    }
}

