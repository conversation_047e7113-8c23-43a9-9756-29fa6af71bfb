/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetInboundOrderResponseDataInboundOrdersOrderOperationLogs {
    /**
    * The time when the inbound order is processed by the merchant or warehouses, represented as a Unix timestamp in seconds.
    */
    'operateTime'?: number;
    /**
    * The order status in Fulfilled by TikTok warehouse. Possible values: - WORKING - READY_TO_SHIP - TO_BE_RECEIVED - ARRIVED_HUB - INTERNAL_TRANSFER - RECEIVING - PARTIALLY_RECEIVED - RECEIVED - CANCELLED - DELIVERED
    */
    'orderStatus'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "operateTime",
            "baseName": "operate_time",
            "type": "number"
        },
        {
            "name": "orderStatus",
            "baseName": "order_status",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrdersOrderOperationLogs.attributeTypeMap;
    }
}

