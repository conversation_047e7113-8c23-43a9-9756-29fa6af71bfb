/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202409GetInboundOrderResponseDataInboundOrdersCarriers {
    /**
    * The carrier name.
    */
    'carrierName'?: string;
    /**
    * The tracking number for the current inbound shipment with the carrier.
    */
    'trackingNumber'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "carrierName",
            "baseName": "carrier_name",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202409GetInboundOrderResponseDataInboundOrdersCarriers.attributeTypeMap;
    }
}

