/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202408GetFBTWarehouseListResponseDataWarehousesAddresses } from './GetFBTWarehouseListResponseDataWarehousesAddresses';
import { Fbt202408GetFBTWarehouseListResponseDataWarehousesLogisticsServices } from './GetFBTWarehouseListResponseDataWarehousesLogisticsServices';

export class Fbt202408GetFBTWarehouseListResponseDataWarehouses {
    /**
    * The Fulfilled by TikTok warehouse address information.
    */
    'addresses'?: Array<Fbt202408GetFBTWarehouseListResponseDataWarehousesAddresses>;
    /**
    * The identifier for warehouse generated by Fulfilled By TikTok system.
    */
    'fbtWarehouseId'?: string;
    /**
    * A list of logistics service information related to the current warehouse.
    */
    'logisticsServices'?: Array<Fbt202408GetFBTWarehouseListResponseDataWarehousesLogisticsServices>;
    /**
    * The Fulfilled by TikTok warehouse name, e.g.TikTok_Eastvale(CA).
    */
    'name'?: string;
    /**
    * A flag indicating whether the warehouse is subscribed. True: Subscribed. False: Not subscribed.
    */
    'subscribed'?: boolean;
    /**
    * The Fulfilled by TikTok warehouse type.   Possible values: - PLATFORM_WAREHOUSE
    */
    'type'?: string;
    /**
    * The identifier for warehouse generated by TikTok Shop system, will have the relationship with `fbt_warehouse_id`.
    */
    'warehouseIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addresses",
            "baseName": "addresses",
            "type": "Array<Fbt202408GetFBTWarehouseListResponseDataWarehousesAddresses>"
        },
        {
            "name": "fbtWarehouseId",
            "baseName": "fbt_warehouse_id",
            "type": "string"
        },
        {
            "name": "logisticsServices",
            "baseName": "logistics_services",
            "type": "Array<Fbt202408GetFBTWarehouseListResponseDataWarehousesLogisticsServices>"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "subscribed",
            "baseName": "subscribed",
            "type": "boolean"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        },
        {
            "name": "warehouseIds",
            "baseName": "warehouse_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408GetFBTWarehouseListResponseDataWarehouses.attributeTypeMap;
    }
}

