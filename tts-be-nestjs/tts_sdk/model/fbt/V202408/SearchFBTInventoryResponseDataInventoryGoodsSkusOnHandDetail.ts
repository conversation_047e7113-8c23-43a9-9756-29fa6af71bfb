/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail {
    /**
    * The number of units available for sale.  Note:  The number does not include reserved units.
    */
    'availableQuantity'?: number;
    /**
    * The number of units reserved for customer orders or return to supplier shipment.
    */
    'reservedQuantity'?: number;
    /**
    * The total number of units for current SKU, consist of `available_quantity` and `reserved_quantity`.
    */
    'totalQuantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "availableQuantity",
            "baseName": "available_quantity",
            "type": "number"
        },
        {
            "name": "reservedQuantity",
            "baseName": "reserved_quantity",
            "type": "number"
        },
        {
            "name": "totalQuantity",
            "baseName": "total_quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail.attributeTypeMap;
    }
}

