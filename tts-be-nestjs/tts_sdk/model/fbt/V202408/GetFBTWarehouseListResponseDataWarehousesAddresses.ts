/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202408GetFBTWarehouseListResponseDataWarehousesAddresses {
    /**
    * The detail address line 1.
    */
    'addressLine1'?: string;
    /**
    * The detail address line 2, additional address information(optional).
    */
    'addressLine2'?: string;
    /**
    * The detail address line 3, additional address information(optional).
    */
    'addressLine3'?: string;
    /**
    * Warehouse address city.
    */
    'city'?: string;
    /**
    * Warehouse address district or county.
    */
    'district'?: string;
    /**
    * Warehouse address postal code (also known as zip code).
    */
    'postalCode'?: string;
    /**
    * Warehouse country or region code in two-character ISO 3166-1 alpha-2 format.
    */
    'regionCode'?: string;
    /**
    * Warehouse address state or province.
    */
    'state'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressLine1",
            "baseName": "address_line_1",
            "type": "string"
        },
        {
            "name": "addressLine2",
            "baseName": "address_line_2",
            "type": "string"
        },
        {
            "name": "addressLine3",
            "baseName": "address_line_3",
            "type": "string"
        },
        {
            "name": "city",
            "baseName": "city",
            "type": "string"
        },
        {
            "name": "district",
            "baseName": "district",
            "type": "string"
        },
        {
            "name": "postalCode",
            "baseName": "postal_code",
            "type": "string"
        },
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        },
        {
            "name": "state",
            "baseName": "state",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408GetFBTWarehouseListResponseDataWarehousesAddresses.attributeTypeMap;
    }
}

