/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202408GetFBTWarehouseListResponseDataWarehousesLogisticsServices {
    /**
    * The identifier for logistics service in TikTok Shop system.
    */
    'id'?: string;
    /**
    * The logistics service name.
    */
    'name'?: string;
    /**
    * A flag indicating whether the logistic service is subscribed. True: Subscribed. False: Not subscribed.
    */
    'subscribed'?: boolean;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "subscribed",
            "baseName": "subscribed",
            "type": "boolean"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408GetFBTWarehouseListResponseDataWarehousesLogisticsServices.attributeTypeMap;
    }
}

