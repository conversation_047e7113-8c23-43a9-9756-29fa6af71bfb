/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkus } from './SearchFBTInventoryResponseDataInventoryGoodsSkus';

export class Fbt202408SearchFBTInventoryResponseDataInventoryGoods {
    /**
    * The identifier for goods generated by Fulfilled by TikTok system.
    */
    'id'?: string;
    /**
    * The goods name.
    */
    'name'?: string;
    /**
    * The identifier for goods defined by the merchant, commonly used to reference the ID from the merchant\'s other order management systems.
    */
    'referenceCode'?: string;
    /**
    * A list of inventory information for Stock Keeping Units (SKUs) matched with current Fulfilled by TikTok goods.
    */
    'skus'?: Array<Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "referenceCode",
            "baseName": "reference_code",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryResponseDataInventoryGoods.attributeTypeMap;
    }
}

