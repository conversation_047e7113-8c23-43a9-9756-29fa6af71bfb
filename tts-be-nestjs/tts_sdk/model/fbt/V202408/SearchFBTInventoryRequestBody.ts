/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202408SearchFBTInventoryRequestBody {
    /**
    * The identifiers for warehouses generated by Fulfilled by TikTok system. If specificed, it will only show inventory records belonging to those warehouses, otherwise, all warehouses subscribed by merchant will be returned.
    */
    'fbtWarehouseIds'?: Array<string>;
    /**
    * The identifiers for goods generated by Fulfilled by TikTok system, maximum length 100. If specificed, it will only show inventory information belonging to those goods, otherwise, all goods with non-zero inventory will be returned. Note:  This API only allows either `goods_ids` or `sku_ids`, and returns results that satisfy all specified criteria.
    */
    'goodsIds'?: Array<string>;
    /**
    * A list of identifiers for Stock Keeping Units(SKUs) generated by TikTok Shop, maximum length 100. Filter inventory information to display only those belonging to the goods matched with specified SKUs.  Refer to note in `goods_ids` for more usage information.
    */
    'skuIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "fbtWarehouseIds",
            "baseName": "fbt_warehouse_ids",
            "type": "Array<string>"
        },
        {
            "name": "goodsIds",
            "baseName": "goods_ids",
            "type": "Array<string>"
        },
        {
            "name": "skuIds",
            "baseName": "sku_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryRequestBody.attributeTypeMap;
    }
}

