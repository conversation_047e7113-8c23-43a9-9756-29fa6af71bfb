/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail } from './SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail';

export class Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkus {
    /**
    * The identifier for SKU generated by TikTok Shop system.
    */
    'id'?: string;
    'onHandDetail'?: Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "onHandDetail",
            "baseName": "on_hand_detail",
            "type": "Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkusOnHandDetail"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryResponseDataInventoryGoodsSkus.attributeTypeMap;
    }
}

