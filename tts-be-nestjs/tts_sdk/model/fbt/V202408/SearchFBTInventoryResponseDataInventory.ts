/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202408SearchFBTInventoryResponseDataInventoryGoods } from './SearchFBTInventoryResponseDataInventoryGoods';
import { Fbt202408SearchFBTInventoryResponseDataInventoryOnHandDetail } from './SearchFBTInventoryResponseDataInventoryOnHandDetail';

export class Fbt202408SearchFBTInventoryResponseDataInventory {
    /**
    * The identifier for warehouse generated by Fulfilled by TikTok system.
    */
    'fbtWarehouseId'?: string;
    'goods'?: Fbt202408SearchFBTInventoryResponseDataInventoryGoods;
    /**
    * The number of goods units currently en route to the warehouse. The inventory includes inbound replenishments, customer returns, unsuccessful delivery returns, etc.
    */
    'inTransitQuantity'?: number;
    'onHandDetail'?: Fbt202408SearchFBTInventoryResponseDataInventoryOnHandDetail;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "fbtWarehouseId",
            "baseName": "fbt_warehouse_id",
            "type": "string"
        },
        {
            "name": "goods",
            "baseName": "goods",
            "type": "Fbt202408SearchFBTInventoryResponseDataInventoryGoods"
        },
        {
            "name": "inTransitQuantity",
            "baseName": "in_transit_quantity",
            "type": "number"
        },
        {
            "name": "onHandDetail",
            "baseName": "on_hand_detail",
            "type": "Fbt202408SearchFBTInventoryResponseDataInventoryOnHandDetail"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryResponseDataInventory.attributeTypeMap;
    }
}

