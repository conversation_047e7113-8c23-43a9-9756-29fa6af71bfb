/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fbt202408SearchFBTInventoryResponseDataInventoryOnHandDetail {
    /**
    * The number of units available for sale.  Note:  The number does not include reserved units.
    */
    'availableQuantity'?: number;
    /**
    * The number of units reserved for customer orders or return to supplier shipment.
    */
    'reservedQuantity'?: number;
    /**
    * The total number of units physically in the Fulfilled by TikTok warehouse, excluding those in transit. This total is the sum of `available_quantity`, `reserved_quantity`, and `unfulfillable_quantity`.
    */
    'totalQuantity'?: number;
    /**
    * The number of units that are currently unavailable for sale.
    */
    'unfulfillableQuantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "availableQuantity",
            "baseName": "available_quantity",
            "type": "number"
        },
        {
            "name": "reservedQuantity",
            "baseName": "reserved_quantity",
            "type": "number"
        },
        {
            "name": "totalQuantity",
            "baseName": "total_quantity",
            "type": "number"
        },
        {
            "name": "unfulfillableQuantity",
            "baseName": "unfulfillable_quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408SearchFBTInventoryResponseDataInventoryOnHandDetail.attributeTypeMap;
    }
}

