/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fbt202408GetFBTWarehouseListResponseDataWarehouses } from './GetFBTWarehouseListResponseDataWarehouses';

export class Fbt202408GetFBTWarehouseListResponseData {
    /**
    * A list of Fulfilled by TikTok warehouses information.
    */
    'warehouses'?: Array<Fbt202408GetFBTWarehouseListResponseDataWarehouses>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "warehouses",
            "baseName": "warehouses",
            "type": "Array<Fbt202408GetFBTWarehouseListResponseDataWarehouses>"
        }    ];

    static getAttributeTypeMap() {
        return Fbt202408GetFBTWarehouseListResponseData.attributeTypeMap;
    }
}

