/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { DataReconciliation202401QualityFactoryOrderDataImportAPIResponseDataErrorsDetail } from './QualityFactoryOrderDataImportAPIResponseDataErrorsDetail';

export class DataReconciliation202401QualityFactoryOrderDataImportAPIResponseDataErrors {
    /**
    * Integartion err code 
    */
    'code'?: string;
    'detail'?: DataReconciliation202401QualityFactoryOrderDataImportAPIResponseDataErrorsDetail;
    /**
    * Integartion err message 
    */
    'message'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "string"
        },
        {
            "name": "detail",
            "baseName": "detail",
            "type": "DataReconciliation202401QualityFactoryOrderDataImportAPIResponseDataErrorsDetail"
        },
        {
            "name": "message",
            "baseName": "message",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return DataReconciliation202401QualityFactoryOrderDataImportAPIResponseDataErrors.attributeTypeMap;
    }
}

