/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { DataReconciliation202309OrderStatusDataExchangeRequestBodyOrders } from './OrderStatusDataExchangeRequestBodyOrders';

export class DataReconciliation202309OrderStatusDataExchangeRequestBody {
    /**
    * The exchange order list
    */
    'orders'?: Array<DataReconciliation202309OrderStatusDataExchangeRequestBodyOrders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<DataReconciliation202309OrderStatusDataExchangeRequestBodyOrders>"
        }    ];

    static getAttributeTypeMap() {
        return DataReconciliation202309OrderStatusDataExchangeRequestBody.attributeTypeMap;
    }
}

