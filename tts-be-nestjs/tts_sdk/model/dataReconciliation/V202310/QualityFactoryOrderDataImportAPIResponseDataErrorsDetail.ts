/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { DataReconciliation202310QualityFactoryOrderDataImportAPIResponseDataErrorsDetailExtraErrors } from './QualityFactoryOrderDataImportAPIResponseDataErrorsDetailExtraErrors';

export class DataReconciliation202310QualityFactoryOrderDataImportAPIResponseDataErrorsDetail {
    /**
    * Failed channel order id 
    */
    'channelOrderId'?: string;
    /**
    * Failed channel type 
    */
    'channelType'?: string;
    /**
    * Failed order reasons
    */
    'extraErrors'?: Array<DataReconciliation202310QualityFactoryOrderDataImportAPIResponseDataErrorsDetailExtraErrors>;
    /**
    * Failed Tiktok Shop order id 
    */
    'orderId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "channelOrderId",
            "baseName": "channel_order_id",
            "type": "string"
        },
        {
            "name": "channelType",
            "baseName": "channel_type",
            "type": "string"
        },
        {
            "name": "extraErrors",
            "baseName": "extra_errors",
            "type": "Array<DataReconciliation202310QualityFactoryOrderDataImportAPIResponseDataErrorsDetailExtraErrors>"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return DataReconciliation202310QualityFactoryOrderDataImportAPIResponseDataErrorsDetail.attributeTypeMap;
    }
}

