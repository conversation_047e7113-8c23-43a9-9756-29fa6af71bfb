/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class DataReconciliation202310QualityFactoryOrderDataImportAPIRequestBodyOrdersPackages {
    /**
    * The tracking corresponding Tiktok shop package id 
    */
    'packageId'?: string;
    /**
    * The provider name of tracking info
    */
    'shippingProviderName'?: string;
    /**
    * Tracking number of tracking info 
    */
    'trackingNumber'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return DataReconciliation202310QualityFactoryOrderDataImportAPIRequestBodyOrdersPackages.attributeTypeMap;
    }
}

