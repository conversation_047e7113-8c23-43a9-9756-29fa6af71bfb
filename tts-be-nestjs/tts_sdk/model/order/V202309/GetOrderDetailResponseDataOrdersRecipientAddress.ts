/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderDetailResponseDataOrdersRecipientAddressDeliveryPreferences } from './GetOrderDetailResponseDataOrdersRecipientAddressDeliveryPreferences';
import { Order202309GetOrderDetailResponseDataOrdersRecipientAddressDistrictInfo } from './GetOrderDetailResponseDataOrdersRecipientAddressDistrictInfo';

export class Order202309GetOrderDetailResponseDataOrdersRecipientAddress {
    /**
    * Full buyer detail address.
    */
    'addressDetail'?: string;
    /**
    * The first line of the street address.
    */
    'addressLine1'?: string;
    /**
    * The second line of the street addresss
    */
    'addressLine2'?: string;
    /**
    * The third line of the street address.  Applicable only for the BR market.
    */
    'addressLine3'?: string;
    /**
    * The fourth line of the street address. Applicable only for the BR market.
    */
    'addressLine4'?: string;
    'deliveryPreferences'?: Order202309GetOrderDetailResponseDataOrdersRecipientAddressDeliveryPreferences;
    /**
    * District information list.
    */
    'districtInfo'?: Array<Order202309GetOrderDetailResponseDataOrdersRecipientAddressDistrictInfo>;
    /**
    * The first name of the recipient. If the buyer does not provide their first and last name separately, this parameter will have the same value as the \"name\" parameter. Note: Only available in US and UK markets.
    */
    'firstName'?: string;
    /**
    * Recipient first name in katakana. **Note**: Applicable only for the JP market.
    */
    'firstNameLocalScript'?: string;
    /**
    * The complete recipient addresses information.
    */
    'fullAddress'?: string;
    /**
    * The last name of the recipient. If the buyer does not provide their first and last name separately, this parameter will be empty. Note: Only available in US and UK markets.
    */
    'lastName'?: string;
    /**
    * Recipient last name in katakana. **Note**: Applicable only for the JP market.
    */
    'lastNameLocalScript'?: string;
    /**
    * The name of the recipient. Please note, if this order uses platform logistics, recipient name will be desensitized
    */
    'name'?: string;
    /**
    * The telephone number of the buyer. Please notice, if this order uses platform logistics, phone number will be desensitized.
    */
    'phoneNumber'?: string;
    /**
    * The postal code that can be used by seller for shipping (in the U.S, this is the ZIP code).
    */
    'postalCode'?: string;
    /**
    * Region code.
    */
    'regionCode'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressDetail",
            "baseName": "address_detail",
            "type": "string"
        },
        {
            "name": "addressLine1",
            "baseName": "address_line1",
            "type": "string"
        },
        {
            "name": "addressLine2",
            "baseName": "address_line2",
            "type": "string"
        },
        {
            "name": "addressLine3",
            "baseName": "address_line3",
            "type": "string"
        },
        {
            "name": "addressLine4",
            "baseName": "address_line4",
            "type": "string"
        },
        {
            "name": "deliveryPreferences",
            "baseName": "delivery_preferences",
            "type": "Order202309GetOrderDetailResponseDataOrdersRecipientAddressDeliveryPreferences"
        },
        {
            "name": "districtInfo",
            "baseName": "district_info",
            "type": "Array<Order202309GetOrderDetailResponseDataOrdersRecipientAddressDistrictInfo>"
        },
        {
            "name": "firstName",
            "baseName": "first_name",
            "type": "string"
        },
        {
            "name": "firstNameLocalScript",
            "baseName": "first_name_local_script",
            "type": "string"
        },
        {
            "name": "fullAddress",
            "baseName": "full_address",
            "type": "string"
        },
        {
            "name": "lastName",
            "baseName": "last_name",
            "type": "string"
        },
        {
            "name": "lastNameLocalScript",
            "baseName": "last_name_local_script",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "phoneNumber",
            "baseName": "phone_number",
            "type": "string"
        },
        {
            "name": "postalCode",
            "baseName": "postal_code",
            "type": "string"
        },
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderDetailResponseDataOrdersRecipientAddress.attributeTypeMap;
    }
}

