/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderDetailResponseDataOrdersLineItemsCombinedListingSkus } from './GetOrderDetailResponseDataOrdersLineItemsCombinedListingSkus';
import { Order202309GetOrderDetailResponseDataOrdersLineItemsItemTax } from './GetOrderDetailResponseDataOrdersLineItemsItemTax';

export class Order202309GetOrderDetailResponseDataOrdersLineItems {
    /**
    * A service fee is charged on every transaction made. The charge is applied from the fifth order onwards and collected directly from customers during checkout. Only available in Indonesia market.
    */
    'buyerServiceFee'?: string;
    /**
    * Item cancellation reason.
    */
    'cancelReason'?: string;
    /**
    * The initiator of a cancellation request: - `BUYER` - `SELLER` - `OPERATOR` - `SYSTEM`
    */
    'cancelUser'?: string;
    /**
    * For a combined listing SKU, returns an array of related product SKUs that compose the combined listing SKU.
    */
    'combinedListingSkus'?: Array<Order202309GetOrderDetailResponseDataOrdersLineItemsCombinedListingSkus>;
    /**
    * Currency for payment.
    */
    'currency'?: string;
    /**
    * - `UNPAID`: The order is placed, but payment is not yet completed. - `AWAITING_SHIPMENT`: The order is ready for shipment, but no items are shipped yet. - `AWAITING_COLLECTION`:  The shipment is arranged, but the package is waiting to be collected by the carrier. - `IN_TRANSIT`: The package is collected by the carrier and delivery is in progress. - `DELIVERED`: The package is delivered to buyer. - `COMPLETED`: The order is completed, and no further returns or refunds are allowed. - `CANCELLED`: The order is canceled.
    */
    'displayStatus'?: string;
    /**
    * [**Deprecated**: This field is deprecated and will be removed in a future API version. Use `handling_duration` instead.]  The number of business days required for the seller to process the order and hand it over to a shipping carrier after the order is placed.  Applicable only if the value for `sku_type` is `MADE_TO_ORDER`.
    */
    'handlingDurationDays'?: string;
    /**
    * Line item ID.
    */
    'id'?: string;
    /**
    * Whether the SKU is a hazmat item. When creating the label for a hazmat item, you must follow the platform rules to put certain items into one package. Please refer to the relationship between `sku_id` and `package_id` to determine how to follow platform rules.
    */
    'isDangerousGood'?: boolean;
    /**
    * Indicates whether the current order line is a gift.
    */
    'isGift'?: boolean;
    /**
    * Item tax detail.
    */
    'itemTax'?: Array<Order202309GetOrderDetailResponseDataOrdersLineItemsItemTax>;
    /**
    * Item original price, please refer to the currency of `payment_info`.
    */
    'originalPrice'?: string;
    /**
    * An order can contain one more more packages based on how the seller chooses to ship. Each package has a unique `package_id` (and also a `tracking_id`, which is used to track the progress of the package as it is shipped). For local sellers in the US and UK markets, the `package_id` and `package_status` property will not be returned before the package is shipped
    */
    'packageId'?: string;
    /**
    * The package status of the item: - `TO_FULFILL`: package waiting seller to arrange shipment. - `PROCESSING`: package shipment has been arranged by seller. Waiting carrier to collect the parcel. - `FULFILLING`: package has been collected by carrier and in transit. - `COMPLETED`: package has been delivered. - `CANCELLED`: package has been canceled. Normally, the package is canceled due to the package being lost or damaged.
    */
    'packageStatus'?: string;
    /**
    * Platform discount amount, please refer to the currency of `payment_info`.
    */
    'platformDiscount'?: string;
    /**
    * Product ID.
    */
    'productId'?: string;
    /**
    * Product name.
    */
    'productName'?: string;
    /**
    * RDF (retail delivery fee). Available only in the US market.
    */
    'retailDeliveryFee'?: string;
    /**
    * The time seller shipped line order (call Ship Order endpoint successfully). Unix timestamp.
    */
    'rtsTime'?: number;
    /**
    * Item sale price, please refer to the currency of `payment_info`.
    */
    'salePrice'?: string;
    /**
    * Seller discount amount. Please refer to the currency of `payment_info`.
    */
    'sellerDiscount'?: string;
    /**
    * The seller stock keeping unit (SKU) of the item. 
    */
    'sellerSku'?: string;
    /**
    * The shipping provider ID of the item.
    */
    'shippingProviderId'?: string;
    /**
    * The shipping provider name of the item.
    */
    'shippingProviderName'?: string;
    /**
    * SKU ID.
    */
    'skuId'?: string;
    /**
    * SKU image.
    */
    'skuImage'?: string;
    /**
    * The name of the SKU, combined by product SKU attribute like size or color. For example, \"Black, 26.\"  
    */
    'skuName'?: string;
    /**
    * [**Deprecated**: This field is deprecated and will be removed in a future API version. Use `order_type` instead.]  The order line type: Possible values based on region:  **All regions** - `NORMAL`: An item that is in stock and available for immediate purchase and fulfillment. - `ZERO_LOTTERY:` An item purchased during a lottery event in TikTok LIVE. - `SHOP_PARTNER`: An item purchased from a TikTok Shop partner store.  **US** - `PRE_ORDER`: An item that is not yet available or released. Fulfillment starts on a specific date in the future. - `MADE_TO_ORDER`: An item that is produced only after the order is received. Fulfillment starts after the product is produced. 
    */
    'skuType'?: string;
    /**
    * Small order fee for TH.
    */
    'smallOrderFee'?: string;
    /**
    * Tracking number. Available after package has been shipped.
    */
    'trackingNumber'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "buyerServiceFee",
            "baseName": "buyer_service_fee",
            "type": "string"
        },
        {
            "name": "cancelReason",
            "baseName": "cancel_reason",
            "type": "string"
        },
        {
            "name": "cancelUser",
            "baseName": "cancel_user",
            "type": "string"
        },
        {
            "name": "combinedListingSkus",
            "baseName": "combined_listing_skus",
            "type": "Array<Order202309GetOrderDetailResponseDataOrdersLineItemsCombinedListingSkus>"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "displayStatus",
            "baseName": "display_status",
            "type": "string"
        },
        {
            "name": "handlingDurationDays",
            "baseName": "handling_duration_days",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isDangerousGood",
            "baseName": "is_dangerous_good",
            "type": "boolean"
        },
        {
            "name": "isGift",
            "baseName": "is_gift",
            "type": "boolean"
        },
        {
            "name": "itemTax",
            "baseName": "item_tax",
            "type": "Array<Order202309GetOrderDetailResponseDataOrdersLineItemsItemTax>"
        },
        {
            "name": "originalPrice",
            "baseName": "original_price",
            "type": "string"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "packageStatus",
            "baseName": "package_status",
            "type": "string"
        },
        {
            "name": "platformDiscount",
            "baseName": "platform_discount",
            "type": "string"
        },
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "productName",
            "baseName": "product_name",
            "type": "string"
        },
        {
            "name": "retailDeliveryFee",
            "baseName": "retail_delivery_fee",
            "type": "string"
        },
        {
            "name": "rtsTime",
            "baseName": "rts_time",
            "type": "number"
        },
        {
            "name": "salePrice",
            "baseName": "sale_price",
            "type": "string"
        },
        {
            "name": "sellerDiscount",
            "baseName": "seller_discount",
            "type": "string"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        },
        {
            "name": "skuImage",
            "baseName": "sku_image",
            "type": "string"
        },
        {
            "name": "skuName",
            "baseName": "sku_name",
            "type": "string"
        },
        {
            "name": "skuType",
            "baseName": "sku_type",
            "type": "string"
        },
        {
            "name": "smallOrderFee",
            "baseName": "small_order_fee",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderDetailResponseDataOrdersLineItems.attributeTypeMap;
    }
}

