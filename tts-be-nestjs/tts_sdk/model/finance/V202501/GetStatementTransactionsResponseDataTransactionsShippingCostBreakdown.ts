/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent } from './GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent';

export class Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown {
    /**
    * The actual shipping fee calculated based on the weight/dimensions measured by the carrier. For details, check `shipping_cost_breakdown.supplementary_component`.
    */
    'actualShippingFeeAmount'?: string;
    /**
    * The actual shipping fee borne by the customer, calculated based on the product weight uploaded by the seller.
    */
    'customerPaidShippingFeeAmount'?: string;
    /**
    * The shipping fee paid by the seller for the delivery of goods exchange.  Applicable only for Indonesia.
    */
    'exchangeShippingFeeAmount'?: string;
    /**
    * The shipping fee paid by the seller for the delivery of goods replacement.  Applicable only for Indonesia.
    */
    'replacementShippingFeeAmount'?: string;
    /**
    * The shipping fee paid by the seller for the delivery of returns.
    */
    'returnShippingFeeAmount'?: string;
    /**
    * The fee borne by the customer to cover the cost of return labels. This fee is collected on the seller\'s behalf and settled according to the agreed payment terms.
    */
    'returnShippingLabelFeeAmount'?: string;
    /**
    * The shipping fee subsidies and incentives provided by the platform. This includes all subsidies regardless of fulfillment channels or policies. For details, check `shipping_cost_breakdown.supplementary_component`.
    */
    'shippingFeeDiscountAmount'?: string;
    /**
    * The shipping insurance fee incurred by the seller for purchasing additional TikTok shipping insurance services.
    */
    'shippingInsuranceFeeAmount'?: string;
    /**
    * The fee incurred for packages requiring signature confirmation services.
    */
    'signatureConfirmationFeeAmount'?: string;
    'supplementaryComponent'?: Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "actualShippingFeeAmount",
            "baseName": "actual_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "customerPaidShippingFeeAmount",
            "baseName": "customer_paid_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "exchangeShippingFeeAmount",
            "baseName": "exchange_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "replacementShippingFeeAmount",
            "baseName": "replacement_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "returnShippingFeeAmount",
            "baseName": "return_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "returnShippingLabelFeeAmount",
            "baseName": "return_shipping_label_fee_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeDiscountAmount",
            "baseName": "shipping_fee_discount_amount",
            "type": "string"
        },
        {
            "name": "shippingInsuranceFeeAmount",
            "baseName": "shipping_insurance_fee_amount",
            "type": "string"
        },
        {
            "name": "signatureConfirmationFeeAmount",
            "baseName": "signature_confirmation_fee_amount",
            "type": "string"
        },
        {
            "name": "supplementaryComponent",
            "baseName": "supplementary_component",
            "type": "Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown.attributeTypeMap;
    }
}

