/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202501GetOrderStatementTransactionsResponseDataSkuTransactions } from './GetOrderStatementTransactionsResponseDataSkuTransactions';

export class Finance202501GetOrderStatementTransactionsResponseData {
    /**
    * The three-digit currency code in ISO 4217 format. 
    */
    'currency'?: string;
    /**
    * The fees and taxes charged by the platform for the order at the time of order settlement. Shipping-related costs are excluded.
    */
    'feeAndTaxAmount'?: string;
    /**
    * The creation time of the order. Unix timestamp.
    */
    'orderCreateTime'?: number;
    /**
    * The order ID in TikTok Shop.
    */
    'orderId'?: string;
    /**
    * The revenue amount for the order at the time of order settlement. This is equivalent to the net sales amount.
    */
    'revenueAmount'?: string;
    /**
    * The settlement amount for the order.  Formula: revenue_amount - shipping_cost_amount - fee_tax_amount
    */
    'settlementAmount'?: string;
    /**
    * The shipping costs for the order at the time of order settlement.
    */
    'shippingCostAmount'?: string;
    /**
    * The list of SKU transaction records for the order.
    */
    'skuTransactions'?: Array<Finance202501GetOrderStatementTransactionsResponseDataSkuTransactions>;
    /**
    * The number of transaction records
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "feeAndTaxAmount",
            "baseName": "fee_and_tax_amount",
            "type": "string"
        },
        {
            "name": "orderCreateTime",
            "baseName": "order_create_time",
            "type": "number"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "skuTransactions",
            "baseName": "sku_transactions",
            "type": "Array<Finance202501GetOrderStatementTransactionsResponseDataSkuTransactions>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetOrderStatementTransactionsResponseData.attributeTypeMap;
    }
}

