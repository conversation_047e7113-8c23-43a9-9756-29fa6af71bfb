/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetStatementTransactionsResponseDataTransactionsRevenueBreakdown {
    /**
    * The cash on delivery service fees charged to buyers. Applicable only for Saudi Arabia.
    */
    'codServiceFeeAmount'?: string;
    /**
    * The refund for cash on delivery service fees. Applicable only for Saudi Arabia.
    */
    'refundCodServiceFeeAmount'?: string;
    /**
    * The total price of all refunded items before any seller discounts. This is equivalent to the shop\'s gross sales refund.
    */
    'refundSubtotalBeforeDiscountAmount'?: string;
    /**
    * The total amount of discounts funded by the seller, including: - Seller promotions (Product Discount, Flash Deal, Buy More Save More, Voucher and Bundle Deal) - Seller\'s portion of a co-funded voucher discount in co-funding campaigns - Seller discounts during a campaign
    */
    'sellerDiscountAmount'?: string;
    /**
    * Discounts returned to the sellers due to returns or refunds.
    */
    'sellerDiscountRefundAmount'?: string;
    /**
    * The total price of all order items before any seller discounts and platform discounts are deducted. This is equivalent to the shop\'s gross sales.
    */
    'subtotalBeforeDiscountAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "codServiceFeeAmount",
            "baseName": "cod_service_fee_amount",
            "type": "string"
        },
        {
            "name": "refundCodServiceFeeAmount",
            "baseName": "refund_cod_service_fee_amount",
            "type": "string"
        },
        {
            "name": "refundSubtotalBeforeDiscountAmount",
            "baseName": "refund_subtotal_before_discount_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountAmount",
            "baseName": "seller_discount_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountRefundAmount",
            "baseName": "seller_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "subtotalBeforeDiscountAmount",
            "baseName": "subtotal_before_discount_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactionsRevenueBreakdown.attributeTypeMap;
    }
}

