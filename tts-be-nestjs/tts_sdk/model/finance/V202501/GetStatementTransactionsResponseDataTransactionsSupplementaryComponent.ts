/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetStatementTransactionsResponseDataTransactionsSupplementaryComponent {
    /**
    * The total amount paid by the customer.
    */
    'customerPaymentAmount'?: string;
    /**
    * The exact amount refunded to the customer.
    */
    'customerRefundAmount'?: string;
    /**
    * The platform\'s portion of a co-funded voucher discount in co-funding campaigns.
    */
    'platformCofundedDiscountAmount'?: string;
    /**
    * The platform\'s portion of a co-funded voucher discount in co-funding campaigns that was refunded to the platform.
    */
    'platformCofundedDiscountRefundAmount'?: string;
    /**
    * The discounts funded by the platform, such as coupons and campaign discounts.
    */
    'platformDiscountAmount'?: string;
    /**
    * The platform discounts to be reversed (and deducted from the final settlement) if the order was refunded as a result of the seller\'s responsibility.
    */
    'platformDiscountRefundAmount'?: string;
    /**
    * The final retail delivery fee for deliveries in Colorado, US. For more information, see [Colorado Retail Delivery Fee FAQ](https://seller-us.tiktok.com/university/essay?knowledge_id=2459780628350762&default_language=en&identity=1).   Formula: retail_delivery_fee_payment + retail_delivery_fee_refund
    */
    'retailDeliveryFeeAmount'?: string;
    /**
    * The retail delivery fee for deliveries in Colorado, US. For more information, see [Colorado Retail Delivery Fee FAQ](https://seller-us.tiktok.com/university/essay?knowledge_id=2459780628350762&default_language=en&identity=1).
    */
    'retailDeliveryFeePaymentAmount'?: string;
    /**
    * The retail delivery fee subsidy by the platform for losses due to returns, refunds, or other issues in Colorado, US.
    */
    'retailDeliveryFeeRefundAmount'?: string;
    /**
    * The final sales tax to be paid by the customer for the product and delivery.   Formula: sales_tax_payment_amount - sales_tax_refund_amount
    */
    'salesTaxAmount'?: string;
    /**
    * The expected sales tax to be paid by the customer for the product and delivery.
    */
    'salesTaxPaymentAmount'?: string;
    /**
    * The sales tax amount returned to the customer in the event of a refund.
    */
    'salesTaxRefundAmount'?: string;
    /**
    * The seller\'s portion of a co-funded voucher discount in co-funding campaigns.
    */
    'sellerCofundedDiscountAmount'?: string;
    /**
    * The seller\'s portion of a co-funded voucher discount in co-funding campaigns that was refunded to the seller.
    */
    'sellerCofundedDiscountRefundAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "customerPaymentAmount",
            "baseName": "customer_payment_amount",
            "type": "string"
        },
        {
            "name": "customerRefundAmount",
            "baseName": "customer_refund_amount",
            "type": "string"
        },
        {
            "name": "platformCofundedDiscountAmount",
            "baseName": "platform_cofunded_discount_amount",
            "type": "string"
        },
        {
            "name": "platformCofundedDiscountRefundAmount",
            "baseName": "platform_cofunded_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountAmount",
            "baseName": "platform_discount_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountRefundAmount",
            "baseName": "platform_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeAmount",
            "baseName": "retail_delivery_fee_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeePaymentAmount",
            "baseName": "retail_delivery_fee_payment_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeRefundAmount",
            "baseName": "retail_delivery_fee_refund_amount",
            "type": "string"
        },
        {
            "name": "salesTaxAmount",
            "baseName": "sales_tax_amount",
            "type": "string"
        },
        {
            "name": "salesTaxPaymentAmount",
            "baseName": "sales_tax_payment_amount",
            "type": "string"
        },
        {
            "name": "salesTaxRefundAmount",
            "baseName": "sales_tax_refund_amount",
            "type": "string"
        },
        {
            "name": "sellerCofundedDiscountAmount",
            "baseName": "seller_cofunded_discount_amount",
            "type": "string"
        },
        {
            "name": "sellerCofundedDiscountRefundAmount",
            "baseName": "seller_cofunded_discount_refund_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactionsSupplementaryComponent.attributeTypeMap;
    }
}

