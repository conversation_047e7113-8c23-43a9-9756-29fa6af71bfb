/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetStatementTransactionsResponseDataTotalSettlementBreakdown {
    /**
    * The total adjustment amount based on TikTok Shop policy.  Refer to `transactions.type` for the list of adjustment-related policies.
    */
    'totalAdjustmentAmount'?: string;
    /**
    * The total fees and taxes charged by the platform at the time of order settlement. Shipping-related costs are excluded.
    */
    'totalFeeTaxAmount'?: string;
    /**
    * The total revenue amount at the time of order settlement. This is equivalent to the net sales amount.
    */
    'totalRevenueAmount'?: string;
    /**
    * The total shipping costs at the time of order settlement.
    */
    'totalShippingCostAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "totalAdjustmentAmount",
            "baseName": "total_adjustment_amount",
            "type": "string"
        },
        {
            "name": "totalFeeTaxAmount",
            "baseName": "total_fee_tax_amount",
            "type": "string"
        },
        {
            "name": "totalRevenueAmount",
            "baseName": "total_revenue_amount",
            "type": "string"
        },
        {
            "name": "totalShippingCostAmount",
            "baseName": "total_shipping_cost_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTotalSettlementBreakdown.attributeTypeMap;
    }
}

