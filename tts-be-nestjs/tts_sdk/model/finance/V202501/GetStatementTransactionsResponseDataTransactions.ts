/**
 * tik<PERSON> shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202501GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdown } from './GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdown';
import { Finance202501GetStatementTransactionsResponseDataTransactionsRevenueBreakdown } from './GetStatementTransactionsResponseDataTransactionsRevenueBreakdown';
import { Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown } from './GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown';
import { Finance202501GetStatementTransactionsResponseDataTransactionsSupplementaryComponent } from './GetStatementTransactionsResponseDataTransactionsSupplementaryComponent';

export class Finance202501GetStatementTransactionsResponseDataTransactions {
    /**
    * The adjustment amount based on TikTok Shop policy.  Refer to `transactions.type` for the list of adjustment-related policies.
    */
    'adjustmentAmount'?: string;
    /**
    * The adjustment ID if the transaction is an adjustment. Each transaction can only be associated with an order ID or an adjustment ID.
    */
    'adjustmentId'?: string;
    /**
    * The order ID associated with the adjustment, if any.
    */
    'adjustmentOrderId'?: string;
    /**
    * The order ID associated with the reserve transaction.
    */
    'associatedOrderId'?: string;
    /**
    * The estimated date when the reserve funds will be released and paid out to the seller. Unix timestamp.  Returns an empty value if the reserve funds have already been released.
    */
    'estimatedReleaseTime'?: string;
    /**
    * The total fees and taxes charged by TikTok Shop at the time of order settlement. Shipping-related costs are excluded. This is equivalent to the sum of all contributory amounts in `fee_tax_breakdown`. 
    */
    'feeTaxAmount'?: string;
    'feeTaxBreakdown'?: Finance202501GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdown;
    /**
    * The transaction ID.
    */
    'id'?: string;
    /**
    * The creation time of the order. Unix timestamp.
    */
    'orderCreateTime'?: number;
    /**
    * The order ID. Each transaction can only be associated with an order ID or an adjustment ID.
    */
    'orderId'?: string;
    /**
    * The amount withheld from settlement based on TikTok Shop Reserve Policy. Refer to TikTok Shop Academy for more information. - A positive amount indicates funds that have been released. - A negative amount indicates funds being withheld from the settlement.
    */
    'reserveAmount'?: string;
    /**
    * The ID of a reserve transaction.
    */
    'reserveId'?: string;
    /**
    * The status of the reserve funds. Possible values: - COLLECTED: A portion of the order\'s settlement amount has been withheld as reserve funds. - RELEASED: The previously reserved funds have been released and paid out to the seller.
    */
    'reserveStatus'?: string;
    /**
    * The revenue amount at the time of order settlement. This is equivalent to the sum of all amounts in `revenue_breakdown`.
    */
    'revenueAmount'?: string;
    'revenueBreakdown'?: Finance202501GetStatementTransactionsResponseDataTransactionsRevenueBreakdown;
    /**
    * The settlement amount for the order.  Formula: revenue_amount - shipping_cost_amount - fee_tax_amount - adjustment_amount
    */
    'settlementAmount'?: string;
    /**
    * The shipping costs at the time of order settlement. This is equivalent to the sum of all contributory amounts in `shipping_cost_breakdown`.
    */
    'shippingCostAmount'?: string;
    'shippingCostBreakdown'?: Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown;
    'supplementaryComponent'?: Finance202501GetStatementTransactionsResponseDataTransactionsSupplementaryComponent;
    /**
    * The transaction type.  **Standard transactions** - `ORDER`: A transaction related to an order settlement. - `RESERVE`: A transaction involving collection or release of reserve funds. - If the transaction is an adjustment, it returns one of the following values:  **Platform-related adjustments** - `CHARGE_BACK`: Charges returned to a payment card after a customer has successfully disputed an item on their account statement or transactions report. - `CUSTOMER_SERVICE_COMPENSATION`: Extra compensation or compensation paid to a customer after the after-sales period by customer service. - `DEDUCTIONS_INCURRED_BY_SELLER`: Deduction arising from customer dissatisfaction as a result of the seller\'s responsibility. This includes issues such as fraud, empty packages, items that do not match the product display page, or items of lower value than advertised. - `GMV_PAYMENT_FOR_ADS`: Amount used to pay for your advertisement if you are enabled \"auto pay ads with shop GMV\", or to pay for Tiktok Promote ads orders. - `PLATFORM_COMMISSION_ADJUSTMENT`: Adjustment when there are differences in the platform commission paid by the seller. - `PLATFORM_COMMISSION_COMPENSATION`: Compensation paid to the seller when there are differences in the platform commission paid by the seller. - `PLATFORM_PENALTY`: Penalty imposed for a violation of TikTok Shop policies (the corresponding amount has been deducted from the seller\'s account). For details, please refer to the email notification sent to the seller. - `PROMOTION_ADJUSTMENT`: Adjustment when a seller takes part in a platform promotion and there are differences between the promotion price and the actual amount paid by the seller. - `REBATE`: A discount on referral fees offered by TikTok Shop to eligible sellers. - `PLATFORM_COMPENSATION`: Compensation paid to the seller after the seller successfully appealed for a customer dispute. - `PLATFORM_REIMBURSEMENT`: Reimbursement paid by TikTok Shop for an order refunded under TikTok\'s refund without return policy (the seller is not responsible). - `COFUNDED_CREATOR_REWARDS`: Fees charged for joining the co-funded creator rewards program to reward creator activities.  **Logistics-related adjustments** - `FBT_WAREHOUSE_SERVICE_FEE`: Amount charged by TikTok Fulfillment Portal (Pipak) for warehousing-related bills incurred by the seller under the Fulfilled by TikTok (FBT) service. - `LOGISTICS_REIMBURSEMENT`: Reimbursement paid by TikTok Shop for an order refunded due to logistics-related issues (e.g. lost or damaged order). - `SHIPPING_FEE_ADJUSTMENT`: Adjustment when there are differences or mistakes with the shipping fee paid by the seller. - `SHIPPING_FEE_COMPENSATION`: Compensation given to sellers due to differences between the actual shipping fee and the pre-paid shipping fee. - `SHIPPING_FEE_REBATE`: Shipping fee rebate provided to the seller as part of their participation in a platform campaign. - `SAMPLE_SHIPPING_FEE`: Fees charged for sending samples using the TikTok logistics provider.  **Miscellaneous adjustments** `OTHER_ADJUSTMENT`: Adjustment for other reasons.
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "adjustmentAmount",
            "baseName": "adjustment_amount",
            "type": "string"
        },
        {
            "name": "adjustmentId",
            "baseName": "adjustment_id",
            "type": "string"
        },
        {
            "name": "adjustmentOrderId",
            "baseName": "adjustment_order_id",
            "type": "string"
        },
        {
            "name": "associatedOrderId",
            "baseName": "associated_order_id",
            "type": "string"
        },
        {
            "name": "estimatedReleaseTime",
            "baseName": "estimated_release_time",
            "type": "string"
        },
        {
            "name": "feeTaxAmount",
            "baseName": "fee_tax_amount",
            "type": "string"
        },
        {
            "name": "feeTaxBreakdown",
            "baseName": "fee_tax_breakdown",
            "type": "Finance202501GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdown"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "orderCreateTime",
            "baseName": "order_create_time",
            "type": "number"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "reserveAmount",
            "baseName": "reserve_amount",
            "type": "string"
        },
        {
            "name": "reserveId",
            "baseName": "reserve_id",
            "type": "string"
        },
        {
            "name": "reserveStatus",
            "baseName": "reserve_status",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "revenueBreakdown",
            "baseName": "revenue_breakdown",
            "type": "Finance202501GetStatementTransactionsResponseDataTransactionsRevenueBreakdown"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "shippingCostBreakdown",
            "baseName": "shipping_cost_breakdown",
            "type": "Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdown"
        },
        {
            "name": "supplementaryComponent",
            "baseName": "supplementary_component",
            "type": "Finance202501GetStatementTransactionsResponseDataTransactionsSupplementaryComponent"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactions.attributeTypeMap;
    }
}

