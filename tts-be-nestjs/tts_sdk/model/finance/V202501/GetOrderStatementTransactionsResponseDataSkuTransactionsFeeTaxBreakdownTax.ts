/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax {
    /**
    * The anti-dumping duties collected by governments for import goods.
    */
    'antiDumpingDutyAmount'?: string;
    /**
    * The fees charged by logistic suppliers for customs clearance services.  Applicable only for cross-border shop orders.
    */
    'customsClearanceAmount'?: string;
    /**
    * The customs duties, a type of tax on cross-border goods collected by governments.  Applicable only for cross-border shop orders.
    */
    'customsDutyAmount'?: string;
    /**
    * The goods and services tax (GST) collected and remitted to the tax authority by the platform for low-value goods imported into Singapore, effective January 1, 2023. 
    */
    'gstAmount'?: string;
    /**
    * The import VAT, a tax paid on goods bought in one country and imported into another.  Applicable only for cross-border shop orders.
    */
    'importVatAmount'?: string;
    /**
    * The Mexican federal income tax that TikTok Shop is required to withhold.
    */
    'isrAmount'?: string;
    /**
    * The Mexican VAT that TikTok Shop is required to withhold on your taxable products and remit to the tax authority.
    */
    'ivaAmount'?: string;
    /**
    * The sales and service tax (SST) collected and remitted to the tax authority by the platform for low-value goods imported into Malaysia, effective January 1, 2024.
    */
    'sstAmount'?: string;
    /**
    * The VAT paid by the platform on the seller\'s behalf.  Applicable only for cross-border shop orders.
    */
    'vatAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "antiDumpingDutyAmount",
            "baseName": "anti_dumping_duty_amount",
            "type": "string"
        },
        {
            "name": "customsClearanceAmount",
            "baseName": "customs_clearance_amount",
            "type": "string"
        },
        {
            "name": "customsDutyAmount",
            "baseName": "customs_duty_amount",
            "type": "string"
        },
        {
            "name": "gstAmount",
            "baseName": "gst_amount",
            "type": "string"
        },
        {
            "name": "importVatAmount",
            "baseName": "import_vat_amount",
            "type": "string"
        },
        {
            "name": "isrAmount",
            "baseName": "isr_amount",
            "type": "string"
        },
        {
            "name": "ivaAmount",
            "baseName": "iva_amount",
            "type": "string"
        },
        {
            "name": "sstAmount",
            "baseName": "sst_amount",
            "type": "string"
        },
        {
            "name": "vatAmount",
            "baseName": "vat_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax.attributeTypeMap;
    }
}

