/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownFee } from './GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownFee';
import { Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax } from './GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax';

export class Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdown {
    'fee'?: Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownFee;
    'tax'?: Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "fee",
            "baseName": "fee",
            "type": "Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownFee"
        },
        {
            "name": "tax",
            "baseName": "tax",
            "type": "Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdownTax"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetOrderStatementTransactionsResponseDataSkuTransactionsFeeTaxBreakdown.attributeTypeMap;
    }
}

