/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202501GetStatementTransactionsResponseDataTotalSettlementBreakdown } from './GetStatementTransactionsResponseDataTotalSettlementBreakdown';
import { Finance202501GetStatementTransactionsResponseDataTransactions } from './GetStatementTransactionsResponseDataTransactions';

export class Finance202501GetStatementTransactionsResponseData {
    /**
    * The time when the statement was generated. Unix timestamp.  Statements are generated daily at 00:00 UTC. 
    */
    'createTime'?: number;
    /**
    * The three-digit currency code in ISO 4217 format. 
    */
    'currency'?: string;
    /**
    * The statement ID.
    */
    'id'?: string;
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The final amount paid out after accounting for reserve funds. Formula: total_settlement_amount + total_reserve_amount
    */
    'payableAmount'?: string;
    /**
    * The statement status. Only supports `SETTLED`.
    */
    'status'?: string;
    /**
    * The number of transaction records in the statement.
    */
    'totalCount'?: number;
    /**
    * The total amount withheld from settlement based on TikTok Shop Reserve Policy. Refer to TikTok Shop Academy for more information. - A positive amount indicates the funds that have been released. - A negative amount indicates the funds being withheld from the settlement.  Applicable only for UK and US local sellers.
    */
    'totalReserveAmount'?: string;
    /**
    * The total settlement amount.  Formula: total_revenue_amount - total_shipping_cost_amount - total_fee_tax_amount - total_adjustment_amount
    */
    'totalSettlementAmount'?: string;
    'totalSettlementBreakdown'?: Finance202501GetStatementTransactionsResponseDataTotalSettlementBreakdown;
    /**
    * The list of transaction records in the statement. Each transaction corresponds to an order, an adjustment, or a reserve-related transaction.
    */
    'transactions'?: Array<Finance202501GetStatementTransactionsResponseDataTransactions>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "payableAmount",
            "baseName": "payable_amount",
            "type": "string"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        },
        {
            "name": "totalReserveAmount",
            "baseName": "total_reserve_amount",
            "type": "string"
        },
        {
            "name": "totalSettlementAmount",
            "baseName": "total_settlement_amount",
            "type": "string"
        },
        {
            "name": "totalSettlementBreakdown",
            "baseName": "total_settlement_breakdown",
            "type": "Finance202501GetStatementTransactionsResponseDataTotalSettlementBreakdown"
        },
        {
            "name": "transactions",
            "baseName": "transactions",
            "type": "Array<Finance202501GetStatementTransactionsResponseDataTransactions>"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseData.attributeTypeMap;
    }
}

