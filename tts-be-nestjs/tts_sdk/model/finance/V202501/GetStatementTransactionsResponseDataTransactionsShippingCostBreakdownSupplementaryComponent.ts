/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent {
    /**
    * The fee to offset TikTok Shop Shipping Incentive or customer-paid shipping fee, resulting in a net charge of $0 to the seller.  Applicable only for the US.
    */
    'customerShippingFeeOffsetAmount'?: string;
    /**
    * The shipping fee incurred by the seller for using TikTok Shipping. This is part of `actual_shipping_fee_amount`.
    */
    'fbmShippingCostAmount'?: string;
    /**
    * The shipping and warehouse fulfillment fee incurred by the seller for orders fulfilled by TikTok (FBT). This is part of `actual_shipping_fee_amount`.  Applicable only for the US.
    */
    'fbtFulfillmentFeeAmount'?: string;
    /**
    * The shipping fee incurred by the seller for orders fulfilled by TikTok (FBT). This is part of `actual_shipping_fee_amount`.  Applicable only for EU and UK.
    */
    'fbtShippingCostAmount'?: string;
    /**
    * The shipping fee discount provided in accordance with a campaign policy. This is part of `shipping_fee_discount_amount`.
    */
    'platformShippingFeeDiscountAmount'?: string;
    /**
    * The additional shipping incentive that the seller will receive if the seller signed up for the Co-Funded Free Shipping Program. A negative amount indicates a reversal of incentives due to order refunds attributed to the seller\'s responsibility. This is part of `shipping_fee_discount_amount`.
    */
    'promoShippingIncentiveAmount'?: string;
    /**
    * The shipping fee discount provided by sellers.
    */
    'sellerShippingFeeDiscountAmount'?: string;
    /**
    * The shipping fee subsidy funded by the platform for seller shipping. This is part of `shipping_fee_discount_amount`. - Positive amount represents a subsidy received by the seller. - Negative amount represents a subsidy that the seller must return to TikTok Shop.
    */
    'shippingFeeSubsidyAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "customerShippingFeeOffsetAmount",
            "baseName": "customer_shipping_fee_offset_amount",
            "type": "string"
        },
        {
            "name": "fbmShippingCostAmount",
            "baseName": "fbm_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "fbtFulfillmentFeeAmount",
            "baseName": "fbt_fulfillment_fee_amount",
            "type": "string"
        },
        {
            "name": "fbtShippingCostAmount",
            "baseName": "fbt_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "platformShippingFeeDiscountAmount",
            "baseName": "platform_shipping_fee_discount_amount",
            "type": "string"
        },
        {
            "name": "promoShippingIncentiveAmount",
            "baseName": "promo_shipping_incentive_amount",
            "type": "string"
        },
        {
            "name": "sellerShippingFeeDiscountAmount",
            "baseName": "seller_shipping_fee_discount_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeSubsidyAmount",
            "baseName": "shipping_fee_subsidy_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactionsShippingCostBreakdownSupplementaryComponent.attributeTypeMap;
    }
}

