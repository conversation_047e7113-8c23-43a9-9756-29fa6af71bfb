/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202501GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdownFee {
    /**
    * The commission for eligible orders from ads.
    */
    'affiliateAdsCommissionAmount'?: string;
    /**
    * The commission amount charged to the seller for payment to the creator.
    */
    'affiliateCommissionAmount'?: string;
    /**
    * The affiliate ads commission paid to a creator before any personal income tax withholding.  Applicable only for SEA markets.
    */
    'affiliateCommissionAmountBeforePit'?: string;
    /**
    * The commission amount for purchases through affiliate partner links.
    */
    'affiliatePartnerCommissionAmount'?: string;
    /**
    * The service fee charged for participation in the bonus cashback program.
    */
    'bonusCashbackServiceFeeAmount'?: string;
    /**
    * The service fee charged for co-funded promotions.
    */
    'cofundedPromotionServiceFeeAmount'?: string;
    /**
    * The handling fee charged when the buyer pays with a credit card.
    */
    'creditCardHandlingFeeAmount'?: string;
    /**
    * The handling fee charged for orders that are fulfilled by Dilayani Tokopedia.
    */
    'dtHandlingFeeAmount'?: string;
    /**
    * The eco-contributions TikTok Shop pays on your behalf to the qualified producer responsibility organization (PRO).
    */
    'eprPobServiceFeeAmount'?: string;
    /**
    * The service fee charged for participation in flash sales.
    */
    'flashSalesServiceFeeAmount'?: string;
    /**
    * The service fee charged for participation in the [LIVE Specials Programme].
    */
    'liveSpecialsFeeAmount'?: string;
    /**
    * The service fee charged for using TikTok Shop Mall.
    */
    'mallServiceFeeAmount'?: string;
    /**
    * The commission amount charged by the platform.
    */
    'platformCommissionAmount'?: string;
    /**
    * The service fee charged for participation in the pre-order program.
    */
    'preOrderServiceFeeAmount'?: string;
    /**
    * The referral fee charged for processing successful orders.  Applicable only for the US. 
    */
    'referralFeeAmount'?: string;
    /**
    * The 20% refund administration fee deducted from the total refunded referral fee amount.
    */
    'refundAdministrationFeeAmount'?: string;
    /**
    * The handling fee charged to the seller for participation in the PayLater program.
    */
    'sellerPaylaterHandlingFeeAmount'?: string;
    /**
    * The service fee charged for participation in the [Seller Free Shipping Programme].
    */
    'sfpServiceFeeAmount'?: string;
    /**
    * The transaction fee charged for processing successful orders.
    */
    'transactionFeeAmount'?: string;
    /**
    * The commission amount charged by TikTok Shop Partners (TSP).
    */
    'tspCommissionAmount'?: string;
    /**
    * The service fee charged for participation in the Voucher Xtra program.
    */
    'voucherXtraServiceFeeAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "affiliateAdsCommissionAmount",
            "baseName": "affiliate_ads_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionAmount",
            "baseName": "affiliate_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionAmountBeforePit",
            "baseName": "affiliate_commission_amount_before_pit",
            "type": "string"
        },
        {
            "name": "affiliatePartnerCommissionAmount",
            "baseName": "affiliate_partner_commission_amount",
            "type": "string"
        },
        {
            "name": "bonusCashbackServiceFeeAmount",
            "baseName": "bonus_cashback_service_fee_amount",
            "type": "string"
        },
        {
            "name": "cofundedPromotionServiceFeeAmount",
            "baseName": "cofunded_promotion_service_fee_amount",
            "type": "string"
        },
        {
            "name": "creditCardHandlingFeeAmount",
            "baseName": "credit_card_handling_fee_amount",
            "type": "string"
        },
        {
            "name": "dtHandlingFeeAmount",
            "baseName": "dt_handling_fee_amount",
            "type": "string"
        },
        {
            "name": "eprPobServiceFeeAmount",
            "baseName": "epr_pob_service_fee_amount",
            "type": "string"
        },
        {
            "name": "flashSalesServiceFeeAmount",
            "baseName": "flash_sales_service_fee_amount",
            "type": "string"
        },
        {
            "name": "liveSpecialsFeeAmount",
            "baseName": "live_specials_fee_amount",
            "type": "string"
        },
        {
            "name": "mallServiceFeeAmount",
            "baseName": "mall_service_fee_amount",
            "type": "string"
        },
        {
            "name": "platformCommissionAmount",
            "baseName": "platform_commission_amount",
            "type": "string"
        },
        {
            "name": "preOrderServiceFeeAmount",
            "baseName": "pre_order_service_fee_amount",
            "type": "string"
        },
        {
            "name": "referralFeeAmount",
            "baseName": "referral_fee_amount",
            "type": "string"
        },
        {
            "name": "refundAdministrationFeeAmount",
            "baseName": "refund_administration_fee_amount",
            "type": "string"
        },
        {
            "name": "sellerPaylaterHandlingFeeAmount",
            "baseName": "seller_paylater_handling_fee_amount",
            "type": "string"
        },
        {
            "name": "sfpServiceFeeAmount",
            "baseName": "sfp_service_fee_amount",
            "type": "string"
        },
        {
            "name": "transactionFeeAmount",
            "baseName": "transaction_fee_amount",
            "type": "string"
        },
        {
            "name": "tspCommissionAmount",
            "baseName": "tsp_commission_amount",
            "type": "string"
        },
        {
            "name": "voucherXtraServiceFeeAmount",
            "baseName": "voucher_xtra_service_fee_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202501GetStatementTransactionsResponseDataTransactionsFeeTaxBreakdownFee.attributeTypeMap;
    }
}

