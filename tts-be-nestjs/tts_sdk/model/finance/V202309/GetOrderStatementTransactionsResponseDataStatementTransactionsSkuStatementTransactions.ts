/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202309GetOrderStatementTransactionsResponseDataStatementTransactionsSkuStatementTransactions {
    /**
    * The return shipping fee charged if the seller is responsible for the return.
    */
    'actualReturnShippingFeeAmount'?: string;
    /**
    * The actual shipping fee, calculated based on the weight/dimensions measured by the carrier.
    */
    'actualShippingFeeAmount'?: string;
    /**
    * The adjustment amount. 
    */
    'adjustmentAmount'?: string;
    /**
    * The commission for eligible orders from ads.  Formula: (product price - TikTok Shop discounts - seller discounts) * ads commission rate
    */
    'affiliateAdsCommissionAmount'?: string;
    /**
    * The commission amount charged to the seller for payment to the creator.  Formula: (product price - TikTok Shop coupons - seller coupons) * affiliate commission rate
    */
    'affiliateCommissionAmount'?: string;
    /**
    * The affiliate commission paid to a creator before any personal income tax withholding.
    */
    'affiliateCommissionBeforePit'?: string;
    /**
    * The commission amount for purchases through affiliate partner links.  Formula: (product price - TikTok Shop discounts - seller discounts) * affiliate partner commission rate
    */
    'affiliatePartnerCommissionAmount'?: string;
    /**
    * The total price of all order items after the seller\'s discount has been deducted. 
    */
    'afterSellerDiscountsSubtotalAmount'?: string;
    /**
    * The currency code in ISO 4217 format.
    */
    'currency'?: string;
    /**
    * The customer refund amount deducted from the final settlement. 
    */
    'customerOrderRefundAmount'?: string;
    /**
    * The actual shipping fee borne by the customer.
    */
    'customerPaidShippingFeeAmount'?: string;
    /**
    * The amount of customer-paid shipping fees that are returned to the customer.
    */
    'customerPaidShippingFeeRefundAmount'?: string;
    /**
    * The final amount paid by the customer.   Formula: after_seller_discounts_subtotal_amount + customer_shipping_fee_amount - platform_discount_amount + sales_tax_payment_amount
    */
    'customerPaymentAmount'?: string;
    /**
    * The exact amount refunded to the customer. 
    */
    'customerRefundAmount'?: string;
    /**
    * The expected shipping fee borne by the customer, calculated based on the product weight uploaded by the seller.
    */
    'customerShippingFeeAmount'?: string;
    /**
    * The fee to offset TikTok Shop Shipping Incentive or customer-paid shipping fee, resulting in a net charge of $0 to the seller. Applicable only for the US. 
    */
    'customerShippingFeeOffsetAmount'?: string;
    /**
    * The shipping fee incurred by the seller for using TikTok Shipping.
    */
    'fbmShippingCostAmount'?: string;
    /**
    * The shipping fee incurred by the seller for orders fulfilled by TikTok (FBT). Applicable only for the US.
    */
    'fbtFulfillmentFeeAmount'?: string;
    /**
    * The shipping fee incurred by the seller for orders fulfilled by TikTok (FBT). Applicable for all regions except the US.
    */
    'fbtShippingCostAmount'?: string;
    /**
    * The fees charged by TikTok Shop at the time of order settlement. An order is deemed settled a certain number of days after delivery (varies by region) if no returns or refunds are pending.  **Note**: For UK and US, shipping-related costs are excluded.
    */
    'feeAmount'?: string;
    /**
    * The total sales revenue before any seller or TikTok Shop discounts are applied.
    */
    'grossSalesAmount'?: string;
    /**
    * The sales amount refunded to customers.
    */
    'grossSalesRefundAmount'?: string;
    /**
    * ISR refers to Mexican income tax that TikTok Shop is required to withhold from your earnings and remit to the tax authority.
    */
    'isrIncomeTaxAmount'?: string;
    /**
    * IVA refers to Mexican VAT that TikTok Shop is required to withhold on your taxable products and remit to the tax authority. 
    */
    'ivaVatAmount'?: string;
    /**
    * The revenue amount after seller discounts are deducted. Applicable only for local sellers in UK and US.  Formula: gross_sales_amount + gross_sales_refund_amount + seller_discount_amount + seller_discount_refund_amount
    */
    'netSalesAmount'?: string;
    /**
    * The creator\'s personal income tax withholding amount. Sellers are responsible for filing and remitting withheld taxes based on regional tax laws.
    */
    'pitAmount'?: string;
    /**
    * The commission amount charged by TikTok Shop. Applicable only for the UK.  Formula: commission rate * (customer_payment_amount - customer_order_refund_amount + platform_discount_amount - platform_discount_refund_amount)
    */
    'platformCommissionAmount'?: string;
    /**
    * The discounts funded by TikTok Shop, such as coupons and campaign discounts.
    */
    'platformDiscountAmount'?: string;
    /**
    * The platform discount to be reversed (and deducted from the final settlement) if the order was refunded as a result of the seller\'s responsibility. 
    */
    'platformDiscountRefundAmount'?: string;
    /**
    * The TikTok Shop subsidy to be reversed (and deducted from the final settlement) if the order was refunded as a result of the seller\'s responsibility. 
    */
    'platformRefundSubsidyAmount'?: string;
    /**
    * The shipping fee discount provided for orders using TikTok Shipping.
    */
    'platformShippingFeeDiscountAmount'?: string;
    /**
    * The product name.
    */
    'productName'?: string;
    /**
    * The additional shipping incentive that the seller will receive if the seller signed up for the Co-Funded Free Shipping Program from 2024/08/26 to 2024/12/31. A negative amount indicates a reversal of incentives due to order refunds attributed to the seller\'s responsibility.
    */
    'promoShippingIncentiveAmount'?: string;
    /**
    * The SKU quantity.
    */
    'quantity'?: number;
    /**
    * The referral fee charged for processing successful orders.  Applicable only for the US.
    */
    'referralFeeAmount'?: string;
    /**
    * The 20% refund administration fee deducted from the total refunded referral fee amount.
    */
    'refundAdministrationFeeAmount'?: string;
    /**
    * The TikTok Shop shipping incentive to be reversed (and deducted) if the order was refunded as a result of the seller\'s responsibility.
    */
    'refundShippingCostDiscountAmount'?: string;
    /**
    * The final retail delivery fee for deliveries in Colorado, US. For more information, see [Colorado Retail Delivery Fee FAQ](https://seller-us.tiktok.com/university/essay?knowledge_id=2459780628350762&default_language=en&identity=1).  Formula: retail_delivery_fee_payment_amount + retail_delivery_fee_refund_amount.
    */
    'retailDeliveryFeeAmount'?: string;
    /**
    * The retail delivery fee for deliveries in Colorado, US. For more information, see [Colorado Retail Delivery Fee FAQ](https://seller-us.tiktok.com/university/essay?knowledge_id=2459780628350762&default_language=en&identity=1).
    */
    'retailDeliveryFeePaymentAmount'?: string;
    /**
    * The retail delivery fee subsidy by TikTok Shop for losses due to returns, refunds, or other issues. 
    */
    'retailDeliveryFeeRefundAmount'?: string;
    /**
    * The shipping fee paid by the seller for the delivery of returns.
    */
    'returnShippingFeeAmount'?: string;
    /**
    * The revenue amount for the transaction. Applicable for all regions except UK and US.  Formula: customer_payment_amount + platform_discount_amount + platform_discount_refund_amount + customer_order_refund_amount + shipping_fee_subsidy_amount
    */
    'revenueAmount'?: string;
    /**
    * The final sales tax collected from the customer for the product and delivery.  Applicable only for the US.   Formula: sales_tax_payment_amount - sales_tax_refund_amount
    */
    'salesTaxAmount'?: string;
    /**
    * The expected sales tax to be paid by the customer. Applicable only for the US.
    */
    'salesTaxPaymentAmount'?: string;
    /**
    * The sales tax amount returned to the customer in the event of a refund. Applicable only for the US.
    */
    'salesTaxRefundAmount'?: string;
    /**
    * The total amount of discounts funded by the seller, including: - Seller promotions (Product Discount, Flash Deal, Buy More Save More, Voucher and Bundle Deal) - Seller\'s portion of a co-funded voucher discount in co-funding campaigns - Seller discounts during a campaign
    */
    'sellerDiscountAmount'?: string;
    /**
    * The discounts returned to the sellers due to returns or refunds. 
    */
    'sellerDiscountRefundAmount'?: string;
    /**
    * The final settlement amount for the transaction.   **For UK and US local sellers** Formula: net_sales_amount + shipping_cost_amount + fee_amount + adjustment_amount **For other regions** Formula: revenue_amount + fee_amount + adjustment_amount.
    */
    'settlementAmount'?: string;
    /**
    * The final shipping fees. Applicable only for local sellers in UK and US.  Formula: fbm_shipping_cost_amount + fbt_shipping_cost_amount + signature_confirmation_fee_amount + customer_paid_shipping_fee_amount + customer_paid_shipping_fee_refund_amount + shipping_cost_discount_amount + refund_shipping_cost_discount_amount + shipping_fee_subsidy_amount + return_shipping_fee_amount + shipping_insurance_amount + customer_shipping_fee_offset_amount + fbt_fulfillment_fee_amount + promo_shipping_incentive_amount.
    */
    'shippingCostAmount'?: string;
    /**
    * The TikTok Shop shipping incentive.
    */
    'shippingCostDiscountAmount'?: string;
    /**
    * The final shipping fee. Applicable for all regions except UK and US.  Formula: actual_shipping_fee_amount - platform_shipping_fee_discount_amount + actual_return_shipping_fee_amount + signature_confirmation_fee_amount
    */
    'shippingFeeAmount'?: string;
    /**
    * The shipping fee subsidy funded by TikTok Shop for seller shipping. - Positive amount represents a subsidy received by the seller. - Negative amount represents a subsidy that the seller must return to TikTok Shop.
    */
    'shippingFeeSubsidyAmount'?: string;
    /**
    * The shipping insurance fee incurred by the seller for purchasing additional TikTok shipping insurance. 
    */
    'shippingInsuranceFeeAmount'?: string;
    /**
    * The fee incurred for packages requiring signature confirmations. 
    */
    'signatureConfirmationFeeAmount'?: string;
    /**
    * The SKU ID associated with the transaction.
    */
    'skuId'?: string;
    /**
    * The SKU name.
    */
    'skuName'?: string;
    /**
    * The transaction fee charged for processing successful orders. Non-refundable. Applicable only for the US. 
    */
    'transactionFeeAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "actualReturnShippingFeeAmount",
            "baseName": "actual_return_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "actualShippingFeeAmount",
            "baseName": "actual_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "adjustmentAmount",
            "baseName": "adjustment_amount",
            "type": "string"
        },
        {
            "name": "affiliateAdsCommissionAmount",
            "baseName": "affiliate_ads_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionAmount",
            "baseName": "affiliate_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionBeforePit",
            "baseName": "affiliate_commission_before_pit",
            "type": "string"
        },
        {
            "name": "affiliatePartnerCommissionAmount",
            "baseName": "affiliate_partner_commission_amount",
            "type": "string"
        },
        {
            "name": "afterSellerDiscountsSubtotalAmount",
            "baseName": "after_seller_discounts_subtotal_amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "customerOrderRefundAmount",
            "baseName": "customer_order_refund_amount",
            "type": "string"
        },
        {
            "name": "customerPaidShippingFeeAmount",
            "baseName": "customer_paid_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "customerPaidShippingFeeRefundAmount",
            "baseName": "customer_paid_shipping_fee_refund_amount",
            "type": "string"
        },
        {
            "name": "customerPaymentAmount",
            "baseName": "customer_payment_amount",
            "type": "string"
        },
        {
            "name": "customerRefundAmount",
            "baseName": "customer_refund_amount",
            "type": "string"
        },
        {
            "name": "customerShippingFeeAmount",
            "baseName": "customer_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "customerShippingFeeOffsetAmount",
            "baseName": "customer_shipping_fee_offset_amount",
            "type": "string"
        },
        {
            "name": "fbmShippingCostAmount",
            "baseName": "fbm_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "fbtFulfillmentFeeAmount",
            "baseName": "fbt_fulfillment_fee_amount",
            "type": "string"
        },
        {
            "name": "fbtShippingCostAmount",
            "baseName": "fbt_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "feeAmount",
            "baseName": "fee_amount",
            "type": "string"
        },
        {
            "name": "grossSalesAmount",
            "baseName": "gross_sales_amount",
            "type": "string"
        },
        {
            "name": "grossSalesRefundAmount",
            "baseName": "gross_sales_refund_amount",
            "type": "string"
        },
        {
            "name": "isrIncomeTaxAmount",
            "baseName": "isr_income_tax_amount",
            "type": "string"
        },
        {
            "name": "ivaVatAmount",
            "baseName": "iva_vat_amount",
            "type": "string"
        },
        {
            "name": "netSalesAmount",
            "baseName": "net_sales_amount",
            "type": "string"
        },
        {
            "name": "pitAmount",
            "baseName": "pit_amount",
            "type": "string"
        },
        {
            "name": "platformCommissionAmount",
            "baseName": "platform_commission_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountAmount",
            "baseName": "platform_discount_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountRefundAmount",
            "baseName": "platform_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "platformRefundSubsidyAmount",
            "baseName": "platform_refund_subsidy_amount",
            "type": "string"
        },
        {
            "name": "platformShippingFeeDiscountAmount",
            "baseName": "platform_shipping_fee_discount_amount",
            "type": "string"
        },
        {
            "name": "productName",
            "baseName": "product_name",
            "type": "string"
        },
        {
            "name": "promoShippingIncentiveAmount",
            "baseName": "promo_shipping_incentive_amount",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        },
        {
            "name": "referralFeeAmount",
            "baseName": "referral_fee_amount",
            "type": "string"
        },
        {
            "name": "refundAdministrationFeeAmount",
            "baseName": "refund_administration_fee_amount",
            "type": "string"
        },
        {
            "name": "refundShippingCostDiscountAmount",
            "baseName": "refund_shipping_cost_discount_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeAmount",
            "baseName": "retail_delivery_fee_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeePaymentAmount",
            "baseName": "retail_delivery_fee_payment_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeRefundAmount",
            "baseName": "retail_delivery_fee_refund_amount",
            "type": "string"
        },
        {
            "name": "returnShippingFeeAmount",
            "baseName": "return_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "salesTaxAmount",
            "baseName": "sales_tax_amount",
            "type": "string"
        },
        {
            "name": "salesTaxPaymentAmount",
            "baseName": "sales_tax_payment_amount",
            "type": "string"
        },
        {
            "name": "salesTaxRefundAmount",
            "baseName": "sales_tax_refund_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountAmount",
            "baseName": "seller_discount_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountRefundAmount",
            "baseName": "seller_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "shippingCostDiscountAmount",
            "baseName": "shipping_cost_discount_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeAmount",
            "baseName": "shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeSubsidyAmount",
            "baseName": "shipping_fee_subsidy_amount",
            "type": "string"
        },
        {
            "name": "shippingInsuranceFeeAmount",
            "baseName": "shipping_insurance_fee_amount",
            "type": "string"
        },
        {
            "name": "signatureConfirmationFeeAmount",
            "baseName": "signature_confirmation_fee_amount",
            "type": "string"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        },
        {
            "name": "skuName",
            "baseName": "sku_name",
            "type": "string"
        },
        {
            "name": "transactionFeeAmount",
            "baseName": "transaction_fee_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetOrderStatementTransactionsResponseDataStatementTransactionsSkuStatementTransactions.attributeTypeMap;
    }
}

