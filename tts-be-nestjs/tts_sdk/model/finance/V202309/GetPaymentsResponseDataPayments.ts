/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202309GetPaymentsResponseDataPaymentsAmount } from './GetPaymentsResponseDataPaymentsAmount';
import { Finance202309GetPaymentsResponseDataPaymentsPaymentAmountBeforeExchange } from './GetPaymentsResponseDataPaymentsPaymentAmountBeforeExchange';
import { Finance202309GetPaymentsResponseDataPaymentsReserveAmount } from './GetPaymentsResponseDataPaymentsReserveAmount';
import { Finance202309GetPaymentsResponseDataPaymentsSettlementAmount } from './GetPaymentsResponseDataPaymentsSettlementAmount';

export class Finance202309GetPaymentsResponseDataPayments {
    'amount'?: Finance202309GetPaymentsResponseDataPaymentsAmount;
    /**
    * The seller\'s bank account number masked, revealing only the last 4 digits for privacy.
    */
    'bankAccount'?: string;
    /**
    * The time when the payment was initiated in TikTok Shop. Unix timestamp.
    */
    'createTime'?: number;
    /**
    * The exchange rate, displayed with six decimal places.
    */
    'exchangeRate'?: string;
    /**
    * The payment ID.
    */
    'id'?: string;
    /**
    * The time when the payment was successfully processed. Unix timestamp.
    */
    'paidTime'?: number;
    'paymentAmountBeforeExchange'?: Finance202309GetPaymentsResponseDataPaymentsPaymentAmountBeforeExchange;
    'reserveAmount'?: Finance202309GetPaymentsResponseDataPaymentsReserveAmount;
    'settlementAmount'?: Finance202309GetPaymentsResponseDataPaymentsSettlementAmount;
    /**
    * The payment status, indicating whether payment has been transferred to the seller\'s bank account. Possible values: - PAID: Payment has been transferred to the seller. - FAILED: Payment transfer failed. - PROCESSING: Payment is currently being processed.
    */
    'status'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "amount",
            "baseName": "amount",
            "type": "Finance202309GetPaymentsResponseDataPaymentsAmount"
        },
        {
            "name": "bankAccount",
            "baseName": "bank_account",
            "type": "string"
        },
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "exchangeRate",
            "baseName": "exchange_rate",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "paidTime",
            "baseName": "paid_time",
            "type": "number"
        },
        {
            "name": "paymentAmountBeforeExchange",
            "baseName": "payment_amount_before_exchange",
            "type": "Finance202309GetPaymentsResponseDataPaymentsPaymentAmountBeforeExchange"
        },
        {
            "name": "reserveAmount",
            "baseName": "reserve_amount",
            "type": "Finance202309GetPaymentsResponseDataPaymentsReserveAmount"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "Finance202309GetPaymentsResponseDataPaymentsSettlementAmount"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetPaymentsResponseDataPayments.attributeTypeMap;
    }
}

