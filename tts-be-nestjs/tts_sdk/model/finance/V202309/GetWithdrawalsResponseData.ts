/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202309GetWithdrawalsResponseDataWithdrawals } from './GetWithdrawalsResponseDataWithdrawals';

export class Finance202309GetWithdrawalsResponseData {
    /**
    * Cursor used for searching for more information 
    */
    'nextPageToken'?: string;
    /**
    * The total num of the withdraws
    */
    'totalCount'?: number;
    /**
    * Withdraw list
    */
    'withdrawals'?: Array<Finance202309GetWithdrawalsResponseDataWithdrawals>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        },
        {
            "name": "withdrawals",
            "baseName": "withdrawals",
            "type": "Array<Finance202309GetWithdrawalsResponseDataWithdrawals>"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetWithdrawalsResponseData.attributeTypeMap;
    }
}

