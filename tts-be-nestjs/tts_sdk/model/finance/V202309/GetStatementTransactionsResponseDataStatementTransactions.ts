/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202309GetStatementTransactionsResponseDataStatementTransactions {
    /**
    * If the seller is responsible for the return, the return shipping fee will be charged.
    */
    'actualReturnShippingFeeAmount'?: string;
    /**
    * The actual shipping fee calculated based on the weight/dimensions measured by the carrier
    */
    'actualShippingFeeAmount'?: string;
    /**
    * The adjustment amount calculated by the platform when the order fees need to be changed, the specific adjustment type will be shown in fields type
    */
    'adjustmentAmount'?: string;
    /**
    * The unique ID of the adjustment transaction，One of Order ID and Adjustment ID must not be 0
    */
    'adjustmentId'?: string;
    /**
    * Orders related to adjustments, if any.
    */
    'adjustmentOrderId'?: string;
    /**
    * The price the customer actually paid for the product (for eligible orders that come from ads) multiplied by the commission percentage.
    */
    'affiliateAdsCommissionAmount'?: string;
    /**
    * The price the customer actually paid for the product (product sale price minus any platform coupons and merchant coupons) multiplied by the commission percentage.
    */
    'affiliateCommissionAmount'?: string;
    /**
    * The commission paid to a creator before any personal income tax withholding.
    */
    'affiliateCommissionBeforePit'?: string;
    /**
    * The amount the customer paid for the product (product price minus platform and seller discounts) via affiliate partner\'s links multiplied by the affiliate partner commission rate.
    */
    'affiliatePartnerCommissionAmount'?: string;
    /**
    * The price of all order items after the seller\'s discount is deducted
    */
    'afterSellerDiscountsSubtotalAmount'?: string;
    /**
    * The three-digit currency code in ISO 4217 format.   
    */
    'currency'?: string;
    /**
    * The refunded amount deducted from the final settlement
    */
    'customerOrderRefundAmount'?: string;
    /**
    * The shipping fee incurred by the customer is determined by the product weight uploaded.
    */
    'customerPaidShippingFeeAmount'?: string;
    /**
    * The refunded amount of customer-paid shipping fees deducted from the seller\'s account.
    */
    'customerPaidShippingFeeRefundAmount'?: string;
    /**
    * The total amount paid by the customer, customer_payment = after_seller_discounts_subtotal_amount+customer_shipping_fee_amount-platform_discount_amount+sales_tax_payment_amount
    */
    'customerPaymentAmount'?: string;
    /**
    * The exact amount refunded to the customer
    */
    'customerRefundAmount'?: string;
    /**
    * The expected shipping fee borne by the buyer calculated based on the weight of the product uploaded by the seller
    */
    'customerShippingFeeAmount'?: string;
    /**
    * This billing item is currently applicable to US sellers only. Although included on FBT invoices, it will not lead to any net charges for sellers. Any shipping fees paid by the customer or the platform will be debited and forwarded to TikTok Shop.
    */
    'customerShippingFeeOffsetAmount'?: string;
    /**
    * Full name is TikTok Shop shipping fee.The shipping fee incurred by the seller for using TikTok shipping. 
    */
    'fbmShippingCostAmount'?: string;
    /**
    * The shipping fee incurred by sellers for orders fulfilled by TikTok.
    */
    'fbtFulfillmentFeeAmount'?: string;
    /**
    * Full name is Fulfilled by TikTok Shop (FBT) shipping fee.The shipping fee incurred by the seller for orders fulfilled by TikTok.
    */
    'fbtShippingCostAmount'?: string;
    /**
    * The fees calculated by platform when the order meet the settlement rules (the order is deemed to settle X days after it is delivered with no return/refund ongoing)
    */
    'feeAmount'?: string;
    /**
    * The total revenue before any discounts from the seller or TikTok Shop have been taken into account.
    */
    'grossSalesAmount'?: string;
    /**
    * The amount of gross sales refunded to customers.
    */
    'grossSalesRefundAmount'?: string;
    /**
    * The transaction Unique key，you can use this field to determine whether it is repeated.
    */
    'id'?: string;
    /**
    * ISR refers to Mexican income tax that TikTok Shop is required to withhold from your earnings and remit to the tax authority.
    */
    'isrIncomeTaxAmount'?: string;
    /**
    * IVA refers to Mexican VAT that TikTok Shop is required to withhold on your taxable products and remit to the tax authority. 
    */
    'ivaVatAmount'?: string;
    /**
    * Used for US, UK L2L sellers only. The total revenue with any applicable seller discounts deducted.   net_sales_amount = gross_sales_amount + gross_sales_refund_amount + seller_discount_amount + seller_discount_refund_amount
    */
    'netSalesAmount'?: string;
    /**
    * The create time of the order
    */
    'orderCreateTime'?: number;
    /**
    * The unique ID of the order，One of Order ID and Adjustment ID must not be 0
    */
    'orderId'?: string;
    /**
    * The creator\'s personal income tax withholding amount. Sellers are required to handle the filing and remittance of withheld taxes based on their region\'s tax regulations.
    */
    'pitAmount'?: string;
    /**
    * Only for UK. Rate * (customer_payment_amount - customer_order_refund_amount + platform_discount_amount - platform_discount_refund_amount)
    */
    'platformCommissionAmount'?: string;
    /**
    * This category represents promotions paid for by platform, such as coupons and other campaign discounts organised by platform
    */
    'platformDiscountAmount'?: string;
    /**
    * If a refund happens, the platform discount that was applied will be regarded as invalid and deducted from the final settlement
    */
    'platformDiscountRefundAmount'?: string;
    /**
    * The refunded amount subsidized by the platform
    */
    'platformRefundSubsidyAmount'?: string;
    /**
    * The shipping fee discount provided in accordance with a campaign policy
    */
    'platformShippingFeeDiscountAmount'?: string;
    /**
    * From Aug 26, 2024 to Dec 31, 2024, TikTok shop will provide additional logistics incentives for sellers that have registered for the co-funded free shipping program. Negative amounts mean a clawback of the incentives given.
    */
    'promoShippingIncentiveAmount'?: string;
    /**
    * Only for US. The referral fee is an amount charged for processing successful orders in TikTok Shop. (For orders placed before April 3, 2023, 0:00 AM UTC-04:00, New York Time)$0.3 + rate %* (Customer payment amount + Platform discount amount - (Customer order refund amount + Platform discount refund amount))
    */
    'referralFeeAmount'?: string;
    /**
    * Refunds or returns will incur a 20% refund administration fee deduction from the total refunded referral fee amount
    */
    'refundAdministrationFeeAmount'?: string;
    /**
    * Also named TikTok Shop shipping incentive refund.The shipping fee incentive amount deducted from the seller\'s account for refunded orders due to a seller fault.
    */
    'refundShippingCostDiscountAmount'?: string;
    /**
    * retail_delivery_fee_amount = retail_delivery_fee_payment_amount+retail_delivery_fee_refund_amount
    */
    'retailDeliveryFeeAmount'?: string;
    /**
    * The retail delivery fee is collected and remitted by the TikTok Shop. It applies to all deliveries by motor vehicle to a location in Colorado with at least one item of tangible personal property that is subject to state sales or use tax.
    */
    'retailDeliveryFeePaymentAmount'?: string;
    /**
    * Subsidy paid by TikTok Shop for losses due to return or refund request rules or other issues
    */
    'retailDeliveryFeeRefundAmount'?: string;
    /**
    * The shipping fee paid by the seller for the delivery of returns。
    */
    'returnShippingFeeAmount'?: string;
    /**
    * Total revenue amount, revenue_amont = customer_payment_amount+platform_discount_amount+platform_discount_refund_amount+customer_order_refund_amount+shipping_fee_subsidy_amount
    */
    'revenueAmount'?: string;
    /**
    * Only for US. The final sales tax collected from the buyer for the product and delivery. sales_tax_amount = sales_tax_refund_amount-sales_tax_payment_amount
    */
    'salesTaxAmount'?: string;
    /**
    * Only for US. The sales tax collected from the buyer for the product and delivery.
    */
    'salesTaxPaymentAmount'?: string;
    /**
    * Only for US. If the purchase is refunded, the sales tax that was applied will be returned to the buyer.
    */
    'salesTaxRefundAmount'?: string;
    /**
    * Seller discounts are the total amount of discounts funded by the seller. These include: i) Discounts funded by the seller through the seller\'s promotions (Product Discount, Flash Deal, Buy More Save More, Voucher and Bundle Deal) ii) Seller\'s portion of a co-funded voucher discount during the seller\'s participation in co-funding campaigns iii) Discounts funded by the seller during a campaign
    */
    'sellerDiscountAmount'?: string;
    /**
    * Discounts refunded to the seller.
    */
    'sellerDiscountRefundAmount'?: string;
    /**
    * The total settlement amount of the order
    */
    'settlementAmount'?: string;
    /**
    * Only for UK and US Local Sellers, represent the total fees related to shipping. Equates to fbm_shipping_cost_amount + fbt_shipping_cost_amount + signature_confirmation_fee_amount + customer_paid_shipping_fee_amount + customer_paid_shipping_fee_refund_amount + shipping_cost_discount_amount + refund_shipping_cost_discount_amount + shipping_fee_subsidy_amount + return_shipping_fee_amount + shipping_insurance_amount + customer_shipping_fee_offset_amount + fbt_fulfillment_fee_amount + promo_shipping_incentive_amount
    */
    'shippingCostAmount'?: string;
    /**
    * Also named TikTok Shop shipping incentive.The shipping fee incentive provided by TikTok Shop.
    */
    'shippingCostDiscountAmount'?: string;
    /**
    * shipping_fee_amount = actual_shipping_fee_amount - platform_shipping_fee_discount_amount + acutal_return_shipping_fee_amount + signature_confirmation_fee_amount
    */
    'shippingFeeAmount'?: string;
    /**
    * The shipping fee subsidy provided by TikTok Shop for orders fulfilled by the seller themselves。
    */
    'shippingFeeSubsidyAmount'?: string;
    /**
    * The shipping insurance fee incurred by the seller for purchasing additional TikTok shipping insurance services.
    */
    'shippingInsuranceFeeAmount'?: string;
    /**
    * The fee incurred for packages requiring signature confirmation services.
    */
    'signatureConfirmationFeeAmount'?: string;
    /**
    * Only for US. Transaction Fee is a service fee charged for processing successful orders in TikTok Shop. Transaction Fee is a non-refundable fee, and is charged at 0.3 USD plus a percentage of the customer paid price per transaction. (For orders placed before April 3, 2023, 0:00 AM UTC-04:00, New York Time)$0.3 + rate %* Customer payment amount
    */
    'transactionFeeAmount'?: string;
    /**
    * If the transaction is a regular order, we will return ORDER. If the transaction is an adjustment, we will return the adjustment type.  Adjustment type possible values: - SHIPPING_FEE_ADJUSTMENT：Adjustment when there are differences or mistakes with the shipping fee paid by the seller. - SHIPPING_FEE_COMPENSATION：Compensation given to sellers due to differences between the actual shipping fee and the pre-paid shipping fee. - CHARGE_BACK：The charge returned to a payment card after a customer has successfully disputed an item on their account statement or transactions report. - CUSTOMER_SERVICE_COMPENSATION：This is extra compensation or compensation paid to a customer after the after-sales period by customer service. - PROMOTION_ADJUSTMENT：Adjustment when a seller takes part in a platform promotion and there are differences between the promotion price and the actual amount paid by the seller. - PLATFORM_COMPENSATION：Compensation paid to the seller as a result of the seller successfully appealing customer dispute. - PLATFORM_PENALTY：After identifying a violation of platform policies, we have deducted the corresponding penalty amount from the seller\'s account in accordance with our policies. Please see the email notification that was sent to you. - SAMPLE_SHIPPING_FEE：Fees are charged for sending samples using the platform logistics provider. - LOGISTICS_REIMBURSEMENT: Reimbursement paid by TikTok Shop for losses due to logistics-related issues - PLATFORM_REIMBURSEMENT   - Platform refund without return: Order has been processed using the TikTok refund without return policy. Since the reason for this refund is not the responsibility of seller, TikTok has borne the refund amount to the customer. - DEDUCTIONS_INCURRED_BY_SELLER   -When a customer is unhappy with their experience due to an issue that is the seller\'s responsibility, or Order was found to meet the definition of fraud, and to either have one of the following issues, or other similar issues: empty package sent to customer, items sent not matching those on the product display page, items sent of lower value than advertised. The seller is responsible for costs related to this issue. - SHIPPING_FEE_REBATE：The shipping fee rebate given to the seller as part of their participation in a platform campaign. - PLATFORM_COMMISSION_ADJUSTMENT：Adjustment when there are differences in the platform commission paid by the seller. - PLATFORM_COMMISSION_COMPENSATION：Compensation paid to the seller when there are differences in the platform commission paid by the seller. - OTHER_ADJUSTMENT：Adjustments for other reasons. - FBT_WAREHOUSE_SERVICE_FEE: The amount charged by TikTok Fulfillment Portal (Pipak) for warehousing-related bills incurred by the seller under the Fulfilled by TikTok (FBT) service. - GMV_PAYMENT_FOR_ADS: The amount used to pay for your advertisement if you open \'pay ads with shop GMV\' or pay for TikTok Promote ads orders. - REBATE:  A discount on referral fees offered by TikTok Shop to eligible sellers.
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "actualReturnShippingFeeAmount",
            "baseName": "actual_return_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "actualShippingFeeAmount",
            "baseName": "actual_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "adjustmentAmount",
            "baseName": "adjustment_amount",
            "type": "string"
        },
        {
            "name": "adjustmentId",
            "baseName": "adjustment_id",
            "type": "string"
        },
        {
            "name": "adjustmentOrderId",
            "baseName": "adjustment_order_id",
            "type": "string"
        },
        {
            "name": "affiliateAdsCommissionAmount",
            "baseName": "affiliate_ads_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionAmount",
            "baseName": "affiliate_commission_amount",
            "type": "string"
        },
        {
            "name": "affiliateCommissionBeforePit",
            "baseName": "affiliate_commission_before_pit",
            "type": "string"
        },
        {
            "name": "affiliatePartnerCommissionAmount",
            "baseName": "affiliate_partner_commission_amount",
            "type": "string"
        },
        {
            "name": "afterSellerDiscountsSubtotalAmount",
            "baseName": "after_seller_discounts_subtotal_amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "customerOrderRefundAmount",
            "baseName": "customer_order_refund_amount",
            "type": "string"
        },
        {
            "name": "customerPaidShippingFeeAmount",
            "baseName": "customer_paid_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "customerPaidShippingFeeRefundAmount",
            "baseName": "customer_paid_shipping_fee_refund_amount",
            "type": "string"
        },
        {
            "name": "customerPaymentAmount",
            "baseName": "customer_payment_amount",
            "type": "string"
        },
        {
            "name": "customerRefundAmount",
            "baseName": "customer_refund_amount",
            "type": "string"
        },
        {
            "name": "customerShippingFeeAmount",
            "baseName": "customer_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "customerShippingFeeOffsetAmount",
            "baseName": "customer_shipping_fee_offset_amount",
            "type": "string"
        },
        {
            "name": "fbmShippingCostAmount",
            "baseName": "fbm_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "fbtFulfillmentFeeAmount",
            "baseName": "fbt_fulfillment_fee_amount",
            "type": "string"
        },
        {
            "name": "fbtShippingCostAmount",
            "baseName": "fbt_shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "feeAmount",
            "baseName": "fee_amount",
            "type": "string"
        },
        {
            "name": "grossSalesAmount",
            "baseName": "gross_sales_amount",
            "type": "string"
        },
        {
            "name": "grossSalesRefundAmount",
            "baseName": "gross_sales_refund_amount",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isrIncomeTaxAmount",
            "baseName": "isr_income_tax_amount",
            "type": "string"
        },
        {
            "name": "ivaVatAmount",
            "baseName": "iva_vat_amount",
            "type": "string"
        },
        {
            "name": "netSalesAmount",
            "baseName": "net_sales_amount",
            "type": "string"
        },
        {
            "name": "orderCreateTime",
            "baseName": "order_create_time",
            "type": "number"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "pitAmount",
            "baseName": "pit_amount",
            "type": "string"
        },
        {
            "name": "platformCommissionAmount",
            "baseName": "platform_commission_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountAmount",
            "baseName": "platform_discount_amount",
            "type": "string"
        },
        {
            "name": "platformDiscountRefundAmount",
            "baseName": "platform_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "platformRefundSubsidyAmount",
            "baseName": "platform_refund_subsidy_amount",
            "type": "string"
        },
        {
            "name": "platformShippingFeeDiscountAmount",
            "baseName": "platform_shipping_fee_discount_amount",
            "type": "string"
        },
        {
            "name": "promoShippingIncentiveAmount",
            "baseName": "promo_shipping_incentive_amount",
            "type": "string"
        },
        {
            "name": "referralFeeAmount",
            "baseName": "referral_fee_amount",
            "type": "string"
        },
        {
            "name": "refundAdministrationFeeAmount",
            "baseName": "refund_administration_fee_amount",
            "type": "string"
        },
        {
            "name": "refundShippingCostDiscountAmount",
            "baseName": "refund_shipping_cost_discount_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeAmount",
            "baseName": "retail_delivery_fee_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeePaymentAmount",
            "baseName": "retail_delivery_fee_payment_amount",
            "type": "string"
        },
        {
            "name": "retailDeliveryFeeRefundAmount",
            "baseName": "retail_delivery_fee_refund_amount",
            "type": "string"
        },
        {
            "name": "returnShippingFeeAmount",
            "baseName": "return_shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "salesTaxAmount",
            "baseName": "sales_tax_amount",
            "type": "string"
        },
        {
            "name": "salesTaxPaymentAmount",
            "baseName": "sales_tax_payment_amount",
            "type": "string"
        },
        {
            "name": "salesTaxRefundAmount",
            "baseName": "sales_tax_refund_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountAmount",
            "baseName": "seller_discount_amount",
            "type": "string"
        },
        {
            "name": "sellerDiscountRefundAmount",
            "baseName": "seller_discount_refund_amount",
            "type": "string"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "shippingCostDiscountAmount",
            "baseName": "shipping_cost_discount_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeAmount",
            "baseName": "shipping_fee_amount",
            "type": "string"
        },
        {
            "name": "shippingFeeSubsidyAmount",
            "baseName": "shipping_fee_subsidy_amount",
            "type": "string"
        },
        {
            "name": "shippingInsuranceFeeAmount",
            "baseName": "shipping_insurance_fee_amount",
            "type": "string"
        },
        {
            "name": "signatureConfirmationFeeAmount",
            "baseName": "signature_confirmation_fee_amount",
            "type": "string"
        },
        {
            "name": "transactionFeeAmount",
            "baseName": "transaction_fee_amount",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetStatementTransactionsResponseDataStatementTransactions.attributeTypeMap;
    }
}

