/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202309GetPaymentsResponseDataPayments } from './GetPaymentsResponseDataPayments';

export class Finance202309GetPaymentsResponseData {
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The list of payments that meet the query conditions.
    */
    'payments'?: Array<Finance202309GetPaymentsResponseDataPayments>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "payments",
            "baseName": "payments",
            "type": "Array<Finance202309GetPaymentsResponseDataPayments>"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetPaymentsResponseData.attributeTypeMap;
    }
}

