/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202309GetStatementsResponseDataStatements {
    /**
    * The adjustment amount. For more details about the reason for adjustment, refer to the [Get Statement Transactions API](https://partner.tiktokshop.com/docv2/page/650a6749defece02be67da87).
    */
    'adjustmentAmount'?: string;
    /**
    * The currency code in ISO 4217 format.
    */
    'currency'?: string;
    /**
    * The fees charged by TikTok Shop at the time of order settlement. An order is deemed settled a certain number of days after delivery (varies by region) if no returns or refunds are pending.  **Note**: For UK and US, shipping-related costs are excluded.
    */
    'feeAmount'?: string;
    /**
    * The statement ID.
    */
    'id'?: string;
    /**
    * The final revenue amount after seller discounts are deducted. Applicable only for local sellers in UK and US. 
    */
    'netSalesAmount'?: string;
    /**
    * The payment ID.
    */
    'paymentId'?: string;
    /**
    * The payment status, indicating whether payment has been transferred to the seller\'s bank account. Possible values: - PAID: Payment has been transferred to the seller. - FAILED: Payment transfer failed. - PROCESSING: Payment is currently being processed.
    */
    'paymentStatus'?: string;
    /**
    * The final revenue amount at the time of order settlement.  Applicable for all regions except UK and US.
    */
    'revenueAmount'?: string;
    /**
    * The settlement amount. 
    */
    'settlementAmount'?: string;
    /**
    * The shipping fees. Applicable only for local sellers in UK and US. 
    */
    'shippingCostAmount'?: string;
    /**
    * The time when the statement was generated. Unix timestamp.  Statements are generated daily at 00:00 UTC, and it includes all transactions from the past day. 
    */
    'statementTime'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "adjustmentAmount",
            "baseName": "adjustment_amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "feeAmount",
            "baseName": "fee_amount",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "netSalesAmount",
            "baseName": "net_sales_amount",
            "type": "string"
        },
        {
            "name": "paymentId",
            "baseName": "payment_id",
            "type": "string"
        },
        {
            "name": "paymentStatus",
            "baseName": "payment_status",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "statementTime",
            "baseName": "statement_time",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetStatementsResponseDataStatements.attributeTypeMap;
    }
}

