/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Finance202309GetWithdrawalsResponseDataWithdrawals {
    /**
    * Withdraw amount
    */
    'amount'?: string;
    /**
    * Withdraw create time
    */
    'createTime'?: number;
    /**
    * The three-digit currency code in ISO 4217 format.   
    */
    'currency'?: string;
    /**
    * A unique transaction id generated by TTS for the withdrawal.
    */
    'id'?: string;
    /**
    * The processing status of the withdrawal indicates whether the withdrawal is transferred. Possible values: - PROCESSING：the withdrawal is currently processing. If the withdrawal is successful, the status will transition to PAID. If not, it will be FAILED. - SUCCESS：the withdrawal has been transferred to the Seller - FAILED：the withdrawal failed
    */
    'status'?: string;
    /**
    * WITHDRAW：The action of the seller to receive the settlement amount to the bank card through the action of withdrawal SETTLE：The platform settles the amount to the seller TRANSFER：Platform subsidies or deductions due to platform policies REVERSE：Withdrawal failure due to incorrect bank card 
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "amount",
            "baseName": "amount",
            "type": "string"
        },
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetWithdrawalsResponseDataWithdrawals.attributeTypeMap;
    }
}

