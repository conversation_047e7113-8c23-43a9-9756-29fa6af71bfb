/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Finance202309GetStatementTransactionsResponseDataStatementTransactions } from './GetStatementTransactionsResponseDataStatementTransactions';

export class Finance202309GetStatementTransactionsResponseData {
    /**
    * The total amount calculated by the platform when the order fees need to be changed, the specific adjustment type will be shown in fields type
    */
    'adjustmentAmount'?: string;
    /**
    * The three-digit currency code in ISO 4217 format.   
    */
    'currency'?: string;
    /**
    * The fees calculated by the platform when the order meets the settlement rules (the order is settled 3 to 15 days after it is delivered with no return/refund ongoing). For UK and US Local Sellers, this amount already excludes shipping related costs.
    */
    'feeAmount'?: string;
    /**
    * Used for US, UK L2L sellers only. The total revenue with any applicable seller discounts deducted.   net_sales_amount = gross_sales_amount + gross_sales_refund_amount + seller_discount_amount + seller_discount_refund_amount
    */
    'netSalesAmount'?: string;
    /**
    * Cursor used for searching for more information
    */
    'nextPageToken'?: string;
    /**
    * The total earnings calculated by the platform at the time the order was paid
    */
    'revenueAmount'?: string;
    /**
    * The total settlement amount of the statement settlement_amount = revenue_amount + fee_amount + adjustment_amount For UK and US Local Sellers: settlement_amount=net_sales_amount+shipping_cost_amount+fee_amount + adjustment_amount
    */
    'settlementAmount'?: string;
    /**
    * Only for UK and US Local Sellers, represents the total fees related to shipping. Equates to fbm_shipping_cost_amount + fbt_shipping_cost_amount + signature_confirmation_fee_amount + customer_paid_shipping_fee_amount + customer_paid_shipping_fee_refund_amount + shipping_cost_discount_amount + refund_shipping_cost_discount_amount + shipping_fee_subsidy_amount + return_shipping_fee_amount + shipping_insurance_amount + customer_shipping_fee_offset_amount + fbt_fulfillment_fee_amount + promo_shipping_incentive_amount
    */
    'shippingCostAmount'?: string;
    /**
    * The unique ID of statement
    */
    'statementId'?: string;
    /**
    * Time of statement in UTC time zone
    */
    'statementTime'?: number;
    /**
    * Statement list
    */
    'statementTransactions'?: Array<Finance202309GetStatementTransactionsResponseDataStatementTransactions>;
    /**
    * The number of transaction records
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "adjustmentAmount",
            "baseName": "adjustment_amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "feeAmount",
            "baseName": "fee_amount",
            "type": "string"
        },
        {
            "name": "netSalesAmount",
            "baseName": "net_sales_amount",
            "type": "string"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "revenueAmount",
            "baseName": "revenue_amount",
            "type": "string"
        },
        {
            "name": "settlementAmount",
            "baseName": "settlement_amount",
            "type": "string"
        },
        {
            "name": "shippingCostAmount",
            "baseName": "shipping_cost_amount",
            "type": "string"
        },
        {
            "name": "statementId",
            "baseName": "statement_id",
            "type": "string"
        },
        {
            "name": "statementTime",
            "baseName": "statement_time",
            "type": "number"
        },
        {
            "name": "statementTransactions",
            "baseName": "statement_transactions",
            "type": "Array<Finance202309GetStatementTransactionsResponseDataStatementTransactions>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Finance202309GetStatementTransactionsResponseData.attributeTypeMap;
    }
}

