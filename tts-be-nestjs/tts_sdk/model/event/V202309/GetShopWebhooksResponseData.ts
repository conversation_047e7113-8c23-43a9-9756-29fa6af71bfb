/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Event202309GetShopWebhooksResponseDataWebhooks } from './GetShopWebhooksResponseDataWebhooks';

export class Event202309GetShopWebhooksResponseData {
    /**
    * The total number of webhooks returned.
    */
    'totalCount'?: number;
    /**
    * The list of webhooks configured for the shop.
    */
    'webhooks'?: Array<Event202309GetShopWebhooksResponseDataWebhooks>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        },
        {
            "name": "webhooks",
            "baseName": "webhooks",
            "type": "Array<Event202309GetShopWebhooksResponseDataWebhooks>"
        }    ];

    static getAttributeTypeMap() {
        return Event202309GetShopWebhooksResponseData.attributeTypeMap;
    }
}

