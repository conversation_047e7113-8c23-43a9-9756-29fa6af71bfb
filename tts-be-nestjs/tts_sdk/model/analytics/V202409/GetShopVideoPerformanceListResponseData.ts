/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202409GetShopVideoPerformanceListResponseDataVideos } from './GetShopVideoPerformanceListResponseDataVideos';

export class Analytics202409GetShopVideoPerformanceListResponseData {
    /**
    * Latest date in local timezone where data is ready (ISO 8601 format).
    */
    'latestAvailableDate'?: string;
    /**
    * Page token for the next page request.
    */
    'nextPageToken'?: string;
    /**
    * Total number of videos.
    */
    'totalCount'?: number;
    /**
    * List of video performance metrics.
    */
    'videos'?: Array<Analytics202409GetShopVideoPerformanceListResponseDataVideos>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "latestAvailableDate",
            "baseName": "latest_available_date",
            "type": "string"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        },
        {
            "name": "videos",
            "baseName": "videos",
            "type": "Array<Analytics202409GetShopVideoPerformanceListResponseDataVideos>"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoPerformanceListResponseData.attributeTypeMap;
    }
}

