/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Analytics202409GetShopVideoPerformanceDetailsResponseDataEngagementData {
    /**
    * Total number of video comments since the video was published.
    */
    'totalComments'?: number;
    /**
    * Total number of video likes since the video was published.
    */
    'totalLikes'?: number;
    /**
    * Total number of video shares since the video was published.
    */
    'totalShares'?: number;
    /**
    * Total number of video views since the video was published.
    */
    'totalViews'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "totalComments",
            "baseName": "total_comments",
            "type": "number"
        },
        {
            "name": "totalLikes",
            "baseName": "total_likes",
            "type": "number"
        },
        {
            "name": "totalShares",
            "baseName": "total_shares",
            "type": "number"
        },
        {
            "name": "totalViews",
            "baseName": "total_views",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoPerformanceDetailsResponseDataEngagementData.attributeTypeMap;
    }
}

