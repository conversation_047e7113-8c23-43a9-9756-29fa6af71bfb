/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202409GetShopVideoProductPerformanceListResponseDataProductsGmv } from './GetShopVideoProductPerformanceListResponseDataProductsGmv';

export class Analytics202409GetShopVideoProductPerformanceListResponseDataProducts {
    /**
    * Average number of buyers per day for the current product.
    */
    'dailyAvgBuyers'?: string;
    'gmv'?: Analytics202409GetShopVideoProductPerformanceListResponseDataProductsGmv;
    /**
    * Product ID
    */
    'id'?: string;
    /**
    * Product Name
    */
    'name'?: string;
    /**
    * Number of units sold for the current product.
    */
    'unitsSold'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dailyAvgBuyers",
            "baseName": "daily_avg_buyers",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202409GetShopVideoProductPerformanceListResponseDataProductsGmv"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoProductPerformanceListResponseDataProducts.attributeTypeMap;
    }
}

