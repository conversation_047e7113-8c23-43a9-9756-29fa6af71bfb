/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202409GetShopVideoPerformanceDetailsResponseDataEngagementData } from './GetShopVideoPerformanceDetailsResponseDataEngagementData';
import { Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformance } from './GetShopVideoPerformanceDetailsResponseDataPerformance';

export class Analytics202409GetShopVideoPerformanceDetailsResponseData {
    'engagementData'?: Analytics202409GetShopVideoPerformanceDetailsResponseDataEngagementData;
    /**
    * Latest date in local timezone where data is ready (ISO 8601 format).
    */
    'latestAvailableDate'?: string;
    'performance'?: Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformance;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "engagementData",
            "baseName": "engagement_data",
            "type": "Analytics202409GetShopVideoPerformanceDetailsResponseDataEngagementData"
        },
        {
            "name": "latestAvailableDate",
            "baseName": "latest_available_date",
            "type": "string"
        },
        {
            "name": "performance",
            "baseName": "performance",
            "type": "Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformance"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoPerformanceDetailsResponseData.attributeTypeMap;
    }
}

