/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervalsGmv } from './GetShopVideoPerformanceDetailsResponseDataPerformanceIntervalsGmv';

export class Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals {
    /**
    * Ratio of the number of product clicks compared to number of video views in raw decimal format. To calculate the percentage, multiple it by 100%. Example: 0.0528 <=> 5.28%
    */
    'clickThroughRate'?: string;
    /**
    * Average number of buyers per day from the video during the selected time range.
    */
    'dailyAvgBuyers'?: string;
    /**
    * End date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
    */
    'endDate'?: string;
    'gmv'?: Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervalsGmv;
    /**
    * Start date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
    */
    'startDate'?: string;
    /**
    * Total number of video views during the selected time range.
    */
    'views'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "clickThroughRate",
            "baseName": "click_through_rate",
            "type": "string"
        },
        {
            "name": "dailyAvgBuyers",
            "baseName": "daily_avg_buyers",
            "type": "string"
        },
        {
            "name": "endDate",
            "baseName": "end_date",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervalsGmv"
        },
        {
            "name": "startDate",
            "baseName": "start_date",
            "type": "string"
        },
        {
            "name": "views",
            "baseName": "views",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals.attributeTypeMap;
    }
}

