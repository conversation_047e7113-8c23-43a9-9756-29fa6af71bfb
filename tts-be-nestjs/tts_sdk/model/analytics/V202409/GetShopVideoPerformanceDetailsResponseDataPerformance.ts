/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceComparisonIntervals } from './GetShopVideoPerformanceDetailsResponseDataPerformanceComparisonIntervals';
import { Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals } from './GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals';

export class Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformance {
    /**
    * Same structure as \"intervals\" It contains data for the previous time range with the same range length and granularity of the current time range Example, if current time range (represented in start_time_ge and end_time_lt) is from 2024-09-01 to 2024-09-08) with granularity \"ALL\", the previous_intervals will contain data from 2024-08-25 to 2024-09-01 with granularity \"ALL\"
    */
    'comparisonIntervals'?: Array<Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceComparisonIntervals>;
    /**
    * Interval data for the requested time range. The time range of each interval is determined by the granularity.
    */
    'intervals'?: Array<Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals>;
    /**
    * Date and time video was posted (ISO 8601 format)
    */
    'videoPostTime'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "comparisonIntervals",
            "baseName": "comparison_intervals",
            "type": "Array<Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceComparisonIntervals>"
        },
        {
            "name": "intervals",
            "baseName": "intervals",
            "type": "Array<Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformanceIntervals>"
        },
        {
            "name": "videoPostTime",
            "baseName": "video_post_time",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202409GetShopVideoPerformanceDetailsResponseDataPerformance.attributeTypeMap;
    }
}

