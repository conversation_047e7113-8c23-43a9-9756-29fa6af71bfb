/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmv } from './GetShopSKUPerformanceResponseDataPerformanceIntervalsGmv';
import { Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmvBreakdown } from './GetShopSKUPerformanceResponseDataPerformanceIntervalsGmvBreakdown';
import { Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsUnitsSoldBreakdown } from './GetShopSKUPerformanceResponseDataPerformanceIntervalsUnitsSoldBreakdown';

export class Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervals {
    /**
    * End date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
    */
    'endDate'?: string;
    'gmv'?: Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmv;
    /**
    * GMV breakdowns for the SKU.
    */
    'gmvBreakdown'?: Array<Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmvBreakdown>;
    /**
    * Total (sum of all) orders for the SKU.
    */
    'skuOrders'?: number;
    /**
    * Start date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
    */
    'startDate'?: string;
    /**
    * Number of units sold for the SKU.
    */
    'unitsSold'?: number;
    /**
    * Unit sold breakdowns.
    */
    'unitsSoldBreakdown'?: Array<Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsUnitsSoldBreakdown>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "endDate",
            "baseName": "end_date",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmv"
        },
        {
            "name": "gmvBreakdown",
            "baseName": "gmv_breakdown",
            "type": "Array<Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsGmvBreakdown>"
        },
        {
            "name": "skuOrders",
            "baseName": "sku_orders",
            "type": "number"
        },
        {
            "name": "startDate",
            "baseName": "start_date",
            "type": "string"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        },
        {
            "name": "unitsSoldBreakdown",
            "baseName": "units_sold_breakdown",
            "type": "Array<Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervalsUnitsSoldBreakdown>"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202406GetShopSKUPerformanceResponseDataPerformanceIntervals.attributeTypeMap;
    }
}

