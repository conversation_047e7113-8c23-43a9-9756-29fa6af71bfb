/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202406GetShopSKUPerformanceListResponseDataSkus } from './GetShopSKUPerformanceListResponseDataSkus';

export class Analytics202406GetShopSKUPerformanceListResponseData {
    /**
    * Latest date in local timezone where data is ready (ISO 8601 format). 
    */
    'latestAvailableDate'?: string;
    /**
    * Page token for the next page request.
    */
    'nextPageToken'?: string;
    /**
    * List of SKU performance metrics.
    */
    'skus'?: Array<Analytics202406GetShopSKUPerformanceListResponseDataSkus>;
    /**
    * Total number of SKUs.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "latestAvailableDate",
            "baseName": "latest_available_date",
            "type": "string"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Analytics202406GetShopSKUPerformanceListResponseDataSkus>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202406GetShopSKUPerformanceListResponseData.attributeTypeMap;
    }
}

