/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202406GetShopSKUPerformanceListResponseDataSkusGmv } from './GetShopSKUPerformanceListResponseDataSkusGmv';

export class Analytics202406GetShopSKUPerformanceListResponseDataSkus {
    'gmv'?: Analytics202406GetShopSKUPerformanceListResponseDataSkusGmv;
    /**
    * SKU ID
    */
    'id'?: number;
    /**
    * Product ID
    */
    'productId'?: number;
    /**
    * Total (sum of all) orders for a SKU
    */
    'skuOrders'?: number;
    /**
    * Number of units sold for a SKU
    */
    'unitsSold'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202406GetShopSKUPerformanceListResponseDataSkusGmv"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "number"
        },
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "number"
        },
        {
            "name": "skuOrders",
            "baseName": "sku_orders",
            "type": "number"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202406GetShopSKUPerformanceListResponseDataSkus.attributeTypeMap;
    }
}

