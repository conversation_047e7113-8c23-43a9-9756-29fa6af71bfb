/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervals } from './GetShopPerformanceResponseDataPerformanceComparisonIntervals';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervals } from './GetShopPerformanceResponseDataPerformanceIntervals';

export class Analytics202405GetShopPerformanceResponseDataPerformance {
    /**
    * Same structure as \"intervals\" It contains data for the previous time range with the same range length and granularity of the current time range Example, if current time range (represented in start_time_ge and end_time_lt) is from 2024-04-01 to 2024-04-08) with granularity \"ALL\", the previous_intervals will contain data from 2024-03-25 to 2024-04-01 with granularity \"ALL\".
    */
    'comparisonIntervals'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervals>;
    /**
    * Interval data for the requested time range. The time range of each interval is determined by the granularity.
    */
    'intervals'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervals>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "comparisonIntervals",
            "baseName": "comparison_intervals",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervals>"
        },
        {
            "name": "intervals",
            "baseName": "intervals",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervals>"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopPerformanceResponseDataPerformance.attributeTypeMap;
    }
}

