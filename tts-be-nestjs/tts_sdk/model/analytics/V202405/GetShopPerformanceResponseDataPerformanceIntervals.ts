/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgOrderValue } from './GetShopPerformanceResponseDataPerformanceIntervalsAvgOrderValue';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgProductPageVisitorBreakdowns } from './GetShopPerformanceResponseDataPerformanceIntervalsAvgProductPageVisitorBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsBuyerBreakdowns } from './GetShopPerformanceResponseDataPerformanceIntervalsBuyerBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmv } from './GetShopPerformanceResponseDataPerformanceIntervalsGmv';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmvBreakdowns } from './GetShopPerformanceResponseDataPerformanceIntervalsGmvBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductImpressionBreakdowns } from './GetShopPerformanceResponseDataPerformanceIntervalsProductImpressionBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductPageViewBreakdowns } from './GetShopPerformanceResponseDataPerformanceIntervalsProductPageViewBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsRefunds } from './GetShopPerformanceResponseDataPerformanceIntervalsRefunds';

export class Analytics202405GetShopPerformanceResponseDataPerformanceIntervals {
    'avgOrderValue'?: Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgOrderValue;
    /**
    * Average daily product page visitor breakdowns.
    */
    'avgProductPageVisitorBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgProductPageVisitorBreakdowns>;
    /**
    * Average number of unique visitors per day for the shop.
    */
    'avgProductPageVisitors'?: number;
    /**
    * Buyer breakdowns.
    */
    'buyerBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsBuyerBreakdowns>;
    /**
    * Number of unique buyers for the shop.
    */
    'buyers'?: number;
    /**
    * Total number of items that were canceled or returned for the shop.
    */
    'cancellationsAndReturns'?: number;
    /**
    * End date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
    */
    'endDate'?: string;
    'gmv'?: Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmv;
    /**
    * GMV breakdowns for the shop.
    */
    'gmvBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmvBreakdowns>;
    /**
    * Total (sum of all) orders for a shop.
    */
    'orders'?: number;
    /**
    * Product impression breakdowns.
    */
    'productImpressionBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductImpressionBreakdowns>;
    /**
    * Total product impressions for the shop.
    */
    'productImpressions'?: number;
    /**
    * Product page view breakdowns.
    */
    'productPageViewBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductPageViewBreakdowns>;
    /**
    * Total product detail page views for the shop.
    */
    'productPageViews'?: number;
    'refunds'?: Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsRefunds;
    /**
    * Number of Stock Keeping Units (SKUs) in orders placed.
    */
    'skuOrders'?: number;
    /**
    * Start date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
    */
    'startDate'?: string;
    /**
    * Number of units sold for the shop.
    */
    'unitsSold'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "avgOrderValue",
            "baseName": "avg_order_value",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgOrderValue"
        },
        {
            "name": "avgProductPageVisitorBreakdowns",
            "baseName": "avg_product_page_visitor_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsAvgProductPageVisitorBreakdowns>"
        },
        {
            "name": "avgProductPageVisitors",
            "baseName": "avg_product_page_visitors",
            "type": "number"
        },
        {
            "name": "buyerBreakdowns",
            "baseName": "buyer_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsBuyerBreakdowns>"
        },
        {
            "name": "buyers",
            "baseName": "buyers",
            "type": "number"
        },
        {
            "name": "cancellationsAndReturns",
            "baseName": "cancellations_and_returns",
            "type": "number"
        },
        {
            "name": "endDate",
            "baseName": "end_date",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmv"
        },
        {
            "name": "gmvBreakdowns",
            "baseName": "gmv_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsGmvBreakdowns>"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "number"
        },
        {
            "name": "productImpressionBreakdowns",
            "baseName": "product_impression_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductImpressionBreakdowns>"
        },
        {
            "name": "productImpressions",
            "baseName": "product_impressions",
            "type": "number"
        },
        {
            "name": "productPageViewBreakdowns",
            "baseName": "product_page_view_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsProductPageViewBreakdowns>"
        },
        {
            "name": "productPageViews",
            "baseName": "product_page_views",
            "type": "number"
        },
        {
            "name": "refunds",
            "baseName": "refunds",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceIntervalsRefunds"
        },
        {
            "name": "skuOrders",
            "baseName": "sku_orders",
            "type": "number"
        },
        {
            "name": "startDate",
            "baseName": "start_date",
            "type": "string"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopPerformanceResponseDataPerformanceIntervals.attributeTypeMap;
    }
}

