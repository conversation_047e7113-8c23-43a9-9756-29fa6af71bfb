/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgOrderValue } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgOrderValue';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgProductPageVisitorBreakdowns } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgProductPageVisitorBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsBuyerBreakdowns } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsBuyerBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmv } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmv';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductImpressionBreakdowns } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductImpressionBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductPageViewBreakdowns } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductPageViewBreakdowns';
import { Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsRefunds } from './GetShopPerformanceResponseDataPerformanceComparisonIntervalsRefunds';

export class Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervals {
    'avgOrderValue'?: Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgOrderValue;
    /**
    * Average daily product page visitor breakdowns.
    */
    'avgProductPageVisitorBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgProductPageVisitorBreakdowns>;
    /**
    * Average number of unique visitors per day for the shop.
    */
    'avgProductPageVisitors'?: number;
    /**
    * Buyer breakdowns.
    */
    'buyerBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsBuyerBreakdowns>;
    /**
    * Number of unique buyers for the shop.
    */
    'buyers'?: number;
    /**
    * Total number of items that were canceled or returned for the shop.
    */
    'cancellationsAndReturns'?: number;
    /**
    * End date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
    */
    'endDate'?: string;
    'gmv'?: Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmv;
    /**
    * GMV breakdowns for the shop.
    */
    'gmvBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns>;
    /**
    * Total (sum of all) orders for the shop.
    */
    'orders'?: number;
    /**
    * Product impression breakdowns.
    */
    'productImpressionBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductImpressionBreakdowns>;
    /**
    * Total product impressions for the shop.
    */
    'productImpressions'?: number;
    /**
    * Product page view breakdowns.
    */
    'productPageViewBreakdowns'?: Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductPageViewBreakdowns>;
    /**
    * Total product detail page views for the shop.
    */
    'productPageViews'?: number;
    'refunds'?: Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsRefunds;
    /**
    * Number of SKUs in orders placed for the shop.
    */
    'skuOrders'?: number;
    /**
    * Start date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
    */
    'startDate'?: string;
    /**
    * Number of units sold for the shop.
    */
    'unitsSold'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "avgOrderValue",
            "baseName": "avg_order_value",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgOrderValue"
        },
        {
            "name": "avgProductPageVisitorBreakdowns",
            "baseName": "avg_product_page_visitor_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsAvgProductPageVisitorBreakdowns>"
        },
        {
            "name": "avgProductPageVisitors",
            "baseName": "avg_product_page_visitors",
            "type": "number"
        },
        {
            "name": "buyerBreakdowns",
            "baseName": "buyer_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsBuyerBreakdowns>"
        },
        {
            "name": "buyers",
            "baseName": "buyers",
            "type": "number"
        },
        {
            "name": "cancellationsAndReturns",
            "baseName": "cancellations_and_returns",
            "type": "number"
        },
        {
            "name": "endDate",
            "baseName": "end_date",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmv"
        },
        {
            "name": "gmvBreakdowns",
            "baseName": "gmv_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns>"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "number"
        },
        {
            "name": "productImpressionBreakdowns",
            "baseName": "product_impression_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductImpressionBreakdowns>"
        },
        {
            "name": "productImpressions",
            "baseName": "product_impressions",
            "type": "number"
        },
        {
            "name": "productPageViewBreakdowns",
            "baseName": "product_page_view_breakdowns",
            "type": "Array<Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsProductPageViewBreakdowns>"
        },
        {
            "name": "productPageViews",
            "baseName": "product_page_views",
            "type": "number"
        },
        {
            "name": "refunds",
            "baseName": "refunds",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervalsRefunds"
        },
        {
            "name": "skuOrders",
            "baseName": "sku_orders",
            "type": "number"
        },
        {
            "name": "startDate",
            "baseName": "start_date",
            "type": "string"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopPerformanceResponseDataPerformanceComparisonIntervals.attributeTypeMap;
    }
}

