/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202405GetShopPerformanceResponseDataPerformance } from './GetShopPerformanceResponseDataPerformance';

export class Analytics202405GetShopPerformanceResponseData {
    /**
    * Latest date in local timezone where data is ready (ISO 8601 format).
    */
    'latestAvailableDate'?: string;
    'performance'?: Analytics202405GetShopPerformanceResponseDataPerformance;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "latestAvailableDate",
            "baseName": "latest_available_date",
            "type": "string"
        },
        {
            "name": "performance",
            "baseName": "performance",
            "type": "Analytics202405GetShopPerformanceResponseDataPerformance"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopPerformanceResponseData.attributeTypeMap;
    }
}

