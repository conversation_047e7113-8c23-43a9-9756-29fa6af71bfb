/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsAvgPageVisitorBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsAvgPageVisitorBreakdowns';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsClickThroughRateBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsClickThroughRateBreakdowns';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmv } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmv';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsImpressionBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsImpressionBreakdowns';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsPageViewBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsPageViewBreakdowns';
import { Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsUnitSoldBreakdowns } from './GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsUnitSoldBreakdowns';

export class Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervals {
    /**
    * Average daily product page visitor breakdowns.
    */
    'avgPageVisitorBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsAvgPageVisitorBreakdowns>;
    /**
    * Average number of unique visitors per day for the product.
    */
    'avgPageVisitors'?: number;
    /**
    * Ratio of the number of product clicks compared to number of product impressions in raw decimal format. To calculate the percentage, multiple it by 100%. Example: 0.0528 <=> 5.28%
    */
    'clickThroughRate'?: string;
    /**
    * Click through rate breakdowns.
    */
    'clickThroughRateBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsClickThroughRateBreakdowns>;
    /**
    * End date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
    */
    'endDate'?: string;
    'gmv'?: Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmv;
    /**
    * GMV breakdowns for the product.
    */
    'gmvBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns>;
    /**
    * Impression breakdowns.
    */
    'impressionBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsImpressionBreakdowns>;
    /**
    * Total impressions for the product.
    */
    'impressions'?: number;
    /**
    * Total (sum of all) orders for the product.
    */
    'orders'?: number;
    /**
    * Page view breakdowns.
    */
    'pageViewBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsPageViewBreakdowns>;
    /**
    * Total page views for the product.
    */
    'pageViews'?: number;
    /**
    * Start date of the interval (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
    */
    'startDate'?: string;
    /**
    * Unit sold breakdowns.
    */
    'unitSoldBreakdowns'?: Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsUnitSoldBreakdowns>;
    /**
    * Number of units sold for the product.
    */
    'unitsSold'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "avgPageVisitorBreakdowns",
            "baseName": "avg_page_visitor_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsAvgPageVisitorBreakdowns>"
        },
        {
            "name": "avgPageVisitors",
            "baseName": "avg_page_visitors",
            "type": "number"
        },
        {
            "name": "clickThroughRate",
            "baseName": "click_through_rate",
            "type": "string"
        },
        {
            "name": "clickThroughRateBreakdowns",
            "baseName": "click_through_rate_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsClickThroughRateBreakdowns>"
        },
        {
            "name": "endDate",
            "baseName": "end_date",
            "type": "string"
        },
        {
            "name": "gmv",
            "baseName": "gmv",
            "type": "Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmv"
        },
        {
            "name": "gmvBreakdowns",
            "baseName": "gmv_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsGmvBreakdowns>"
        },
        {
            "name": "impressionBreakdowns",
            "baseName": "impression_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsImpressionBreakdowns>"
        },
        {
            "name": "impressions",
            "baseName": "impressions",
            "type": "number"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "number"
        },
        {
            "name": "pageViewBreakdowns",
            "baseName": "page_view_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsPageViewBreakdowns>"
        },
        {
            "name": "pageViews",
            "baseName": "page_views",
            "type": "number"
        },
        {
            "name": "startDate",
            "baseName": "start_date",
            "type": "string"
        },
        {
            "name": "unitSoldBreakdowns",
            "baseName": "unit_sold_breakdowns",
            "type": "Array<Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervalsUnitSoldBreakdowns>"
        },
        {
            "name": "unitsSold",
            "baseName": "units_sold",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopProductPerformanceResponseDataPerformanceComparisonIntervals.attributeTypeMap;
    }
}

