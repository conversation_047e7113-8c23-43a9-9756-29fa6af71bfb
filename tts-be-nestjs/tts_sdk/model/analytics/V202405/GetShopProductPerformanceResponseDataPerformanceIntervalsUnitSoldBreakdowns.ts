/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Analytics202405GetShopProductPerformanceResponseDataPerformanceIntervalsUnitSoldBreakdowns {
    /**
    * Number of units sold for the corresponding type.
    */
    'amount'?: number;
    /**
    * Breakdown type. Possible values: LIVE, VIDEO, PRODUCT_CARD
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "amount",
            "baseName": "amount",
            "type": "number"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Analytics202405GetShopProductPerformanceResponseDataPerformanceIntervalsUnitSoldBreakdowns.attributeTypeMap;
    }
}

