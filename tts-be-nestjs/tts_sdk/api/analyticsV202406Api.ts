/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import localVarRequest from 'request';
import http from 'http';

/* tslint:disable:no-unused-locals */
import { Analytics202406GetShopSKUPerformanceListResponse } from '../model/analytics/V202406/GetShopSKUPerformanceListResponse';
import { Analytics202406GetShopSKUPerformanceResponse } from '../model/analytics/V202406/GetShopSKUPerformanceResponse';

import { ObjectSerializer, Authentication, VoidAuth, Interceptor } from '../model/models';

import { HttpError, RequestFile } from './apis';

let defaultBasePath = 'https://open-api.tiktokglobalshop.com';

// ===============================================
// This file is autogenerated - Please do not edit
// ===============================================

export enum AnalyticsV202406ApiApiKeys {
}

export class AnalyticsV202406Api {
    protected _basePath = defaultBasePath;
    protected _defaultHeaders : any = {};
    protected _useQuerystring : boolean = false;

    static readonly apiName = 'AnalyticsV202406Api' as const;

    protected authentications = {
        'default': <Authentication>new VoidAuth(),
    }

    protected interceptors: Interceptor[] = [];

    constructor(basePath?: string);
    constructor(basePathOrUsername: string, password?: string, basePath?: string) {
        if (password) {
            if (basePath) {
                this.basePath = basePath;
            }
        } else {
            if (basePathOrUsername) {
                this.basePath = basePathOrUsername
            }
        }
    }

    set useQuerystring(value: boolean) {
        this._useQuerystring = value;
    }

    set basePath(basePath: string) {
        this._basePath = basePath;
    }

    set defaultHeaders(defaultHeaders: any) {
        this._defaultHeaders = defaultHeaders;
    }

    get defaultHeaders() {
        return this._defaultHeaders;
    }

    get basePath() {
        return this._basePath;
    }

    public setDefaultAuthentication(auth: Authentication) {
        this.authentications.default = auth;
    }

    public setApiKey(key: AnalyticsV202406ApiApiKeys, value: string) {
        (this.authentications as any)[AnalyticsV202406ApiApiKeys[key]].apiKey = value;
    }

    public addInterceptor(interceptor: Interceptor) {
        this.interceptors.push(interceptor);
    }

    /**
     * Returns a list of SKU performance metrics. This API currently provides data only for shops registered in the United States, United Kingdom, Singapore, Vietnam, and Thailand.
     * @summary GetShopSKUPerformanceList
     * @param startDateGe Start time (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
     * @param endDateLt End time (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param pageSize Number of records per page. The maximum page_size value is 100
     * @param sortField Sort field, possible values: -  gmv (default) - sku_orders - units_sold 
     * @param sortOrder Sort direction, possible values: - DESC (default) - ASC
     * @param pageToken Page token, indicating the current position. The page_token is empty by default, indicating first position.
     * @param productId Filter SKUs by product ID. If a product_id is provided, the API will only return SKUs for the given product ID, otherwise it will return all SKUs for the shop
     * @param currency Currency: - USD: US dollars - LOCAL (default): Local currency where the shop is located
     * @param shopCipher 
     */
    public async ShopSkusPerformanceGet (startDateGe: string, endDateLt: string, xTtsAccessToken: string, contentType: string, pageSize?: number, sortField?: string, sortOrder?: string, pageToken?: string, productId?: string, currency?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Analytics202406GetShopSKUPerformanceListResponse;  }> {
        const localVarPath = this.basePath + '/analytics/202406/shop_skus/performance';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'startDateGe' is not null or undefined
        if (startDateGe === null || startDateGe === undefined) {
            throw new Error('Required parameter startDateGe was null or undefined when calling ShopSkusPerformanceGet.');
        }

        // verify required parameter 'endDateLt' is not null or undefined
        if (endDateLt === null || endDateLt === undefined) {
            throw new Error('Required parameter endDateLt was null or undefined when calling ShopSkusPerformanceGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ShopSkusPerformanceGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ShopSkusPerformanceGet.');
        }

        if (startDateGe !== undefined) {
            localVarQueryParameters['start_date_ge'] = ObjectSerializer.serialize(startDateGe, "string");
        }

        if (endDateLt !== undefined) {
            localVarQueryParameters['end_date_lt'] = ObjectSerializer.serialize(endDateLt, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "number");
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (productId !== undefined) {
            localVarQueryParameters['product_id'] = ObjectSerializer.serialize(productId, "string");
        }

        if (currency !== undefined) {
            localVarQueryParameters['currency'] = ObjectSerializer.serialize(currency, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Analytics202406GetShopSKUPerformanceListResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Analytics202406GetShopSKUPerformanceListResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Return SKU performance metrics. This API currently provides data only for shops registered in the United States, United Kingdom, Singapore, Vietnam, and Thailand.
     * @summary GetShopSKUPerformance
     * @param skuId 
     * @param startDateGe Start time (ISO 8601 YYYY-MM-DD format) in shop registered timezone, inclusive.
     * @param endDateLt End time (ISO 8601 YYYY-MM-DD format) in shop registered timezone, exclusive.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param withComparison Whether previous period data is returned for comparison. true | false. Default value is false The previous period has the same length and granularity as the current period with end time being the same as the start time of the current period. Example: If start_date_ge &#x3D; 2024-04-01 and end_date_lt &#x3D; 2024-04-08, the previous period data will be from 2024-03-25 to 2024-04-01.
     * @param granularity Granularity of the data. Available values: ALL, 1D Default value: ALL * ALL: aggregate * 1D: daily
     * @param currency Currency: - USD: US dollars - LOCAL (default): Local currency where the shop is located
     * @param shopCipher 
     */
    public async ShopSkusSkuIdPerformanceGet (skuId: string, startDateGe: string, endDateLt: string, xTtsAccessToken: string, contentType: string, withComparison?: boolean, granularity?: string, currency?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Analytics202406GetShopSKUPerformanceResponse;  }> {
        const localVarPath = this.basePath + '/analytics/202406/shop_skus/{sku_id}/performance'
            .replace('{' + 'sku_id' + '}', encodeURIComponent(String(skuId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'skuId' is not null or undefined
        if (skuId === null || skuId === undefined) {
            throw new Error('Required parameter skuId was null or undefined when calling ShopSkusSkuIdPerformanceGet.');
        }

        // verify required parameter 'startDateGe' is not null or undefined
        if (startDateGe === null || startDateGe === undefined) {
            throw new Error('Required parameter startDateGe was null or undefined when calling ShopSkusSkuIdPerformanceGet.');
        }

        // verify required parameter 'endDateLt' is not null or undefined
        if (endDateLt === null || endDateLt === undefined) {
            throw new Error('Required parameter endDateLt was null or undefined when calling ShopSkusSkuIdPerformanceGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ShopSkusSkuIdPerformanceGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ShopSkusSkuIdPerformanceGet.');
        }

        if (startDateGe !== undefined) {
            localVarQueryParameters['start_date_ge'] = ObjectSerializer.serialize(startDateGe, "string");
        }

        if (endDateLt !== undefined) {
            localVarQueryParameters['end_date_lt'] = ObjectSerializer.serialize(endDateLt, "string");
        }

        if (withComparison !== undefined) {
            localVarQueryParameters['with_comparison'] = ObjectSerializer.serialize(withComparison, "boolean");
        }

        if (granularity !== undefined) {
            localVarQueryParameters['granularity'] = ObjectSerializer.serialize(granularity, "string");
        }

        if (currency !== undefined) {
            localVarQueryParameters['currency'] = ObjectSerializer.serialize(currency, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Analytics202406GetShopSKUPerformanceResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Analytics202406GetShopSKUPerformanceResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
}

export const AnalyticsV202406ApiOperationNames = {
    ShopSkusPerformanceGet: 'ShopSkusPerformanceGet',ShopSkusSkuIdPerformanceGet: 'ShopSkusSkuIdPerformanceGet',
} as const


export type AnalyticsV202406ApiOperationTypes = {
    ShopSkusPerformanceGet: AnalyticsV202406Api['ShopSkusPerformanceGet'];ShopSkusSkuIdPerformanceGet: AnalyticsV202406Api['ShopSkusSkuIdPerformanceGet'];
};

