/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import localVarRequest from 'request';
import http from 'http';

/* tslint:disable:no-unused-locals */
import { Product202309ActivateProductRequestBody } from '../model/product/V202309/ActivateProductRequestBody';
import { Product202309ActivateProductResponse } from '../model/product/V202309/ActivateProductResponse';
import { Product202309CheckListingPrerequisitesResponse } from '../model/product/V202309/CheckListingPrerequisitesResponse';
import { Product202309CheckProductListingRequestBody } from '../model/product/V202309/CheckProductListingRequestBody';
import { Product202309CheckProductListingResponse } from '../model/product/V202309/CheckProductListingResponse';
import { Product202309CreateCustomBrandsRequestBody } from '../model/product/V202309/CreateCustomBrandsRequestBody';
import { Product202309CreateCustomBrandsResponse } from '../model/product/V202309/CreateCustomBrandsResponse';
import { Product202309CreateGlobalProductRequestBody } from '../model/product/V202309/CreateGlobalProductRequestBody';
import { Product202309CreateGlobalProductResponse } from '../model/product/V202309/CreateGlobalProductResponse';
import { Product202309CreateProductRequestBody } from '../model/product/V202309/CreateProductRequestBody';
import { Product202309CreateProductResponse } from '../model/product/V202309/CreateProductResponse';
import { Product202309DeactivateProductsRequestBody } from '../model/product/V202309/DeactivateProductsRequestBody';
import { Product202309DeactivateProductsResponse } from '../model/product/V202309/DeactivateProductsResponse';
import { Product202309DeleteGlobalProductsRequestBody } from '../model/product/V202309/DeleteGlobalProductsRequestBody';
import { Product202309DeleteGlobalProductsResponse } from '../model/product/V202309/DeleteGlobalProductsResponse';
import { Product202309DeleteProductsRequestBody } from '../model/product/V202309/DeleteProductsRequestBody';
import { Product202309DeleteProductsResponse } from '../model/product/V202309/DeleteProductsResponse';
import { Product202309EditGlobalProductRequestBody } from '../model/product/V202309/EditGlobalProductRequestBody';
import { Product202309EditGlobalProductResponse } from '../model/product/V202309/EditGlobalProductResponse';
import { Product202309EditProductRequestBody } from '../model/product/V202309/EditProductRequestBody';
import { Product202309EditProductResponse } from '../model/product/V202309/EditProductResponse';
import { Product202309GetAttributesResponse } from '../model/product/V202309/GetAttributesResponse';
import { Product202309GetBrandsResponse } from '../model/product/V202309/GetBrandsResponse';
import { Product202309GetCategoriesResponse } from '../model/product/V202309/GetCategoriesResponse';
import { Product202309GetCategoryRulesResponse } from '../model/product/V202309/GetCategoryRulesResponse';
import { Product202309GetGlobalAttributesResponse } from '../model/product/V202309/GetGlobalAttributesResponse';
import { Product202309GetGlobalCategoriesResponse } from '../model/product/V202309/GetGlobalCategoriesResponse';
import { Product202309GetGlobalCategoryRulesResponse } from '../model/product/V202309/GetGlobalCategoryRulesResponse';
import { Product202309GetGlobalProductResponse } from '../model/product/V202309/GetGlobalProductResponse';
import { Product202309GetProductResponse } from '../model/product/V202309/GetProductResponse';
import { Product202309InventorySearchRequestBody } from '../model/product/V202309/InventorySearchRequestBody';
import { Product202309InventorySearchResponse } from '../model/product/V202309/InventorySearchResponse';
import { Product202309PartialEditProductRequestBody } from '../model/product/V202309/PartialEditProductRequestBody';
import { Product202309PartialEditProductResponse } from '../model/product/V202309/PartialEditProductResponse';
import { Product202309PublishGlobalProductRequestBody } from '../model/product/V202309/PublishGlobalProductRequestBody';
import { Product202309PublishGlobalProductResponse } from '../model/product/V202309/PublishGlobalProductResponse';
import { Product202309RecommendCategoryRequestBody } from '../model/product/V202309/RecommendCategoryRequestBody';
import { Product202309RecommendCategoryResponse } from '../model/product/V202309/RecommendCategoryResponse';
import { Product202309RecommendGlobalCategoriesRequestBody } from '../model/product/V202309/RecommendGlobalCategoriesRequestBody';
import { Product202309RecommendGlobalCategoriesResponse } from '../model/product/V202309/RecommendGlobalCategoriesResponse';
import { Product202309RecoverProductsRequestBody } from '../model/product/V202309/RecoverProductsRequestBody';
import { Product202309RecoverProductsResponse } from '../model/product/V202309/RecoverProductsResponse';
import { Product202309SearchGlobalProductsRequestBody } from '../model/product/V202309/SearchGlobalProductsRequestBody';
import { Product202309SearchGlobalProductsResponse } from '../model/product/V202309/SearchGlobalProductsResponse';
import { Product202309SearchProductsRequestBody } from '../model/product/V202309/SearchProductsRequestBody';
import { Product202309SearchProductsResponse } from '../model/product/V202309/SearchProductsResponse';
import { Product202309UpdateGlobalInventoryRequestBody } from '../model/product/V202309/UpdateGlobalInventoryRequestBody';
import { Product202309UpdateGlobalInventoryResponse } from '../model/product/V202309/UpdateGlobalInventoryResponse';
import { Product202309UpdateInventoryRequestBody } from '../model/product/V202309/UpdateInventoryRequestBody';
import { Product202309UpdateInventoryResponse } from '../model/product/V202309/UpdateInventoryResponse';
import { Product202309UpdatePriceRequestBody } from '../model/product/V202309/UpdatePriceRequestBody';
import { Product202309UpdatePriceResponse } from '../model/product/V202309/UpdatePriceResponse';
import { Product202309UploadProductFileResponse } from '../model/product/V202309/UploadProductFileResponse';
import { Product202309UploadProductImageResponse } from '../model/product/V202309/UploadProductImageResponse';

import { ObjectSerializer, Authentication, VoidAuth, Interceptor } from '../model/models';

import { HttpError, RequestFile } from './apis';

let defaultBasePath = 'https://open-api.tiktokglobalshop.com';

// ===============================================
// This file is autogenerated - Please do not edit
// ===============================================

export enum ProductV202309ApiApiKeys {
}

export class ProductV202309Api {
    protected _basePath = defaultBasePath;
    protected _defaultHeaders : any = {};
    protected _useQuerystring : boolean = false;

    static readonly apiName = 'ProductV202309Api' as const;

    protected authentications = {
        'default': <Authentication>new VoidAuth(),
    }

    protected interceptors: Interceptor[] = [];

    constructor(basePath?: string);
    constructor(basePathOrUsername: string, password?: string, basePath?: string) {
        if (password) {
            if (basePath) {
                this.basePath = basePath;
            }
        } else {
            if (basePathOrUsername) {
                this.basePath = basePathOrUsername
            }
        }
    }

    set useQuerystring(value: boolean) {
        this._useQuerystring = value;
    }

    set basePath(basePath: string) {
        this._basePath = basePath;
    }

    set defaultHeaders(defaultHeaders: any) {
        this._defaultHeaders = defaultHeaders;
    }

    get defaultHeaders() {
        return this._defaultHeaders;
    }

    get basePath() {
        return this._basePath;
    }

    public setDefaultAuthentication(auth: Authentication) {
        this.authentications.default = auth;
    }

    public setApiKey(key: ProductV202309ApiApiKeys, value: string) {
        (this.authentications as any)[ProductV202309ApiApiKeys[key]].apiKey = value;
    }

    public addInterceptor(interceptor: Interceptor) {
        this.interceptors.push(interceptor);
    }

    /**
     * Retrieve all available brands for your shop, including the built-in brands and any custom brands created using the [Create Custom Brands API](650a0926f1fd3102b91bbfb0). Pass the returned brand ID when creating or editing a product to associate the brand with the product. - To check if a brand is fully authorized for use in a specific product category, specify the **category ID**. - To obtain the full list of brands that your shop can potentially use and their authorization status, omit the category ID. We recommend that you specify the **brand name** to narrow down the list of brands returned. **Key concept** Whether you can select and display a brand depends on the brand\'s authorization status, the categories authorized for the brand, and whether the brand is classified as T1 (internationally renowned brands that require prior brand authorization). **- Brand selection rules**: You can only select the following types of brands during product creation/editing.    - Authorized brands which contain the desired category (`authorized_status=AUTHORIZED` and `brand_status=AVAILABLE`)    - Unauthorized non-T1 brands (`authorized_status=UNAUTHORIZED` and `is_t1_brand=false`)  **- Brand display rules**: Note however that brands will only appear on the product display page if the brand is authorized (`authorized_status=AUTHORIZED`) and available in the desired category (`brand_status=AVAILABLE`). This means that you need to obtain brand authorization for unauthorized non-T1 brands before they can be displayed. Obtain brand authorization or add categories to an authorized brand through TikTok Shop Seller Center > Qualification Center > Brand qualification. **For Tokopedia sellers**: You can select and display any returned brand on Tokopedia regardless of these rules.
     * @summary GetBrands
     * @param pageSize The number of results to be returned per page. Valid range: [1-100]
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param categoryId Specify a category ID to show the availability of **authorized brands** in the category. **Note**: Specify this value to obtain an accurate list of brands that you can use in a category.
     * @param isAuthorized Filter results by the brand authorization status. Possible values: - 1: Returns only authorized brands - 0: Returns all brands
     * @param brandName Filter results to include brand names that begin with the specified value.
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the &#x60;next_page_token&#x60; from a previous response. It is not needed for the first page.
     * @param categoryVersion The category tree version that corresponds to the specified &#x60;category_id&#x60;. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: For US shops, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     * @param shopCipher 
     */
    public async BrandsGet (pageSize: number, xTtsAccessToken: string, contentType: string, categoryId?: string, isAuthorized?: boolean, brandName?: string, pageToken?: string, categoryVersion?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetBrandsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/brands';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'pageSize' is not null or undefined
        if (pageSize === null || pageSize === undefined) {
            throw new Error('Required parameter pageSize was null or undefined when calling BrandsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling BrandsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling BrandsGet.');
        }

        if (categoryId !== undefined) {
            localVarQueryParameters['category_id'] = ObjectSerializer.serialize(categoryId, "string");
        }

        if (isAuthorized !== undefined) {
            localVarQueryParameters['is_authorized'] = ObjectSerializer.serialize(isAuthorized, "boolean");
        }

        if (brandName !== undefined) {
            localVarQueryParameters['brand_name'] = ObjectSerializer.serialize(brandName, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "number");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetBrandsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetBrandsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Create custom brands for your own use across all markets. Authorization is not required when creating a brand. You can create the brand first and obtain brand authorization later through the Qualification Center in TikTok Shop Seller Center. **Note**: You can create up to 50 brands per day, with a total limit of 1,000 brands.
     * @summary CreateCustomBrands
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param CreateCustomBrandsRequestBody 
     */
    public async BrandsPost (xTtsAccessToken: string, contentType: string, CreateCustomBrandsRequestBody?: Product202309CreateCustomBrandsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309CreateCustomBrandsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/brands';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling BrandsPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling BrandsPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CreateCustomBrandsRequestBody, "Product202309CreateCustomBrandsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309CreateCustomBrandsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309CreateCustomBrandsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the standard built-in product and sales attributes for listing a product in a particular category based on your shop\'s location. Products on TikTok Shop are grouped into categories predefined by TikTok Shop, and each category is associated with a standard set of product attributes and sales attributes. - **Sales attributes** (e.g. size, color, length) define product variants and are optional if your product is straightforward and has no variants. - **Product attributes** (e.g. manufacturer, country of origin, materials used) describe the product as a whole, regardless of variant. Some product attributes are mandatory based on listing policies. Use this API to determine the mandatory and optional attributes before listing a product. **Note**: Only leaf categories are supported.
     * @summary GetAttributes
     * @param categoryId The ID of the category of this product. It must be a leaf category.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying the attribute information.  Default: The default locale of your shop. Possible values: - &#x60;de-DE&#x60; - &#x60;en-GB&#x60; - &#x60;en-IE&#x60; - &#x60;en-US&#x60; - &#x60;es-ES&#x60; - &#x60;es-MX&#x60; - &#x60;fr-FR&#x60; - &#x60;id-ID&#x60; - &#x60;it-IT&#x60; - &#x60;ja-JP&#x60; - &#x60;ms-MY&#x60; - &#x60;pt-BR&#x60; - &#x60;th-TH&#x60; - &#x60;vi-VN&#x60; - &#x60;zh-CN&#x60;
     * @param categoryVersion The category tree version that corresponds to the specified &#x60;category_id&#x60;. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: For US shops, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     * @param shopCipher 
     */
    public async CategoriesCategoryIdAttributesGet (categoryId: string, xTtsAccessToken: string, contentType: string, locale?: string, categoryVersion?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetAttributesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories/{category_id}/attributes'
            .replace('{' + 'category_id' + '}', encodeURIComponent(String(categoryId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'categoryId' is not null or undefined
        if (categoryId === null || categoryId === undefined) {
            throw new Error('Required parameter categoryId was null or undefined when calling CategoriesCategoryIdAttributesGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesCategoryIdAttributesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesCategoryIdAttributesGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetAttributesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetAttributesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the standard built-in product and sales attributes for listing a global product in a particular category, regardless of market variations. Products on TikTok Shop are grouped into categories predefined by TikTok Shop, and each category is associated with a standard set of product attributes and sales attributes. - **Sales attributes** (e.g. size, color, length) define product variants and are optional if your product is straightforward and has no variants. - **Product attributes** (e.g. manufacturer, country of origin, materials used) describe the product as a whole, regardless of variant. Some product attributes are mandatory based on listing policies. Use this API to determine the mandatory and optional attributes before listing a global product. **Note**: Only leaf categories are supported.
     * @summary GetGlobalAttributes
     * @param categoryId The ID of the category. It must be a leaf category. 
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying the attribute information.  Default: en-US Possible values: - &#x60;de-DE&#x60; - &#x60;en-GB&#x60; - &#x60;en-IE&#x60; - &#x60;en-US&#x60; - &#x60;es-ES&#x60; - &#x60;es-MX&#x60; - &#x60;fr-FR&#x60; - &#x60;id-ID&#x60; - &#x60;it-IT&#x60; - &#x60;ms-MY&#x60; - &#x60;th-TH&#x60; - &#x60;vi-VN&#x60; - &#x60;zh-CN&#x60;
     * @param categoryVersion The category tree version that corresponds to the specified &#x60;category_id&#x60;. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: If the seller account contains an active US shop, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     */
    public async CategoriesCategoryIdGlobalAttributesGet (categoryId: string, xTtsAccessToken: string, contentType: string, locale?: string, categoryVersion?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalAttributesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories/{category_id}/global_attributes'
            .replace('{' + 'category_id' + '}', encodeURIComponent(String(categoryId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'categoryId' is not null or undefined
        if (categoryId === null || categoryId === undefined) {
            throw new Error('Required parameter categoryId was null or undefined when calling CategoriesCategoryIdGlobalAttributesGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesCategoryIdGlobalAttributesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesCategoryIdGlobalAttributesGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalAttributesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetGlobalAttributesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the additional requirements (beyond mandatory product attributes) for listing a global product in a particular category, regardless of market variations. Requirements may include product certifications, size charts, dimensions and more. Use this API to determine the supporting information that you must prepare before listing a global product. **Note**: Only leaf categories are supported.
     * @summary GetGlobalCategoryRules
     * @param categoryId The ID of the category. It must be a leaf category. 
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param categoryVersion The category tree version that corresponds to the specified &#x60;category_id&#x60;. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: If the seller account contains an active US shop, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     */
    public async CategoriesCategoryIdGlobalRulesGet (categoryId: string, xTtsAccessToken: string, contentType: string, categoryVersion?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalCategoryRulesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories/{category_id}/global_rules'
            .replace('{' + 'category_id' + '}', encodeURIComponent(String(categoryId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'categoryId' is not null or undefined
        if (categoryId === null || categoryId === undefined) {
            throw new Error('Required parameter categoryId was null or undefined when calling CategoriesCategoryIdGlobalRulesGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesCategoryIdGlobalRulesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesCategoryIdGlobalRulesGet.');
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalCategoryRulesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetGlobalCategoryRulesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the additional requirements (beyond mandatory product attributes) for listing a product in a particular category based on your shop\'s location. Requirements may include product certifications, size charts, dimensions and more. Use this API to determine the supporting information that you must prepare before listing a product. **Note**: Only leaf categories are supported.
     * @summary GetCategoryRules
     * @param categoryId The ID of the category. It must be a leaf category. 
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param categoryVersion The category tree version that corresponds to the specified &#x60;category_id&#x60;. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: For US shops, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     * @param locale The BCP-47 locale codes for displaying category information.  Default: en-US Possible values: - de-DE - en-GB - en-IE - en-US - es-ES - es-MX - fr-FR - id-ID - it-IT - ja-JP - ms-MY - pt-BR - th-TH - vi-VN - zh-CN
     * @param shopCipher 
     */
    public async CategoriesCategoryIdRulesGet (categoryId: string, xTtsAccessToken: string, contentType: string, categoryVersion?: string, locale?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetCategoryRulesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories/{category_id}/rules'
            .replace('{' + 'category_id' + '}', encodeURIComponent(String(categoryId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'categoryId' is not null or undefined
        if (categoryId === null || categoryId === undefined) {
            throw new Error('Required parameter categoryId was null or undefined when calling CategoriesCategoryIdRulesGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesCategoryIdRulesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesCategoryIdRulesGet.');
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetCategoryRulesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetCategoryRulesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the list of product categories available for your shop. Product categories are updated frequently, so it\'s recommended to call the API in real time to ensure you are using the latest category data. Caching category data locally may result in using outdated information, leading to errors when creating products. **For the Indonesia market**: To list a product on both TikTok Shop and Tokopedia, you must use only categories that are available on both platforms. Please call this API twice to identify the overlapping categories.
     * @summary GetCategories
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying category information. Default: The default locale of your shop. Possible values: - &#x60;de-DE&#x60; - &#x60;en-GB&#x60; - &#x60;en-IE&#x60; - &#x60;en-US&#x60; - &#x60;es-ES&#x60; - &#x60;es-MX&#x60; - &#x60;fr-FR&#x60; - &#x60;id-ID&#x60; - &#x60;it-IT&#x60; - &#x60;ja-JP&#x60; - &#x60;ms-MY&#x60; - &#x60;pt-BR&#x60; - &#x60;th-TH&#x60; - &#x60;vi-VN&#x60; - &#x60;zh-CN&#x60;
     * @param keyword Filter categories by this keyword in &#x60;local_name&#x60;.
     * @param categoryVersion Filter categories by the category tree version. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: For US shops, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     * @param listingPlatform Filter categories by the specified platform. Possible values: - TIKTOK_SHOP - TOKOPEDIA Default: TIKTOK_SHOP  Applicable only for sellers that migrated from Tokopedia.
     * @param shopCipher 
     */
    public async CategoriesGet (xTtsAccessToken: string, contentType: string, locale?: string, keyword?: string, categoryVersion?: string, listingPlatform?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetCategoriesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (keyword !== undefined) {
            localVarQueryParameters['keyword'] = ObjectSerializer.serialize(keyword, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        if (listingPlatform !== undefined) {
            localVarQueryParameters['listing_platform'] = ObjectSerializer.serialize(listingPlatform, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetCategoriesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetCategoriesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the recommended category for a candidate product based on its title, description, and images. If you are syncing product catalogs from an external system to TikTok Shop, use this API to facilitate product categorization. **Note**: Double-byte characters (e.g. Chinese characters) are not supported in text fields such as descriptions and titles. If you include them, the API request will fail.
     * @summary RecommendCategory
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param RecommendCategoryRequestBody 
     */
    public async CategoriesRecommendPost (xTtsAccessToken: string, contentType: string, shopCipher?: string, RecommendCategoryRequestBody?: Product202309RecommendCategoryRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309RecommendCategoryResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/categories/recommend';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CategoriesRecommendPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CategoriesRecommendPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(RecommendCategoryRequestBody, "Product202309RecommendCategoryRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309RecommendCategoryResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309RecommendCategoryResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Upload non-image files, such as PDF or video to TikTok Shop.  Use this API when you need to add videos to your product to improve the shopping experience, or submit certifications or reports to meet TikTok Shop requirements for listing restricted products. Pass the returned ID from this API when creating or editing a product to associate the file with the product.
     * @summary UploadProductFile
     * @param xTtsAccessToken 
     * @param contentType Allowed type: multipart/form-data
     * @param data The local file to be uploaded.  **Note**： - Supported formats: PDF, MP4, MOV, MKV, WMV, WEBM, AVI, 3GP, FLV, MPEG - Max file size: 10MB - Video aspect ratio: 9:16 to 16:9  **Recommendations for product videos**: - Aspect ratio: 1:1  - Resolution: HD 720p or higher - Duration: 20 - 60 seconds 
     * @param name The name of the file, including the file extension (e.g. &#x60;certification.pdf&#x60;)  **Note**: - Do not use additional periods, except the one preceding the file extension. - Do not begin the name with symbols. - Do not include spaces.
     */
    public async FilesUploadPost (xTtsAccessToken: string, contentType: string, data?: RequestFile, name?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309UploadProductFileResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/files/upload';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling FilesUploadPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling FilesUploadPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        if (data !== undefined) {
            localVarFormParams['data'] = data;
        }
        localVarUseFormData = true;

        if (name !== undefined) {
            localVarFormParams['name'] = ObjectSerializer.serialize(name, "string");
        }

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309UploadProductFileResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309UploadProductFileResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve all available product categories, regardless of market variations. Product categories are updated frequently, so it\'s recommended to call the API in real time to ensure you are using the latest category data. Caching category data locally may result in using outdated information, leading to errors when creating global products. 
     * @summary GetGlobalCategories
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying category information.  Default: en-US Possible values: - &#x60;de-DE&#x60; - &#x60;en-GB&#x60; - &#x60;en-IE&#x60; - &#x60;en-US&#x60; - &#x60;es-ES&#x60; - &#x60;es-MX&#x60; - &#x60;fr-FR&#x60; - &#x60;id-ID&#x60; - &#x60;it-IT&#x60; - &#x60;ms-MY&#x60; - &#x60;th-TH&#x60; - &#x60;vi-VN&#x60; - &#x60;zh-CN&#x60;
     * @param keyword Filter categories by this keyword in &#x60;local_name&#x60;.
     * @param categoryVersion Filter categories by the category tree version. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree.   **Important**: If the seller account contains an active US shop, you must pass &#x60;v2&#x60; when using this API. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: &#x60;v1&#x60;
     */
    public async GlobalCategoriesGet (xTtsAccessToken: string, contentType: string, locale?: string, keyword?: string, categoryVersion?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalCategoriesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_categories';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalCategoriesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalCategoriesGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (keyword !== undefined) {
            localVarQueryParameters['keyword'] = ObjectSerializer.serialize(keyword, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalCategoriesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetGlobalCategoriesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve the recommended categories for a candidate global product based on its title, description, and images. If you are syncing product catalogs from an external system to TikTok Shop, use this API to facilitate product classification. **Note**: Double-byte characters (e.g. Chinese characters) are not supported in text fields such as descriptions and titles. If you include them, the API request will fail.
     * @summary RecommendGlobalCategories
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param RecommendGlobalCategoriesRequestBody 
     */
    public async GlobalCategoriesRecommendPost (xTtsAccessToken: string, contentType: string, RecommendGlobalCategoriesRequestBody?: Product202309RecommendGlobalCategoriesRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309RecommendGlobalCategoriesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_categories/recommend';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalCategoriesRecommendPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalCategoriesRecommendPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(RecommendGlobalCategoriesRequestBody, "Product202309RecommendGlobalCategoriesRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309RecommendGlobalCategoriesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309RecommendGlobalCategoriesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Delete global products that you no longer need.
     * @summary DeleteGlobalProducts
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param DeleteGlobalProductsRequestBody 
     */
    public async GlobalProductsDelete (xTtsAccessToken: string, contentType: string, DeleteGlobalProductsRequestBody?: Product202309DeleteGlobalProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309DeleteGlobalProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsDelete.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsDelete.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'DELETE',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(DeleteGlobalProductsRequestBody, "Product202309DeleteGlobalProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309DeleteGlobalProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309DeleteGlobalProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve all properties of a global product that is in the \"DRAFT\", \"UNPUBLISHED\", or \"PUBLISHED\" status, and the corresponding local product IDs in the published markets.
     * @summary GetGlobalProduct
     * @param globalProductId Global product ID
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     */
    public async GlobalProductsGlobalProductIdGet (globalProductId: string, xTtsAccessToken: string, contentType: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products/{global_product_id}'
            .replace('{' + 'global_product_id' + '}', encodeURIComponent(String(globalProductId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'globalProductId' is not null or undefined
        if (globalProductId === null || globalProductId === undefined) {
            throw new Error('Required parameter globalProductId was null or undefined when calling GlobalProductsGlobalProductIdGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsGlobalProductIdGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsGlobalProductIdGet.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetGlobalProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetGlobalProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Update the inventory of unpublished global products if you have access to the Multi-Warehouse feature. The inventory of published global products will not be affected by this update operation.
     * @summary UpdateGlobalInventory
     * @param globalProductId The global product ID to be updated.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param UpdateGlobalInventoryRequestBody 
     */
    public async GlobalProductsGlobalProductIdInventoryUpdatePost (globalProductId: string, xTtsAccessToken: string, contentType: string, UpdateGlobalInventoryRequestBody?: Product202309UpdateGlobalInventoryRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309UpdateGlobalInventoryResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products/{global_product_id}/inventory/update'
            .replace('{' + 'global_product_id' + '}', encodeURIComponent(String(globalProductId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'globalProductId' is not null or undefined
        if (globalProductId === null || globalProductId === undefined) {
            throw new Error('Required parameter globalProductId was null or undefined when calling GlobalProductsGlobalProductIdInventoryUpdatePost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsGlobalProductIdInventoryUpdatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsGlobalProductIdInventoryUpdatePost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(UpdateGlobalInventoryRequestBody, "Product202309UpdateGlobalInventoryRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309UpdateGlobalInventoryResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309UpdateGlobalInventoryResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Publish and convert a global product to local products in one or multiple shops in supported markets. After publishing, the product is sent for review by TikTok Shop in the respective markets. For sellers in the EU market, the provided information will also be automatically translated into all EU languages supported by TikTok Shop. **Note**:  - You can only publish in each market once. To change product information, edit the global product by using the [Edit Global Product API](6509e1bcc16ffe02b8dc3cd7). The changes will be automatically synchronized to all markets where the product is published - Use the [Get Product API](6509d85b4a0bb702c057fdda) to obtain the converted local product information in the target market\'s language.  - Use the [Edit Product API](6509da7d0fcef602bf1caddf) or [Partial Edit Product API](650a98d74a0bb702c06c3289) to edit the local product information, if necessary.
     * @summary PublishGlobalProduct
     * @param globalProductId The global product id.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param PublishGlobalProductRequestBody 
     */
    public async GlobalProductsGlobalProductIdPublishPost (globalProductId: string, xTtsAccessToken: string, contentType: string, PublishGlobalProductRequestBody?: Product202309PublishGlobalProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309PublishGlobalProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products/{global_product_id}/publish'
            .replace('{' + 'global_product_id' + '}', encodeURIComponent(String(globalProductId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'globalProductId' is not null or undefined
        if (globalProductId === null || globalProductId === undefined) {
            throw new Error('Required parameter globalProductId was null or undefined when calling GlobalProductsGlobalProductIdPublishPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsGlobalProductIdPublishPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsGlobalProductIdPublishPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(PublishGlobalProductRequestBody, "Product202309PublishGlobalProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309PublishGlobalProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309PublishGlobalProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Edit all properties (e.g. description, brand, images, attributes) of a global product. **IMPORTANT**: The changes will be automatically retranslated and synced to all markets where the product is published. If you have already edited the product information or translations of the associated local products, they will be overwritten by the information in this API. **Note**: - All inputs, including blanks, in the request payload will overwrite existing values. To retain an existing value, make sure to include it in your request. - The language used in the product content must align with the target market\'s language (e.g. don\'t use Chinese characters), otherwise the listing will fail or be rejected.
     * @summary EditGlobalProduct
     * @param globalProductId The global product ID generated by TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param EditGlobalProductRequestBody 
     */
    public async GlobalProductsGlobalProductIdPut (globalProductId: string, xTtsAccessToken: string, contentType: string, EditGlobalProductRequestBody?: Product202309EditGlobalProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309EditGlobalProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products/{global_product_id}'
            .replace('{' + 'global_product_id' + '}', encodeURIComponent(String(globalProductId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'globalProductId' is not null or undefined
        if (globalProductId === null || globalProductId === undefined) {
            throw new Error('Required parameter globalProductId was null or undefined when calling GlobalProductsGlobalProductIdPut.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsGlobalProductIdPut.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsGlobalProductIdPut.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'PUT',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(EditGlobalProductRequestBody, "Product202309EditGlobalProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309EditGlobalProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309EditGlobalProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Create global products to be sold in one or multiple shops outside of the seller\'s base country. You can only create global products in `AVAILABLE` product categories. For other categories, contact your account manager for assistance. After product creation, use the [Publish Global Product API](https://partner.tiktokshop.com/docv2/page/650a64d6defece02be678fd6) to publish and translate the product in the desired markets. **Key concept**: Global products are products created by cross-border sellers to be sold in shops outside of their base country. With global products, cross-border sellers operating across multiple markets can avoid creating the same product for each shop individually. Instead, they just need to create a single global product, which can be published and synced to all their shops, simplifying product management across markets. **Note**: - The language used in the product content must align with the target market\'s language (e.g. don\'t use Chinese characters), otherwise the listing will fail or be rejected. - To create and list local products intended for sale exclusively in local shops, use the [Create Product API](https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a) instead.
     * @summary CreateGlobalProduct
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param CreateGlobalProductRequestBody 
     */
    public async GlobalProductsPost (xTtsAccessToken: string, contentType: string, CreateGlobalProductRequestBody?: Product202309CreateGlobalProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309CreateGlobalProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CreateGlobalProductRequestBody, "Product202309CreateGlobalProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309CreateGlobalProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309CreateGlobalProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Search your catalog to retrieve a list of global products based on filter conditions through this API. If you need to get detailed information about a global product, use the global product ID response in the \"Get Global Product\" API.
     * @summary SearchGlobalProducts
     * @param pageSize \&quot;page_size\&quot; represents the return list pagination, the number of global products per page. Each page can retrieve up to 100 global product records.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param pageToken The pagination token is a cursor used for pagination. It is not needed for the first page. The token is returned in the previous pagination query to determine the current position. 
     * @param SearchGlobalProductsRequestBody 
     */
    public async GlobalProductsSearchPost (pageSize: number, xTtsAccessToken: string, contentType: string, pageToken?: string, SearchGlobalProductsRequestBody?: Product202309SearchGlobalProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309SearchGlobalProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/global_products/search';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'pageSize' is not null or undefined
        if (pageSize === null || pageSize === undefined) {
            throw new Error('Required parameter pageSize was null or undefined when calling GlobalProductsSearchPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling GlobalProductsSearchPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling GlobalProductsSearchPost.');
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "number");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(SearchGlobalProductsRequestBody, "Product202309SearchGlobalProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309SearchGlobalProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309SearchGlobalProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Upload local images to TikTok Shop for use as product images, variant images, size charts, certification images and so on. Pass the returned URI from this API when creating or editing a product to associate the image with the product. **Note**: All images used in TikTok Shop products must be uploaded through this API. You will not be able to use any image URLs that are not hosted by TikTok Shop.
     * @summary UploadProductImage
     * @param xTtsAccessToken 
     * @param contentType Allowed type: multipart/form-data
     * @param data The local image file to be uploaded.  **Note**: - Supported formats: JPG, JPEG, PNG, WEBP, HEIC, BMP - Max size: 5MB - Dimensions: [100x100 px, 20000x20000 px] (For &#x60;use_case&#x3D;MAIN_IMAGE&#x60;, the dimensions must be between 300x300 px and 4000x4000 px)
     * @param useCase The usage scenario of the image. Possible values: - MAIN_IMAGE: An image displayed in the product image gallery. - ATTRIBUTE_IMAGE: An image that represents a product variant (e.g. color). - DESCRIPTION_IMAGE: An image used within the product description. - CERTIFICATION_IMAGE: An image to provide supporting information to meet TikTok Shop requirements for listing restricted products (e.g., images of certifications, product packaging, labeling). - SIZE_CHART_IMAGE: An image that displays the product\\\&#39;s measurement details.  **Note**: The image aspect ratio for &#x60;MAIN_IMAGE&#x60; and &#x60;ATTRIBUTE_IMAGE&#x60; will be automatically cropped to fit within the 3:4 to 4:3 range.
     */
    public async ImagesUploadPost (xTtsAccessToken: string, contentType: string, data?: RequestFile, useCase?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309UploadProductImageResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/images/upload';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ImagesUploadPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ImagesUploadPost.');
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        if (data !== undefined) {
            localVarFormParams['data'] = data;
        }
        localVarUseFormData = true;

        if (useCase !== undefined) {
            localVarFormParams['use_case'] = ObjectSerializer.serialize(useCase, "string");
        }

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309UploadProductImageResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309UploadProductImageResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve inventory information for multiple products or SKUs. **Note**:  - Searches can be based on either product IDs or SKU IDs, not both at the same time. - Passing Product IDs returns the inventory information of all SKUs under the specified products. - Passing SKU IDs returns the inventory information for the specified SKUs.
     * @summary InventorySearch
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param InventorySearchRequestBody 
     */
    public async InventorySearchPost (xTtsAccessToken: string, contentType: string, shopCipher?: string, InventorySearchRequestBody?: Product202309InventorySearchRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309InventorySearchResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/inventory/search';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling InventorySearchPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling InventorySearchPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(InventorySearchRequestBody, "Product202309InventorySearchRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309InventorySearchResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309InventorySearchResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to get the product rules of the shop and whether the prerequisites for listing product are met.
     * @summary CheckListingPrerequisites
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     */
    public async PrerequisitesGet (xTtsAccessToken: string, contentType: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309CheckListingPrerequisitesResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/prerequisites';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling PrerequisitesGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling PrerequisitesGet.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309CheckListingPrerequisitesResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309CheckListingPrerequisitesResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Activate hidden products that are in the `Seller_deactivated` or `Platform_deactivated` status and display them in the TikTok Shop catalog.  After submitting the activation request, the products will be sent to TikTok Shop for auditing and their status will change to `Pending`. If a product passes the audit, its status will change back to `Activate`. You can use the [Product status change webhook](https://partner.tiktokshop.com/docv2/page/650956aff1fd3102b90b6261) to monitor the audit status.
     * @summary ActivateProduct
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param ActivateProductRequestBody 
     */
    public async ProductsActivatePost (xTtsAccessToken: string, contentType: string, shopCipher?: string, ActivateProductRequestBody?: Product202309ActivateProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309ActivateProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/activate';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsActivatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsActivatePost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(ActivateProductRequestBody, "Product202309ActivateProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309ActivateProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309ActivateProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Deactivate products that are in the `Activate` status and hide them from buyers. The status changes to `Seller_deactivated` after deactivation. In the event there\'s some issue with a product (e.g. out of stock), you can deactivate the product and hide it temporarily from buyers. When the issues are resolved, you can activate the product again by using the [Activate Product API](https://partner.tiktokshop.com/docv2/page/650306ff5a12ff0294eab4a9).
     * @summary DeactivateProducts
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param DeactivateProductsRequestBody 
     */
    public async ProductsDeactivatePost (xTtsAccessToken: string, contentType: string, shopCipher?: string, DeactivateProductsRequestBody?: Product202309DeactivateProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309DeactivateProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/deactivate';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsDeactivatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsDeactivatePost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(DeactivateProductsRequestBody, "Product202309DeactivateProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309DeactivateProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309DeactivateProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Delete non-frozen products that you no longer need. **For the Indonesia market**: You can only delete products that are not frozen on all listing platforms. If the product is frozen on any platform, it cannot be deleted.
     * @summary DeleteProducts
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param DeleteProductsRequestBody 
     */
    public async ProductsDelete (xTtsAccessToken: string, contentType: string, shopCipher?: string, DeleteProductsRequestBody?: Product202309DeleteProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309DeleteProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsDelete.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsDelete.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'DELETE',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(DeleteProductsRequestBody, "Product202309DeleteProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309DeleteProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309DeleteProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Identify any issues with your product properties in advance to ensure your product is ready for listing. Every product must meet TikTok Shop requirements before it can be listed. Before listing, you can submit all relevant product information to this API to check whether a listing meets these requirements. You\'ll receive a list of issues to resolve before listing. This process helps reduce the risk of failure when creating products. **Note**:  - The language used in the product content must align with the target market\'s language (e.g. don\'t use Chinese characters), otherwise the listing will fail or be rejected.
     * @summary CheckProductListing
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param isDiagnosisRequired (**Deprecated**: This field is deprecated and will be removed in a future API version. Use [Diagnose and Optimize Product](677c9523f7765c0308b3d68d) API instead to get listing quality related information.) A flag to indicate whether to return the listing quality information (US only) and optimization diagnosis results for the product. If this is set to &#x60;false&#x60;, the response body will exclude the &#x60;listing_quality&#x60; and &#x60;diagnoses&#x60; objects. Default: true
     * @param shopCipher 
     * @param CheckProductListingRequestBody 
     */
    public async ProductsListingCheckPost (xTtsAccessToken: string, contentType: string, isDiagnosisRequired?: boolean, shopCipher?: string, CheckProductListingRequestBody?: Product202309CheckProductListingRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309CheckProductListingResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/listing_check';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsListingCheckPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsListingCheckPost.');
        }

        if (isDiagnosisRequired !== undefined) {
            localVarQueryParameters['is_diagnosis_required'] = ObjectSerializer.serialize(isDiagnosisRequired, "boolean");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CheckProductListingRequestBody, "Product202309CheckProductListingRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309CheckProductListingResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309CheckProductListingResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Create and list products intended for sale exclusively in local shops. You can create products in `AVAILABLE` categories. (US sellers can also create products in INVITE_ONLY categories). After creation, it will be sent for audit review by TikTok Shop. Use the [Product status change](https://partner.tiktokshop.com/docv2/page/650956aff1fd3102b90b6261) webhook to keep track of the review status. **Note**:  - Before calling this API, we recommend that you prepare the necessary information by following the [usage flow for your region](650b23eef1fd3102b93d2326). - There\'s a limit to the number of products you can list per day. Refer to TikTok Shop Academy for details. - The language used in the product content must align with the target market\'s language (e.g. don\'t use Chinese characters), otherwise the listing will fail or be rejected. - Cross-border sellers can use the [Create Global Product API](https://partner.tiktokshop.com/docv2/page/6509de61bace3e02b7489cba) to create global products that can be listed and sold in multiple markets.
     * @summary CreateProduct
     * @param productId The product ID generated by TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param CreateProductRequestBody 
     */
    public async ProductsPost (productId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, CreateProductRequestBody?: Product202309CreateProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309CreateProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CreateProductRequestBody, "Product202309CreateProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309CreateProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309CreateProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve all properties of a product that is in the `DRAFT`, `PENDING`, or `ACTIVATE` status.
     * @summary GetProduct
     * @param productId The product ID in TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param returnUnderReviewVersion A flag to indicate what product information to retrieve if a live product (&#x60;ACTIVATE&#x60; status) is edited and resent for TikTok Shop review. - True: Retrieves the latest version of the product information that is currently under review. - False: Retrieves a snapshot of the product information that is live and online (before the edit). Default: False
     * @param shopCipher 
     */
    public async ProductsProductIdGet (productId: string, xTtsAccessToken: string, contentType: string, returnUnderReviewVersion?: boolean, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309GetProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/{product_id}'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsProductIdGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsProductIdGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsProductIdGet.');
        }

        if (returnUnderReviewVersion !== undefined) {
            localVarQueryParameters['return_under_review_version'] = ObjectSerializer.serialize(returnUnderReviewVersion, "boolean");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309GetProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309GetProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Update the inventory quantity of multiple SKUs belonging to a product in the `ACTIVATE` status.
     * @summary UpdateInventory
     * @param productId The product ID generated by TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param UpdateInventoryRequestBody 
     */
    public async ProductsProductIdInventoryUpdatePost (productId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, UpdateInventoryRequestBody?: Product202309UpdateInventoryRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309UpdateInventoryResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/{product_id}/inventory/update'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsProductIdInventoryUpdatePost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsProductIdInventoryUpdatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsProductIdInventoryUpdatePost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(UpdateInventoryRequestBody, "Product202309UpdateInventoryRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309UpdateInventoryResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309UpdateInventoryResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Edit a subset of the product properties (e.g. description, brand, images, attributes). After editing the product, the latest product content (referred to as v2) will be resent for audit review. If the audit passes, v2 is published to the shop, otherwise, the existing product stays live and remains unchanged (keeping v1). However, edits to the `price` or `inventory` fields do not require a reaudit and will be immediately published on the platform. Use the [Product status change](650956aff1fd3102b90b6261) webhook to keep track of the review status. **Note**:  - **Updates are handled per top-level property**, so all non-empty fields within an updated object must be supplied to prevent overwriting with blanks. For top-level properties (e.g. `description`, `brand_id`) that are not nested in an object, you can update them individually. Omitting these properties in the request will leave them unchanged. If you need to edit any nested property within an object, you must provide values for all nested properties of that object. Any omitted nested properties will be overwritten with blanks. For example, if you want to update `identifier_code.code`, you must also include the `identifier_code.type` property to avoid data loss for that property. - If new mandatory product attributes were added by TikTok Shop after the creation of your product, ensure that you provide these attributes too. **For Tokopedia sellers**: Note that a product can have **only one active version** across all platforms at any time. If a product is live on both platforms, audit results for the latest version are handled as follows: - **Mixed audit results**: If the product passes audit on one platform but fails on another, on the successful platform, the product will stay live and be updated with content from the latest version (v2), while on the failed platform, the product will be deactivated and hidden entirely. - **Audit failure on all platforms**: If the product fails audit on all platforms, the existing product stays live and remains unchanged (keeping v1).
     * @summary PartialEditProduct
     * @param productId The product ID in TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param PartialEditProductRequestBody 
     */
    public async ProductsProductIdPartialEditPost (productId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, PartialEditProductRequestBody?: Product202309PartialEditProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309PartialEditProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/{product_id}/partial_edit'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsProductIdPartialEditPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsProductIdPartialEditPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsProductIdPartialEditPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(PartialEditProductRequestBody, "Product202309PartialEditProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309PartialEditProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309PartialEditProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Update the price of multiple SKUs belonging to a product in the `ACTIVATE` status. **Note**: The `data` response field is always empty as there is no additional response data.
     * @summary UpdatePrice
     * @param productId The product ID generated by TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param UpdatePriceRequestBody 
     */
    public async ProductsProductIdPricesUpdatePost (productId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, UpdatePriceRequestBody?: Product202309UpdatePriceRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309UpdatePriceResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/{product_id}/prices/update'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsProductIdPricesUpdatePost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsProductIdPricesUpdatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsProductIdPricesUpdatePost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(UpdatePriceRequestBody, "Product202309UpdatePriceRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309UpdatePriceResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309UpdatePriceResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Edit all properties (e.g. description, brand, images, attributes) of a product. After editing the product, the latest product content (referred to as v2) will be resent for audit review. If the audit passes, v2 is published to the shop, otherwise, the existing product stays live and remains unchanged (keeping v1). However, edits to the `price` or `inventory` fields do not require a reaudit and will be immediately published on the platform. Use the [Product status change](https://partner.tiktokshop.com/docv2/page/650956aff1fd3102b90b6261) webhook to keep track of the review status. **Note**:  - All inputs, including blanks, in the request payload will overwrite existing values. To retain an existing value, make sure to include it in your request. Exceptions to this rule are the `price` and `inventory` fields, which will remain unchanged if they are omitted from the request. If you wish to edit only certain properties, you can use the [Partial Edit Product API](650a98d74a0bb702c06c3289), [Update Inventory API](6503068fc20ad60284b38858), or the [Update Price API](650307de5a12ff0294eac8b0). - The language used in the product content must align with the target market\'s language (e.g. don\'t use Chinese characters), otherwise the listing will fail or be rejected. **For Tokopedia sellers**: Note that a product can have **only one active version** across all platforms at any time. If a product is live on both platforms, audit results for the latest version are handled as follows: - **Mixed audit results**: If the product passes audit on one platform but fails on another, on the successful platform, the product will stay live and be updated with content from the latest version (v2), while on the failed platform, the product will be deactivated and hidden entirely. - **Audit failure on all platforms**: If the product fails audit on all platforms, the existing product stays live and remains unchanged (keeping v1).
     * @summary EditProduct
     * @param productId The product ID generated by TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param EditProductRequestBody 
     */
    public async ProductsProductIdPut (productId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, EditProductRequestBody?: Product202309EditProductRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309EditProductResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/{product_id}'
            .replace('{' + 'product_id' + '}', encodeURIComponent(String(productId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'productId' is not null or undefined
        if (productId === null || productId === undefined) {
            throw new Error('Required parameter productId was null or undefined when calling ProductsProductIdPut.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsProductIdPut.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsProductIdPut.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'PUT',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(EditProductRequestBody, "Product202309EditProductRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309EditProductResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309EditProductResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Recover products that are in the `Deleted` status. The status changes to `Seller_deactivated` after recovery.
     * @summary RecoverProducts
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param RecoverProductsRequestBody 
     */
    public async ProductsRecoverPost (xTtsAccessToken: string, contentType: string, shopCipher?: string, RecoverProductsRequestBody?: Product202309RecoverProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309RecoverProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/recover';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsRecoverPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsRecoverPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(RecoverProductsRequestBody, "Product202309RecoverProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309RecoverProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309RecoverProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieve a list of products that meet the specified conditions.  This API will only return the key product properties. You can pass a returned product ID to the [Get Product API](https://partner.tiktokshop.com/docv2/page/6509d85b4a0bb702c057fdda) to obtain more details about the product.
     * @summary SearchProducts
     * @param pageSize The number of results to be returned per page.  Valid range: [1-100]
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the &#x60;next_page_token&#x60; from a previous response. It is not needed for the first page.
     * @param categoryVersion Filter products by the category tree version. Possible values based on region: - US: &#x60;v2&#x60;, represents the 7-level category tree. - Other regions: &#x60;v1&#x60;, represents the 3-level category tree. Default: Return all products from both &#x60;v1&#x60; and &#x60;v2&#x60; category trees.
     * @param shopCipher 
     * @param SearchProductsRequestBody 
     */
    public async ProductsSearchPost (pageSize: number, xTtsAccessToken: string, contentType: string, pageToken?: string, categoryVersion?: string, shopCipher?: string, SearchProductsRequestBody?: Product202309SearchProductsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202309SearchProductsResponse;  }> {
        const localVarPath = this.basePath + '/product/202309/products/search';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'pageSize' is not null or undefined
        if (pageSize === null || pageSize === undefined) {
            throw new Error('Required parameter pageSize was null or undefined when calling ProductsSearchPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ProductsSearchPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ProductsSearchPost.');
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "number");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(SearchProductsRequestBody, "Product202309SearchProductsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202309SearchProductsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202309SearchProductsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
}

export const ProductV202309ApiOperationNames = {
    BrandsGet: 'BrandsGet',BrandsPost: 'BrandsPost',CategoriesCategoryIdAttributesGet: 'CategoriesCategoryIdAttributesGet',CategoriesCategoryIdGlobalAttributesGet: 'CategoriesCategoryIdGlobalAttributesGet',CategoriesCategoryIdGlobalRulesGet: 'CategoriesCategoryIdGlobalRulesGet',CategoriesCategoryIdRulesGet: 'CategoriesCategoryIdRulesGet',CategoriesGet: 'CategoriesGet',CategoriesRecommendPost: 'CategoriesRecommendPost',FilesUploadPost: 'FilesUploadPost',GlobalCategoriesGet: 'GlobalCategoriesGet',GlobalCategoriesRecommendPost: 'GlobalCategoriesRecommendPost',GlobalProductsDelete: 'GlobalProductsDelete',GlobalProductsGlobalProductIdGet: 'GlobalProductsGlobalProductIdGet',GlobalProductsGlobalProductIdInventoryUpdatePost: 'GlobalProductsGlobalProductIdInventoryUpdatePost',GlobalProductsGlobalProductIdPublishPost: 'GlobalProductsGlobalProductIdPublishPost',GlobalProductsGlobalProductIdPut: 'GlobalProductsGlobalProductIdPut',GlobalProductsPost: 'GlobalProductsPost',GlobalProductsSearchPost: 'GlobalProductsSearchPost',ImagesUploadPost: 'ImagesUploadPost',InventorySearchPost: 'InventorySearchPost',PrerequisitesGet: 'PrerequisitesGet',ProductsActivatePost: 'ProductsActivatePost',ProductsDeactivatePost: 'ProductsDeactivatePost',ProductsDelete: 'ProductsDelete',ProductsListingCheckPost: 'ProductsListingCheckPost',ProductsPost: 'ProductsPost',ProductsProductIdGet: 'ProductsProductIdGet',ProductsProductIdInventoryUpdatePost: 'ProductsProductIdInventoryUpdatePost',ProductsProductIdPartialEditPost: 'ProductsProductIdPartialEditPost',ProductsProductIdPricesUpdatePost: 'ProductsProductIdPricesUpdatePost',ProductsProductIdPut: 'ProductsProductIdPut',ProductsRecoverPost: 'ProductsRecoverPost',ProductsSearchPost: 'ProductsSearchPost',
} as const


export type ProductV202309ApiOperationTypes = {
    BrandsGet: ProductV202309Api['BrandsGet'];BrandsPost: ProductV202309Api['BrandsPost'];CategoriesCategoryIdAttributesGet: ProductV202309Api['CategoriesCategoryIdAttributesGet'];CategoriesCategoryIdGlobalAttributesGet: ProductV202309Api['CategoriesCategoryIdGlobalAttributesGet'];CategoriesCategoryIdGlobalRulesGet: ProductV202309Api['CategoriesCategoryIdGlobalRulesGet'];CategoriesCategoryIdRulesGet: ProductV202309Api['CategoriesCategoryIdRulesGet'];CategoriesGet: ProductV202309Api['CategoriesGet'];CategoriesRecommendPost: ProductV202309Api['CategoriesRecommendPost'];FilesUploadPost: ProductV202309Api['FilesUploadPost'];GlobalCategoriesGet: ProductV202309Api['GlobalCategoriesGet'];GlobalCategoriesRecommendPost: ProductV202309Api['GlobalCategoriesRecommendPost'];GlobalProductsDelete: ProductV202309Api['GlobalProductsDelete'];GlobalProductsGlobalProductIdGet: ProductV202309Api['GlobalProductsGlobalProductIdGet'];GlobalProductsGlobalProductIdInventoryUpdatePost: ProductV202309Api['GlobalProductsGlobalProductIdInventoryUpdatePost'];GlobalProductsGlobalProductIdPublishPost: ProductV202309Api['GlobalProductsGlobalProductIdPublishPost'];GlobalProductsGlobalProductIdPut: ProductV202309Api['GlobalProductsGlobalProductIdPut'];GlobalProductsPost: ProductV202309Api['GlobalProductsPost'];GlobalProductsSearchPost: ProductV202309Api['GlobalProductsSearchPost'];ImagesUploadPost: ProductV202309Api['ImagesUploadPost'];InventorySearchPost: ProductV202309Api['InventorySearchPost'];PrerequisitesGet: ProductV202309Api['PrerequisitesGet'];ProductsActivatePost: ProductV202309Api['ProductsActivatePost'];ProductsDeactivatePost: ProductV202309Api['ProductsDeactivatePost'];ProductsDelete: ProductV202309Api['ProductsDelete'];ProductsListingCheckPost: ProductV202309Api['ProductsListingCheckPost'];ProductsPost: ProductV202309Api['ProductsPost'];ProductsProductIdGet: ProductV202309Api['ProductsProductIdGet'];ProductsProductIdInventoryUpdatePost: ProductV202309Api['ProductsProductIdInventoryUpdatePost'];ProductsProductIdPartialEditPost: ProductV202309Api['ProductsProductIdPartialEditPost'];ProductsProductIdPricesUpdatePost: ProductV202309Api['ProductsProductIdPricesUpdatePost'];ProductsProductIdPut: ProductV202309Api['ProductsProductIdPut'];ProductsRecoverPost: ProductV202309Api['ProductsRecoverPost'];ProductsSearchPost: ProductV202309Api['ProductsSearchPost'];
};

