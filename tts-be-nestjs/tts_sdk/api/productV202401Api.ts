/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import localVarRequest from 'request';
import http from 'http';

/* tslint:disable:no-unused-locals */
import { Product202401ListingSchemasResponse } from '../model/product/V202401/ListingSchemasResponse';

import { ObjectSerializer, Authentication, VoidAuth, Interceptor } from '../model/models';

import { HttpError, RequestFile } from './apis';

let defaultBasePath = 'https://open-api.tiktokglobalshop.com';

// ===============================================
// This file is autogenerated - Please do not edit
// ===============================================

export enum ProductV202401ApiApiKeys {
}

export class ProductV202401Api {
    protected _basePath = defaultBasePath;
    protected _defaultHeaders : any = {};
    protected _useQuerystring : boolean = false;

    static readonly apiName = 'ProductV202401Api' as const;

    protected authentications = {
        'default': <Authentication>new VoidAuth(),
    }

    protected interceptors: Interceptor[] = [];

    constructor(basePath?: string);
    constructor(basePathOrUsername: string, password?: string, basePath?: string) {
        if (password) {
            if (basePath) {
                this.basePath = basePath;
            }
        } else {
            if (basePathOrUsername) {
                this.basePath = basePathOrUsername
            }
        }
    }

    set useQuerystring(value: boolean) {
        this._useQuerystring = value;
    }

    set basePath(basePath: string) {
        this._basePath = basePath;
    }

    set defaultHeaders(defaultHeaders: any) {
        this._defaultHeaders = defaultHeaders;
    }

    get defaultHeaders() {
        return this._defaultHeaders;
    }

    get basePath() {
        return this._basePath;
    }

    public setDefaultAuthentication(auth: Authentication) {
        this.authentications.default = auth;
    }

    public setApiKey(key: ProductV202401ApiApiKeys, value: string) {
        (this.authentications as any)[ProductV202401ApiApiKeys[key]].apiKey = value;
    }

    public addInterceptor(interceptor: Interceptor) {
        this.interceptors.push(interceptor);
    }

    /**
     * The interface returns the field requirements for creating a product. By providing the leaf category ID, you can obtain the field information and input methods for the product creation requirements.
     * @summary ListingSchemas
     * @param categoryIds The interface returns the field requirements for creating a product. By providing the leaf category ID, you can obtain the field information and input methods for the product creation requirements.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale Category information will be returned in the corresponding language based on the specified locale. If no locale is provided, the default locale of the store will be used.  The currently supported locales include: en-GB, en-US, id-ID, ms-MY, th-TH, vi-VN, zh-CN. Use BCP-47 language codes, such as \&#39;en-US\&#39; or \&#39;id\&#39;. For more details, please refer to http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
     * @param categoryVersion The version id of the category tree.The new version id is \&quot;v2\&quot; and will return data from our new 7-level category tree.The old version id is \&quot;v1\&quot; and will return data from the current 3-level category tree.The old version of category data will be given by default.
     */
    public async ListingSchemasGet (categoryIds: Array<number>, xTtsAccessToken: string, contentType: string, locale?: string, categoryVersion?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Product202401ListingSchemasResponse;  }> {
        const localVarPath = this.basePath + '/product/202401/listing_schemas';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'categoryIds' is not null or undefined
        if (categoryIds === null || categoryIds === undefined) {
            throw new Error('Required parameter categoryIds was null or undefined when calling ListingSchemasGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ListingSchemasGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ListingSchemasGet.');
        }

        if (categoryIds !== undefined) {
            localVarQueryParameters['category_ids'] = ObjectSerializer.serialize(categoryIds, "Array<number>");
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (categoryVersion !== undefined) {
            localVarQueryParameters['category_version'] = ObjectSerializer.serialize(categoryVersion, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Product202401ListingSchemasResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Product202401ListingSchemasResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
}

export const ProductV202401ApiOperationNames = {
    ListingSchemasGet: 'ListingSchemasGet',
} as const


export type ProductV202401ApiOperationTypes = {
    ListingSchemasGet: ProductV202401Api['ListingSchemasGet'];
};

