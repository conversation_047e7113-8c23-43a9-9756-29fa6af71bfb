/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import localVarRequest from 'request';
import http from 'http';

/* tslint:disable:no-unused-locals */
import { Finance202309GetOrderStatementTransactionsResponse } from '../model/finance/V202309/GetOrderStatementTransactionsResponse';
import { Finance202309GetPaymentsResponse } from '../model/finance/V202309/GetPaymentsResponse';
import { Finance202309GetStatementTransactionsResponse } from '../model/finance/V202309/GetStatementTransactionsResponse';
import { Finance202309GetStatementsResponse } from '../model/finance/V202309/GetStatementsResponse';
import { Finance202309GetWithdrawalsResponse } from '../model/finance/V202309/GetWithdrawalsResponse';

import { ObjectSerializer, Authentication, VoidAuth, Interceptor } from '../model/models';

import { HttpError, RequestFile } from './apis';

let defaultBasePath = 'https://open-api.tiktokglobalshop.com';

// ===============================================
// This file is autogenerated - Please do not edit
// ===============================================

export enum FinanceV202309ApiApiKeys {
}

export class FinanceV202309Api {
    protected _basePath = defaultBasePath;
    protected _defaultHeaders : any = {};
    protected _useQuerystring : boolean = false;

    static readonly apiName = 'FinanceV202309Api' as const;

    protected authentications = {
        'default': <Authentication>new VoidAuth(),
    }

    protected interceptors: Interceptor[] = [];

    constructor(basePath?: string);
    constructor(basePathOrUsername: string, password?: string, basePath?: string) {
        if (password) {
            if (basePath) {
                this.basePath = basePath;
            }
        } else {
            if (basePathOrUsername) {
                this.basePath = basePathOrUsername
            }
        }
    }

    set useQuerystring(value: boolean) {
        this._useQuerystring = value;
    }

    set basePath(basePath: string) {
        this._basePath = basePath;
    }

    set defaultHeaders(defaultHeaders: any) {
        this._defaultHeaders = defaultHeaders;
    }

    get defaultHeaders() {
        return this._defaultHeaders;
    }

    get basePath() {
        return this._basePath;
    }

    public setDefaultAuthentication(auth: Authentication) {
        this.authentications.default = auth;
    }

    public setApiKey(key: FinanceV202309ApiApiKeys, value: string) {
        (this.authentications as any)[FinanceV202309ApiApiKeys[key]].apiKey = value;
    }

    public addInterceptor(interceptor: Interceptor) {
        this.interceptors.push(interceptor);
    }

    /**
     * **This API is currently exclusive to the following markets: US, UK.** Retrieves the transactions associated with an order, including both order-level transactions and SKU-level detailed transactions. This covers all transactions related to sales, fees, commissions, shipping, taxes, adjustments, and refunds.
     * @summary GetOrderStatementTransactions
     * @param orderId The order ID in TikTok Shop.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     */
    public async OrdersOrderIdStatementTransactionsGet (orderId: string, xTtsAccessToken: string, contentType: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Finance202309GetOrderStatementTransactionsResponse;  }> {
        const localVarPath = this.basePath + '/finance/202309/orders/{order_id}/statement_transactions'
            .replace('{' + 'order_id' + '}', encodeURIComponent(String(orderId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'orderId' is not null or undefined
        if (orderId === null || orderId === undefined) {
            throw new Error('Required parameter orderId was null or undefined when calling OrdersOrderIdStatementTransactionsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling OrdersOrderIdStatementTransactionsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling OrdersOrderIdStatementTransactionsGet.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Finance202309GetOrderStatementTransactionsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Finance202309GetOrderStatementTransactionsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * **This API is currently unavailable to SEA markets.** Retrieves records of automated payments for a shop based on a specified date range. Use the returned list to verify and reconcile payments with the transactions in the seller\'s bank account.
     * @summary GetPayments
     * @param sortField The returned results will be sorted by the specified field. Only supports &#x60;create_time&#x60;.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param createTimeLt Filter payments to show only those that occurred before the specified date and time. Unix timestamp. Refer to notes in &#x60;create_time_ge&#x60; for more usage information.
     * @param pageSize The number of results to be returned per page.  Default: 20 Valid range: [1-100]
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the &#x60;next_page_token&#x60; from a previous response. It is not needed for the first page.
     * @param sortOrder The sort order for the &#x60;sort_field&#x60; parameter.  Default: ASC  Possible values: - ASC: Ascending order - DESC: Descending order
     * @param createTimeGe Filter payments to show only those that occurred on or after the specified date and time. Unix timestamp.  **Note:** &#x60;create_time_ge&#x60; and &#x60;create_time_lt&#x60; together constitute the creation time filter condition. - If &#x60;create_time_ge&#x60; is filled but &#x60;create_time_lt&#x60; is empty, &#x60;create_time_lt&#x60; will default to the current time. - If &#x60;create_time_lt&#x60; is filled but &#x60;create_time_ge&#x60; is empty, &#x60;create_time_ge&#x60; will default to the earliest shop time.
     * @param shopCipher 
     */
    public async PaymentsGet (sortField: string, xTtsAccessToken: string, contentType: string, createTimeLt?: number, pageSize?: any, pageToken?: string, sortOrder?: string, createTimeGe?: number, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Finance202309GetPaymentsResponse;  }> {
        const localVarPath = this.basePath + '/finance/202309/payments';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'sortField' is not null or undefined
        if (sortField === null || sortField === undefined) {
            throw new Error('Required parameter sortField was null or undefined when calling PaymentsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling PaymentsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling PaymentsGet.');
        }

        if (createTimeLt !== undefined) {
            localVarQueryParameters['create_time_lt'] = ObjectSerializer.serialize(createTimeLt, "number");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "any");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (createTimeGe !== undefined) {
            localVarQueryParameters['create_time_ge'] = ObjectSerializer.serialize(createTimeGe, "number");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Finance202309GetPaymentsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Finance202309GetPaymentsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Retrieves the statements generated for a shop and the key statement information based on a specified date range or their payment status. Use this API to get an overview of your daily statements over a range of time, or to find out which statements have been paid or not. For the detailed transactions, refer to [Get Statement Transactions](650a6749defece02be67da87) or [Get Order Statement Transactions](650a6734defece02be67d724). Applicable for all regions\' sellers. Only data after 2023-07-01 is available.
     * @summary GetStatements
     * @param sortField The returned results will be sorted by the specified field. Only supports &#x60;statement_time&#x60;.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param statementTimeLt Filter statements to show only those that are generated before the specified date and time. Unix timestamp. Refer to notes in &#x60;statement_time_ge&#x60; for more usage information.
     * @param paymentStatus Filter statements based on the payment status. Possible values: - PAID: Payment has been transferred to the seller. - FAILED: Payment transfer failed. - PROCESSING: Payment is currently being processed. Default: All statuses are returned.
     * @param pageSize The number of results to be returned per page.  Default: 20 Valid range: [1-100]
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the &#x60;next_page_token&#x60; from a previous response. It is not needed for the first page.
     * @param sortOrder The sort order for the &#x60;sort_field&#x60; parameter.  Default: ASC  Possible values: - ASC: Ascending order - DESC: Descending order
     * @param statementTimeGe Filter statements to show only those that are generated on or after the specified date and time. Unix timestamp.  **Note:** &#x60;statement_time_ge&#x60; and &#x60;statement_time_le&#x60; together constitute the creation time filter condition. - If &#x60;statement_time_ge&#x60; is filled but &#x60;statement_time_lt&#x60; is empty, &#x60;statement_time_lt&#x60; will default to the current time. - If &#x60;statement_time_lt&#x60; is filled but &#x60;statement_time_ge&#x60; is empty, &#x60;statement_time_ge&#x60; will default to the earliest shop time.  **Example:** As statements are generated daily at 00:00 UTC, to retrieve statements for the period from Oct 5 to Oct 10, configure the parameters as follows: - Set &#x60;statement_time_ge&#x60; to 00:00 on Oct 6  or any time on Oct 5 (excluding 00:00). - Set &#x60;statement_time_lt&#x60; to any time on Oct 11 (excluding 00:00).
     * @param shopCipher 
     */
    public async StatementsGet (sortField: string, xTtsAccessToken: string, contentType: string, statementTimeLt?: number, paymentStatus?: string, pageSize?: any, pageToken?: string, sortOrder?: string, statementTimeGe?: number, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Finance202309GetStatementsResponse;  }> {
        const localVarPath = this.basePath + '/finance/202309/statements';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'sortField' is not null or undefined
        if (sortField === null || sortField === undefined) {
            throw new Error('Required parameter sortField was null or undefined when calling StatementsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling StatementsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling StatementsGet.');
        }

        if (statementTimeLt !== undefined) {
            localVarQueryParameters['statement_time_lt'] = ObjectSerializer.serialize(statementTimeLt, "number");
        }

        if (paymentStatus !== undefined) {
            localVarQueryParameters['payment_status'] = ObjectSerializer.serialize(paymentStatus, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "any");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (statementTimeGe !== undefined) {
            localVarQueryParameters['statement_time_ge'] = ObjectSerializer.serialize(statementTimeGe, "number");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Finance202309GetStatementsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Finance202309GetStatementsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Only for UK and US local sellers. Get a list of transactions based on statement_id. We will return a list of orders. If you require the SKU level transaction details, pass in the order_id to Get Order Statement Transactions.
     * @summary GetStatementTransactions
     * @param statementId The unique id of statement
     * @param sortField Only support: order_create_time
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param pageToken The default is empty string
     * @param sortOrder The default is ASC, the developer can choose ASC or DESC
     * @param pageSize The default is 20. It must be a positive integer,the range is 1-100
     * @param shopCipher 
     */
    public async StatementsStatementIdStatementTransactionsGet (statementId: string, sortField: string, xTtsAccessToken: string, contentType: string, pageToken?: string, sortOrder?: string, pageSize?: any, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Finance202309GetStatementTransactionsResponse;  }> {
        const localVarPath = this.basePath + '/finance/202309/statements/{statement_id}/statement_transactions'
            .replace('{' + 'statement_id' + '}', encodeURIComponent(String(statementId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'statementId' is not null or undefined
        if (statementId === null || statementId === undefined) {
            throw new Error('Required parameter statementId was null or undefined when calling StatementsStatementIdStatementTransactionsGet.');
        }

        // verify required parameter 'sortField' is not null or undefined
        if (sortField === null || sortField === undefined) {
            throw new Error('Required parameter sortField was null or undefined when calling StatementsStatementIdStatementTransactionsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling StatementsStatementIdStatementTransactionsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling StatementsStatementIdStatementTransactionsGet.');
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "any");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Finance202309GetStatementTransactionsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Finance202309GetStatementTransactionsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Get the list of the withdrawal records (when Seller\'s withdraw money from TikTokShop) based on the specified date range. 
     * @summary GetWithdrawals
     * @param types The type of transaction. Possible values: - WITHDRAW：The action of the seller to receive the settlement amount to the bank card through the action of withdrawal - SETTLE：The platform settles the amount to the seller - TRANSFER：Platform subsidies or deductions due to platform policies - REVERSE：Withdrawal failure due to incorrect bank card 
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param createTimeLt Unix timestamp representing the end of transactions time range one wants to request
     * @param pageSize The default is 20, it must be positive integer,the range is 1-100
     * @param pageToken The next page token
     * @param createTimeGe Unix timestamp representing the start of transactions time range one wants to request
     * @param shopCipher 
     */
    public async WithdrawalsGet (types: Array<string>, xTtsAccessToken: string, contentType: string, createTimeLt?: number, pageSize?: number, pageToken?: string, createTimeGe?: number, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: Finance202309GetWithdrawalsResponse;  }> {
        const localVarPath = this.basePath + '/finance/202309/withdrawals';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'types' is not null or undefined
        if (types === null || types === undefined) {
            throw new Error('Required parameter types was null or undefined when calling WithdrawalsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling WithdrawalsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling WithdrawalsGet.');
        }

        if (createTimeLt !== undefined) {
            localVarQueryParameters['create_time_lt'] = ObjectSerializer.serialize(createTimeLt, "number");
        }

        if (types !== undefined) {
            localVarQueryParameters['types'] = ObjectSerializer.serialize(types, "Array<string>");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "number");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (createTimeGe !== undefined) {
            localVarQueryParameters['create_time_ge'] = ObjectSerializer.serialize(createTimeGe, "number");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: Finance202309GetWithdrawalsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "Finance202309GetWithdrawalsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
}

export const FinanceV202309ApiOperationNames = {
    OrdersOrderIdStatementTransactionsGet: 'OrdersOrderIdStatementTransactionsGet',PaymentsGet: 'PaymentsGet',StatementsGet: 'StatementsGet',StatementsStatementIdStatementTransactionsGet: 'StatementsStatementIdStatementTransactionsGet',WithdrawalsGet: 'WithdrawalsGet',
} as const


export type FinanceV202309ApiOperationTypes = {
    OrdersOrderIdStatementTransactionsGet: FinanceV202309Api['OrdersOrderIdStatementTransactionsGet'];PaymentsGet: FinanceV202309Api['PaymentsGet'];StatementsGet: FinanceV202309Api['StatementsGet'];StatementsStatementIdStatementTransactionsGet: FinanceV202309Api['StatementsStatementIdStatementTransactionsGet'];WithdrawalsGet: FinanceV202309Api['WithdrawalsGet'];
};

