/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import localVarRequest from 'request';
import http from 'http';

/* tslint:disable:no-unused-locals */
import { ReturnRefund202309ApproveCancellationResponse } from '../model/returnRefund/V202309/ApproveCancellationResponse';
import { ReturnRefund202309ApproveReturnRequestBody } from '../model/returnRefund/V202309/ApproveReturnRequestBody';
import { ReturnRefund202309ApproveReturnResponse } from '../model/returnRefund/V202309/ApproveReturnResponse';
import { ReturnRefund202309CalculateRefundRequestBody } from '../model/returnRefund/V202309/CalculateRefundRequestBody';
import { ReturnRefund202309CalculateRefundResponse } from '../model/returnRefund/V202309/CalculateRefundResponse';
import { ReturnRefund202309CancelOrderRequestBody } from '../model/returnRefund/V202309/CancelOrderRequestBody';
import { ReturnRefund202309CancelOrderResponse } from '../model/returnRefund/V202309/CancelOrderResponse';
import { ReturnRefund202309CreateReturnRequestBody } from '../model/returnRefund/V202309/CreateReturnRequestBody';
import { ReturnRefund202309CreateReturnResponse } from '../model/returnRefund/V202309/CreateReturnResponse';
import { ReturnRefund202309GetAftersaleEligibilityResponse } from '../model/returnRefund/V202309/GetAftersaleEligibilityResponse';
import { ReturnRefund202309GetRejectReasonsResponse } from '../model/returnRefund/V202309/GetRejectReasonsResponse';
import { ReturnRefund202309GetReturnRecordsResponse } from '../model/returnRefund/V202309/GetReturnRecordsResponse';
import { ReturnRefund202309RejectCancellationRequestBody } from '../model/returnRefund/V202309/RejectCancellationRequestBody';
import { ReturnRefund202309RejectCancellationResponse } from '../model/returnRefund/V202309/RejectCancellationResponse';
import { ReturnRefund202309RejectReturnRequestBody } from '../model/returnRefund/V202309/RejectReturnRequestBody';
import { ReturnRefund202309RejectReturnResponse } from '../model/returnRefund/V202309/RejectReturnResponse';
import { ReturnRefund202309SearchCancellationsRequestBody } from '../model/returnRefund/V202309/SearchCancellationsRequestBody';
import { ReturnRefund202309SearchCancellationsResponse } from '../model/returnRefund/V202309/SearchCancellationsResponse';
import { ReturnRefund202309SearchReturnsRequestBody } from '../model/returnRefund/V202309/SearchReturnsRequestBody';
import { ReturnRefund202309SearchReturnsResponse } from '../model/returnRefund/V202309/SearchReturnsResponse';

import { ObjectSerializer, Authentication, VoidAuth, Interceptor } from '../model/models';

import { HttpError, RequestFile } from './apis';

let defaultBasePath = 'https://open-api.tiktokglobalshop.com';

// ===============================================
// This file is autogenerated - Please do not edit
// ===============================================

export enum ReturnRefundV202309ApiApiKeys {
}

export class ReturnRefundV202309Api {
    protected _basePath = defaultBasePath;
    protected _defaultHeaders : any = {};
    protected _useQuerystring : boolean = false;

    static readonly apiName = 'ReturnRefundV202309Api' as const;

    protected authentications = {
        'default': <Authentication>new VoidAuth(),
    }

    protected interceptors: Interceptor[] = [];

    constructor(basePath?: string);
    constructor(basePathOrUsername: string, password?: string, basePath?: string) {
        if (password) {
            if (basePath) {
                this.basePath = basePath;
            }
        } else {
            if (basePathOrUsername) {
                this.basePath = basePathOrUsername
            }
        }
    }

    set useQuerystring(value: boolean) {
        this._useQuerystring = value;
    }

    set basePath(basePath: string) {
        this._basePath = basePath;
    }

    set defaultHeaders(defaultHeaders: any) {
        this._defaultHeaders = defaultHeaders;
    }

    get defaultHeaders() {
        return this._defaultHeaders;
    }

    get basePath() {
        return this._basePath;
    }

    public setDefaultAuthentication(auth: Authentication) {
        this.authentications.default = auth;
    }

    public setApiKey(key: ReturnRefundV202309ApiApiKeys, value: string) {
        (this.authentications as any)[ReturnRefundV202309ApiApiKeys[key]].apiKey = value;
    }

    public addInterceptor(interceptor: Interceptor) {
        this.interceptors.push(interceptor);
    }

    /**
     * Use this API to approve a buyer\'s order cancellation request.
     * @summary ApproveCancellation
     * @param cancelId The identifier of a specific cancellation request.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param idempotencyKey The idempotency key is a unique value generated by the client which the server uses to recognize a request.   You may choose your own method of creating unique keys. We suggest using UUIDs, or another random string with enough entropy, to avoid collisions.   Idempotency keys can be up to 255 characters long.
     * @param shopCipher 
     */
    public async CancellationsCancelIdApprovePost (cancelId: string, xTtsAccessToken: string, contentType: string, idempotencyKey?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309ApproveCancellationResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/cancellations/{cancel_id}/approve'
            .replace('{' + 'cancel_id' + '}', encodeURIComponent(String(cancelId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'cancelId' is not null or undefined
        if (cancelId === null || cancelId === undefined) {
            throw new Error('Required parameter cancelId was null or undefined when calling CancellationsCancelIdApprovePost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CancellationsCancelIdApprovePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CancellationsCancelIdApprovePost.');
        }

        if (idempotencyKey !== undefined) {
            localVarQueryParameters['idempotency_key'] = ObjectSerializer.serialize(idempotencyKey, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309ApproveCancellationResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309ApproveCancellationResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to reject a buyer\'s order cancellation request.
     * @summary RejectCancellation
     * @param cancelId The identifier of a specific cancellation request.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param idempotencyKey The idempotency key is a unique value generated by the client which the server uses to recognize a request.   You may choose your own method of creating unique keys. We suggest using UUIDs, or another random string with enough entropy, to avoid collisions.   Idempotency keys can be up to 255 characters long.
     * @param shopCipher 
     * @param RejectCancellationRequestBody 
     */
    public async CancellationsCancelIdRejectPost (cancelId: string, xTtsAccessToken: string, contentType: string, idempotencyKey?: string, shopCipher?: string, RejectCancellationRequestBody?: ReturnRefund202309RejectCancellationRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309RejectCancellationResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/cancellations/{cancel_id}/reject'
            .replace('{' + 'cancel_id' + '}', encodeURIComponent(String(cancelId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'cancelId' is not null or undefined
        if (cancelId === null || cancelId === undefined) {
            throw new Error('Required parameter cancelId was null or undefined when calling CancellationsCancelIdRejectPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CancellationsCancelIdRejectPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CancellationsCancelIdRejectPost.');
        }

        if (idempotencyKey !== undefined) {
            localVarQueryParameters['idempotency_key'] = ObjectSerializer.serialize(idempotencyKey, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(RejectCancellationRequestBody, "ReturnRefund202309RejectCancellationRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309RejectCancellationResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309RejectCancellationResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to cancel an order on behalf of a seller. In the US and UK markets, when an item is out of stock, partial cancellation on the single item level is supported by this API.
     * @summary CancelOrder
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param CancelOrderRequestBody 
     */
    public async CancellationsPost (xTtsAccessToken: string, contentType: string, shopCipher?: string, CancelOrderRequestBody?: ReturnRefund202309CancelOrderRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CancelOrderResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/cancellations';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CancellationsPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CancellationsPost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CancelOrderRequestBody, "ReturnRefund202309CancelOrderRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CancelOrderResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309CancelOrderResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to search and retrieve one or more order cancellations.
     * @summary SearchCancellations
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param sortField The returned results will be sorted by the specified field.  Default: create_time Possible values: - create_time - update_time  Specify the order for sorting the returned results by using the sort_order parameter.
     * @param sortOrder The sort order for the sort_field parameter.  Default: ASC Possible values: - ASC: Ascending order - DESC: Descending order
     * @param pageSize The number of results to be returned per page.  Default: 10.  Valid range: [1-50].
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the next_page_token from a previous response. It is not needed for the first page.
     * @param shopCipher 
     * @param SearchCancellationsRequestBody 
     */
    public async CancellationsSearchPost (xTtsAccessToken: string, contentType: string, sortField?: string, sortOrder?: string, pageSize?: string, pageToken?: string, shopCipher?: string, SearchCancellationsRequestBody?: ReturnRefund202309SearchCancellationsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309SearchCancellationsResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/cancellations/search';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling CancellationsSearchPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling CancellationsSearchPost.');
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "string");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(SearchCancellationsRequestBody, "ReturnRefund202309SearchCancellationsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309SearchCancellationsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309SearchCancellationsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to check eligible aftersale solutions for an order, including whether the seller or buyer can initiate a refund, return, or cancel a specific order.
     * @summary GetAftersaleEligibility
     * @param orderId The unique identifier for a TikTok Shop order.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param initiateAftersaleUser The type of user you would like to check aftersale options for. Default: SELLER Possible values: - SELLER - BUYER
     * @param shopCipher 
     */
    public async OrdersOrderIdAftersaleEligibilityGet (orderId: string, xTtsAccessToken: string, contentType: string, initiateAftersaleUser?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetAftersaleEligibilityResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/orders/{order_id}/aftersale_eligibility'
            .replace('{' + 'order_id' + '}', encodeURIComponent(String(orderId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'orderId' is not null or undefined
        if (orderId === null || orderId === undefined) {
            throw new Error('Required parameter orderId was null or undefined when calling OrdersOrderIdAftersaleEligibilityGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling OrdersOrderIdAftersaleEligibilityGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling OrdersOrderIdAftersaleEligibilityGet.');
        }

        if (initiateAftersaleUser !== undefined) {
            localVarQueryParameters['initiate_aftersale_user'] = ObjectSerializer.serialize(initiateAftersaleUser, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetAftersaleEligibilityResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309GetAftersaleEligibilityResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to check order refundable amounts.
     * @summary CalculateRefund
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param shopCipher 
     * @param CalculateRefundRequestBody 
     */
    public async RefundsCalculatePost (xTtsAccessToken: string, contentType: string, shopCipher?: string, CalculateRefundRequestBody?: ReturnRefund202309CalculateRefundRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CalculateRefundResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/refunds/calculate';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling RefundsCalculatePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling RefundsCalculatePost.');
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CalculateRefundRequestBody, "ReturnRefund202309CalculateRefundRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CalculateRefundResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309CalculateRefundResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to obtain order return or cancellation rejection reasons. The seller is required to provide a reason when they reject a cancel, refund, or return request.
     * @summary GetRejectReasons
     * @param returnOrCancelId The unique identifier for an order return or cancellation.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying the rejection reason, delimited by commas. Default: en-US Refer to [Locale codes](678e3a47bae28f030a8c7523) for the list of supported locale codes.
     * @param shopCipher 
     */
    public async RejectReasonsGet (returnOrCancelId: string, xTtsAccessToken: string, contentType: string, locale?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetRejectReasonsResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/reject_reasons';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'returnOrCancelId' is not null or undefined
        if (returnOrCancelId === null || returnOrCancelId === undefined) {
            throw new Error('Required parameter returnOrCancelId was null or undefined when calling RejectReasonsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling RejectReasonsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling RejectReasonsGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (returnOrCancelId !== undefined) {
            localVarQueryParameters['return_or_cancel_id'] = ObjectSerializer.serialize(returnOrCancelId, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetRejectReasonsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309GetRejectReasonsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to initiate a return request on behalf of the buyer Sellers can reject the request, or accept and issue: -Return and Refund (buyer must send package back) -Returnless Refund (buyer can keep the item) -Partial Refund (Seller issues a partial refund, buyer can keep the item)
     * @summary CreateReturn
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param idempotencyKey Idempotency Key
     * @param shopCipher 
     * @param CreateReturnRequestBody 
     */
    public async ReturnsPost (xTtsAccessToken: string, contentType: string, idempotencyKey?: string, shopCipher?: string, CreateReturnRequestBody?: ReturnRefund202309CreateReturnRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CreateReturnResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/returns';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ReturnsPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ReturnsPost.');
        }

        if (idempotencyKey !== undefined) {
            localVarQueryParameters['idempotency_key'] = ObjectSerializer.serialize(idempotencyKey, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(CreateReturnRequestBody, "ReturnRefund202309CreateReturnRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309CreateReturnResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309CreateReturnResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to approve a buyer\'s return request.
     * @summary ApproveReturn
     * @param returnId The identifier of a specific return request.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param idempotencyKey The idempotency key is a unique value generated by the client which the server uses to recognize the same request. How you create unique keys is up to you, but we suggest using UUIDs, or another random string with enough entropy to avoid collisions. Idempotency keys can be up to 255 characters long.
     * @param shopCipher 
     * @param ApproveReturnRequestBody 
     */
    public async ReturnsReturnIdApprovePost (returnId: string, xTtsAccessToken: string, contentType: string, idempotencyKey?: string, shopCipher?: string, ApproveReturnRequestBody?: ReturnRefund202309ApproveReturnRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309ApproveReturnResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/returns/{return_id}/approve'
            .replace('{' + 'return_id' + '}', encodeURIComponent(String(returnId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'returnId' is not null or undefined
        if (returnId === null || returnId === undefined) {
            throw new Error('Required parameter returnId was null or undefined when calling ReturnsReturnIdApprovePost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ReturnsReturnIdApprovePost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ReturnsReturnIdApprovePost.');
        }

        if (idempotencyKey !== undefined) {
            localVarQueryParameters['idempotency_key'] = ObjectSerializer.serialize(idempotencyKey, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(ApproveReturnRequestBody, "ReturnRefund202309ApproveReturnRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309ApproveReturnResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309ApproveReturnResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to get a list of return records.
     * @summary GetReturnRecords
     * @param returnId A unique identifier for a TikTok Shop return request.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param locale The BCP-47 locale codes for displaying the return reason, delimited by commas. Default: en-US Refer to [Locale codes](678e3a47bae28f030a8c7523) for the list of supported locale codes.
     * @param shopCipher 
     */
    public async ReturnsReturnIdRecordsGet (returnId: string, xTtsAccessToken: string, contentType: string, locale?: string, shopCipher?: string, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetReturnRecordsResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/returns/{return_id}/records'
            .replace('{' + 'return_id' + '}', encodeURIComponent(String(returnId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'returnId' is not null or undefined
        if (returnId === null || returnId === undefined) {
            throw new Error('Required parameter returnId was null or undefined when calling ReturnsReturnIdRecordsGet.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ReturnsReturnIdRecordsGet.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ReturnsReturnIdRecordsGet.');
        }

        if (locale !== undefined) {
            localVarQueryParameters['locale'] = ObjectSerializer.serialize(locale, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'GET',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309GetReturnRecordsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309GetReturnRecordsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to reject a buyer\'s return or refund request.
     * @summary RejectReturn
     * @param returnId The identifier of a specific return request.
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param idempotencyKey The idempotency key is a unique value generated by the client which the server uses to recognize the same request. How you create unique keys is up to you, but we suggest using UUIDs, or another random string with enough entropy to avoid collisions. Idempotency keys can be up to 255 characters long.
     * @param shopCipher 
     * @param RejectReturnRequestBody 
     */
    public async ReturnsReturnIdRejectPost (returnId: string, xTtsAccessToken: string, contentType: string, idempotencyKey?: string, shopCipher?: string, RejectReturnRequestBody?: ReturnRefund202309RejectReturnRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309RejectReturnResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/returns/{return_id}/reject'
            .replace('{' + 'return_id' + '}', encodeURIComponent(String(returnId)));
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'returnId' is not null or undefined
        if (returnId === null || returnId === undefined) {
            throw new Error('Required parameter returnId was null or undefined when calling ReturnsReturnIdRejectPost.');
        }

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ReturnsReturnIdRejectPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ReturnsReturnIdRejectPost.');
        }

        if (idempotencyKey !== undefined) {
            localVarQueryParameters['idempotency_key'] = ObjectSerializer.serialize(idempotencyKey, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(RejectReturnRequestBody, "ReturnRefund202309RejectReturnRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309RejectReturnResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309RejectReturnResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
    /**
     * Use this API to retrieve one or more returns. This API supports filtering returns using query parameters. You can filter returns by create time, update time, return status, or return types. 
     * @summary SearchReturns
     * @param xTtsAccessToken 
     * @param contentType Allowed type: application/json
     * @param sortField The returned results will be sorted by the specified field.  Default: &#x60;create_time&#x60; Possible values: - &#x60;create_time&#x60; - &#x60;update_time&#x60;  Specify the order for sorting the returned results by using the &#x60;sort_order&#x60; parameter.
     * @param sortOrder The sort order for the &#x60;sort_field&#x60; parameter. Default: ASC Possible values: - &#x60;ASC&#x60;: Ascending order - &#x60;DESC&#x60;: Descending order
     * @param pageSize The number of results to be returned per page. Default: 10. Valid range: [10-50].
     * @param pageToken An opaque token used to retrieve the next page of a paginated result set. Retrieve this value from the result of the &#x60;next_page_token&#x60; from a previous response. It is not needed for the first page.
     * @param shopCipher 
     * @param SearchReturnsRequestBody 
     */
    public async ReturnsSearchPost (xTtsAccessToken: string, contentType: string, sortField?: string, sortOrder?: string, pageSize?: string, pageToken?: string, shopCipher?: string, SearchReturnsRequestBody?: ReturnRefund202309SearchReturnsRequestBody, options: {headers: {[name: string]: string}} = {headers: {}}) : Promise<{ response: http.IncomingMessage; body: ReturnRefund202309SearchReturnsResponse;  }> {
        const localVarPath = this.basePath + '/return_refund/202309/returns/search';
        let localVarQueryParameters: any = {};
        let localVarHeaderParams: any = (<any>Object).assign({}, this._defaultHeaders);
        const produces = ['application/json'];
        // give precedence to 'application/json'
        if (produces.indexOf('application/json') >= 0) {
            localVarHeaderParams.Accept = 'application/json';
        } else {
            localVarHeaderParams.Accept = produces.join(',');
        }
        let localVarFormParams: any = {};

        // verify required parameter 'xTtsAccessToken' is not null or undefined
        if (xTtsAccessToken === null || xTtsAccessToken === undefined) {
            throw new Error('Required parameter xTtsAccessToken was null or undefined when calling ReturnsSearchPost.');
        }

        // verify required parameter 'contentType' is not null or undefined
        if (contentType === null || contentType === undefined) {
            throw new Error('Required parameter contentType was null or undefined when calling ReturnsSearchPost.');
        }

        if (sortField !== undefined) {
            localVarQueryParameters['sort_field'] = ObjectSerializer.serialize(sortField, "string");
        }

        if (sortOrder !== undefined) {
            localVarQueryParameters['sort_order'] = ObjectSerializer.serialize(sortOrder, "string");
        }

        if (pageSize !== undefined) {
            localVarQueryParameters['page_size'] = ObjectSerializer.serialize(pageSize, "string");
        }

        if (pageToken !== undefined) {
            localVarQueryParameters['page_token'] = ObjectSerializer.serialize(pageToken, "string");
        }

        if (shopCipher !== undefined) {
            localVarQueryParameters['shop_cipher'] = ObjectSerializer.serialize(shopCipher, "string");
        }

        localVarHeaderParams['x-tts-access-token'] = ObjectSerializer.serialize(xTtsAccessToken, "string");
        localVarHeaderParams['Content-Type'] = ObjectSerializer.serialize(contentType, "string");
        (<any>Object).assign(localVarHeaderParams, options.headers);

        let localVarUseFormData = false;

        let localVarRequestOptions: localVarRequest.Options = {
            method: 'POST',
            qs: localVarQueryParameters,
            headers: localVarHeaderParams,
            uri: localVarPath,
            useQuerystring: this._useQuerystring,
            json: true,
            body: ObjectSerializer.serialize(SearchReturnsRequestBody, "ReturnRefund202309SearchReturnsRequestBody")
        };

        let authenticationPromise = Promise.resolve();
        authenticationPromise = authenticationPromise.then(() => this.authentications.default.applyToRequest(localVarRequestOptions));

        let interceptorPromise = authenticationPromise;
        for (const interceptor of this.interceptors) {
            interceptorPromise = interceptorPromise.then(() => interceptor(localVarRequestOptions));
        }

        return interceptorPromise.then(() => {
            if (Object.keys(localVarFormParams).length) {
                if (localVarUseFormData) {
                    (<any>localVarRequestOptions).formData = localVarFormParams;
                } else {
                    localVarRequestOptions.form = localVarFormParams;
                }
            }
            return new Promise<{ response: http.IncomingMessage; body: ReturnRefund202309SearchReturnsResponse;  }>((resolve, reject) => {
                localVarRequest(localVarRequestOptions, (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        if (response.statusCode && response.statusCode >= 200 && response.statusCode <= 299) {
                            body = ObjectSerializer.deserialize(body, "ReturnRefund202309SearchReturnsResponse");
                            resolve({ response: response, body: body });
                        } else {
                            reject(new HttpError(response, body, response.statusCode));
                        }
                    }
                });
            });
        });
    }
}

export const ReturnRefundV202309ApiOperationNames = {
    CancellationsCancelIdApprovePost: 'CancellationsCancelIdApprovePost',CancellationsCancelIdRejectPost: 'CancellationsCancelIdRejectPost',CancellationsPost: 'CancellationsPost',CancellationsSearchPost: 'CancellationsSearchPost',OrdersOrderIdAftersaleEligibilityGet: 'OrdersOrderIdAftersaleEligibilityGet',RefundsCalculatePost: 'RefundsCalculatePost',RejectReasonsGet: 'RejectReasonsGet',ReturnsPost: 'ReturnsPost',ReturnsReturnIdApprovePost: 'ReturnsReturnIdApprovePost',ReturnsReturnIdRecordsGet: 'ReturnsReturnIdRecordsGet',ReturnsReturnIdRejectPost: 'ReturnsReturnIdRejectPost',ReturnsSearchPost: 'ReturnsSearchPost',
} as const


export type ReturnRefundV202309ApiOperationTypes = {
    CancellationsCancelIdApprovePost: ReturnRefundV202309Api['CancellationsCancelIdApprovePost'];CancellationsCancelIdRejectPost: ReturnRefundV202309Api['CancellationsCancelIdRejectPost'];CancellationsPost: ReturnRefundV202309Api['CancellationsPost'];CancellationsSearchPost: ReturnRefundV202309Api['CancellationsSearchPost'];OrdersOrderIdAftersaleEligibilityGet: ReturnRefundV202309Api['OrdersOrderIdAftersaleEligibilityGet'];RefundsCalculatePost: ReturnRefundV202309Api['RefundsCalculatePost'];RejectReasonsGet: ReturnRefundV202309Api['RejectReasonsGet'];ReturnsPost: ReturnRefundV202309Api['ReturnsPost'];ReturnsReturnIdApprovePost: ReturnRefundV202309Api['ReturnsReturnIdApprovePost'];ReturnsReturnIdRecordsGet: ReturnRefundV202309Api['ReturnsReturnIdRecordsGet'];ReturnsReturnIdRejectPost: ReturnRefundV202309Api['ReturnsReturnIdRejectPost'];ReturnsSearchPost: ReturnRefundV202309Api['ReturnsSearchPost'];
};

