#!/bin/bash

# Exit on any error
set -e

echo "=== Starting deployment process ==="

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "Error: .env.production file not found!"
    exit 1
fi

# Load environment variables from .env.production
export $(cat .env.production | grep -v '^#' | xargs)
echo "NODE_ENV set to: $NODE_ENV"

# Validate required environment variables
if [ -z "$DB_HOST" ] || [ -z "$R2_BUCKET_NAME" ]; then
    echo "Error: Required environment variables not set!"
    echo "DB_HOST: $DB_HOST"
    echo "R2_BUCKET_NAME: $R2_BUCKET_NAME"
    exit 1
fi

# Stop all PM2 processes
echo "Stopping PM2 processes..."
pm2 delete all

# Install dependencies
echo "Installing dependencies..."
npm install

# Build the application
echo "Building application..."
npm run build

# Run database migrations
echo "Running database migrations..."
npm run migration:run

# Start the application with PM2
echo "Starting application with PM2..."
pm2 start ecosystem.config.js --env production

# Save PM2 process list and setup startup script
echo "Saving PM2 configuration..."
pm2 save

# Setup PM2 to start on system boot (run only once)
if ! pm2 startup | grep -q "already"; then
    echo "Setting up PM2 startup script..."
    echo "Note: You may need to run the PM2 startup command manually with sudo"
    pm2 startup
fi

# Show PM2 status
echo "PM2 Status:"
pm2 status

echo "=== Deployment completed successfully ==="
